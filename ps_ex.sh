#!/bin/sh

function ps_ex()
{
	#for pid in `ps -e | grep $1 | awk '{print $1}'` ;
  selfFileName=`basename $0`

  #ps -ef | grep nginx| grep -v $selfFileName|grep -v 'grep'| awk '{print $1,$2,$9}'
  for pid in `ps -ef | grep nginx| grep -v $selfFileName|grep -v 'grep'| awk '{print $2}'` ;
    do
       ls -l /proc/${pid}/exe &>/dev/null
       if [ "$?" == "0" ]
       then
          echo -n "${pid} "
          ls -l /proc/${pid}/exe | awk '{print $10 $11}'
       fi
    done
}

ps_ex $1
