remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1837 ->/root/kinginx/nginx
1838 ->/root/kinginx/nginx
1840 ->/root/kinginx/nginx
1841 ->/root/kinginx/nginx
1842 ->/root/kinginx/nginx
1844 ->/root/kinginx/nginx
1845 ->/root/kinginx/nginx
1848 ->/root/kinginx/nginx
1850 ->/root/kinginx/nginx
1851 ->/root/kinginx/nginx
1852 ->/root/kinginx/nginx
1854 ->/root/kinginx/nginx
1855 ->/root/kinginx/nginx
1856 ->/root/kinginx/nginx
1857 ->/root/kinginx/nginx
1858 ->/root/kinginx/nginx
1859 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2267 ->/root/kinginx/nginx
2269 ->/root/kinginx/nginx
2270 ->/root/kinginx/nginx
2272 ->/root/kinginx/nginx
2276 ->/root/kinginx/nginx
2277 ->/root/kinginx/nginx
2278 ->/root/kinginx/nginx
2280 ->/root/kinginx/nginx
2281 ->/root/kinginx/nginx
2282 ->/root/kinginx/nginx
2283 ->/root/kinginx/nginx
2284 ->/root/kinginx/nginx
2285 ->/root/kinginx/nginx
2286 ->/root/kinginx/nginx
2287 ->/root/kinginx/nginx
2290 ->/root/kinginx/nginx
2292 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2657 ->/root/kinginx/nginx
2659 ->/root/kinginx/nginx
2661 ->/root/kinginx/nginx
2663 ->/root/kinginx/nginx
2666 ->/root/kinginx/nginx
2667 ->/root/kinginx/nginx
2668 ->/root/kinginx/nginx
2670 ->/root/kinginx/nginx
2671 ->/root/kinginx/nginx
2672 ->/root/kinginx/nginx
2673 ->/root/kinginx/nginx
2674 ->/root/kinginx/nginx
2676 ->/root/kinginx/nginx
2677 ->/root/kinginx/nginx
2679 ->/root/kinginx/nginx
2682 ->/root/kinginx/nginx
2686 ->/root/kinginx/nginx
Nginx Stopping failed, There are nginx processes still running.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2657 ->/root/kinginx/nginx
2659 ->/root/kinginx/nginx
2661 ->/root/kinginx/nginx
2663 ->/root/kinginx/nginx
2666 ->/root/kinginx/nginx
2667 ->/root/kinginx/nginx
2668 ->/root/kinginx/nginx
2670 ->/root/kinginx/nginx
2671 ->/root/kinginx/nginx
2672 ->/root/kinginx/nginx
2673 ->/root/kinginx/nginx
2674 ->/root/kinginx/nginx
2676 ->/root/kinginx/nginx
2677 ->/root/kinginx/nginx
2679 ->/root/kinginx/nginx
2682 ->/root/kinginx/nginx
2686 ->/root/kinginx/nginx
Nginx Already Started.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2657 ->/root/kinginx/nginx
2659 ->/root/kinginx/nginx
2661 ->/root/kinginx/nginx
2663 ->/root/kinginx/nginx
2666 ->/root/kinginx/nginx
2667 ->/root/kinginx/nginx
2668 ->/root/kinginx/nginx
2670 ->/root/kinginx/nginx
2671 ->/root/kinginx/nginx
2672 ->/root/kinginx/nginx
2673 ->/root/kinginx/nginx
2674 ->/root/kinginx/nginx
2676 ->/root/kinginx/nginx
2677 ->/root/kinginx/nginx
2679 ->/root/kinginx/nginx
2682 ->/root/kinginx/nginx
2686 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
3421 ->/root/kinginx/nginx
3423 ->/root/kinginx/nginx
3424 ->/root/kinginx/nginx
3427 ->/root/kinginx/nginx
3429 ->/root/kinginx/nginx
3431 ->/root/kinginx/nginx
3432 ->/root/kinginx/nginx
3433 ->/root/kinginx/nginx
3434 ->/root/kinginx/nginx
3436 ->/root/kinginx/nginx
3437 ->/root/kinginx/nginx
3438 ->/root/kinginx/nginx
3439 ->/root/kinginx/nginx
3440 ->/root/kinginx/nginx
3441 ->/root/kinginx/nginx
3442 ->/root/kinginx/nginx
3443 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
7068 ->/root/kinginx/nginx
7070 ->/root/kinginx/nginx
7071 ->/root/kinginx/nginx
7074 ->/root/kinginx/nginx
7077 ->/root/kinginx/nginx
7078 ->/root/kinginx/nginx
7079 ->/root/kinginx/nginx
7081 ->/root/kinginx/nginx
7082 ->/root/kinginx/nginx
7083 ->/root/kinginx/nginx
7084 ->/root/kinginx/nginx
7085 ->/root/kinginx/nginx
7086 ->/root/kinginx/nginx
7087 ->/root/kinginx/nginx
7088 ->/root/kinginx/nginx
7090 ->/root/kinginx/nginx
7091 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
7438 ->/root/kinginx/nginx
7440 ->/root/kinginx/nginx
7441 ->/root/kinginx/nginx
7444 ->/root/kinginx/nginx
7446 ->/root/kinginx/nginx
7448 ->/root/kinginx/nginx
7449 ->/root/kinginx/nginx
7450 ->/root/kinginx/nginx
7452 ->/root/kinginx/nginx
7453 ->/root/kinginx/nginx
7454 ->/root/kinginx/nginx
7455 ->/root/kinginx/nginx
7456 ->/root/kinginx/nginx
7457 ->/root/kinginx/nginx
7458 ->/root/kinginx/nginx
7459 ->/root/kinginx/nginx
7460 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
7858 ->/root/kinginx/nginx
7860 ->/root/kinginx/nginx
7861 ->/root/kinginx/nginx
7864 ->/root/kinginx/nginx
7867 ->/root/kinginx/nginx
7868 ->/root/kinginx/nginx
7869 ->/root/kinginx/nginx
7871 ->/root/kinginx/nginx
7872 ->/root/kinginx/nginx
7873 ->/root/kinginx/nginx
7874 ->/root/kinginx/nginx
7875 ->/root/kinginx/nginx
7876 ->/root/kinginx/nginx
7877 ->/root/kinginx/nginx
7878 ->/root/kinginx/nginx
7880 ->/root/kinginx/nginx
7881 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8411 ->/root/kinginx/nginx
8413 ->/root/kinginx/nginx
8415 ->/root/kinginx/nginx
8417 ->/root/kinginx/nginx
8419 ->/root/kinginx/nginx
8421 ->/root/kinginx/nginx
8422 ->/root/kinginx/nginx
8423 ->/root/kinginx/nginx
8424 ->/root/kinginx/nginx
8426 ->/root/kinginx/nginx
8427 ->/root/kinginx/nginx
8428 ->/root/kinginx/nginx
8429 ->/root/kinginx/nginx
8430 ->/root/kinginx/nginx
8431 ->/root/kinginx/nginx
8432 ->/root/kinginx/nginx
8434 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8818 ->/root/kinginx/nginx
8820 ->/root/kinginx/nginx
8821 ->/root/kinginx/nginx
8823 ->/root/kinginx/nginx
8825 ->/root/kinginx/nginx
8828 ->/root/kinginx/nginx
8829 ->/root/kinginx/nginx
8830 ->/root/kinginx/nginx
8832 ->/root/kinginx/nginx
8833 ->/root/kinginx/nginx
8834 ->/root/kinginx/nginx
8835 ->/root/kinginx/nginx
8836 ->/root/kinginx/nginx
8838 ->/root/kinginx/nginx
8839 ->/root/kinginx/nginx
8841 ->/root/kinginx/nginx
8843 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9223 ->/root/kinginx/nginx
9224 ->/root/kinginx/nginx
9226 ->/root/kinginx/nginx
9227 ->/root/kinginx/nginx
9228 ->/root/kinginx/nginx
9230 ->/root/kinginx/nginx
9232 ->/root/kinginx/nginx
9234 ->/root/kinginx/nginx
9236 ->/root/kinginx/nginx
9237 ->/root/kinginx/nginx
9239 ->/root/kinginx/nginx
9240 ->/root/kinginx/nginx
9241 ->/root/kinginx/nginx
9242 ->/root/kinginx/nginx
9243 ->/root/kinginx/nginx
9244 ->/root/kinginx/nginx
9245 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9622 ->/root/kinginx/nginx
9624 ->/root/kinginx/nginx
9625 ->/root/kinginx/nginx
9627 ->/root/kinginx/nginx
9629 ->/root/kinginx/nginx
9631 ->/root/kinginx/nginx
9633 ->/root/kinginx/nginx
9634 ->/root/kinginx/nginx
9635 ->/root/kinginx/nginx
9636 ->/root/kinginx/nginx
9638 ->/root/kinginx/nginx
9639 ->/root/kinginx/nginx
9640 ->/root/kinginx/nginx
9641 ->/root/kinginx/nginx
9642 ->/root/kinginx/nginx
9643 ->/root/kinginx/nginx
9644 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9976 ->/root/kinginx/nginx
9980 ->/root/kinginx/nginx
9982 ->/root/kinginx/nginx
9983 ->/root/kinginx/nginx
9985 ->/root/kinginx/nginx
9986 ->/root/kinginx/nginx
9987 ->/root/kinginx/nginx
9989 ->/root/kinginx/nginx
9990 ->/root/kinginx/nginx
9991 ->/root/kinginx/nginx
9992 ->/root/kinginx/nginx
9993 ->/root/kinginx/nginx
9994 ->/root/kinginx/nginx
9995 ->/root/kinginx/nginx
9996 ->/root/kinginx/nginx
9997 ->/root/kinginx/nginx
9999 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10581 ->/root/kinginx/nginx
10583 ->/root/kinginx/nginx
10586 ->/root/kinginx/nginx
10588 ->/root/kinginx/nginx
10590 ->/root/kinginx/nginx
10591 ->/root/kinginx/nginx
10592 ->/root/kinginx/nginx
10593 ->/root/kinginx/nginx
10595 ->/root/kinginx/nginx
10596 ->/root/kinginx/nginx
10597 ->/root/kinginx/nginx
10598 ->/root/kinginx/nginx
10599 ->/root/kinginx/nginx
10600 ->/root/kinginx/nginx
10601 ->/root/kinginx/nginx
10602 ->/root/kinginx/nginx
10604 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10977 ->/root/kinginx/nginx
10979 ->/root/kinginx/nginx
10980 ->/root/kinginx/nginx
10982 ->/root/kinginx/nginx
10984 ->/root/kinginx/nginx
10986 ->/root/kinginx/nginx
10988 ->/root/kinginx/nginx
10989 ->/root/kinginx/nginx
10990 ->/root/kinginx/nginx
10992 ->/root/kinginx/nginx
10993 ->/root/kinginx/nginx
10994 ->/root/kinginx/nginx
10995 ->/root/kinginx/nginx
10996 ->/root/kinginx/nginx
10997 ->/root/kinginx/nginx
10998 ->/root/kinginx/nginx
10999 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11339 ->/root/kinginx/nginx
11340 ->/root/kinginx/nginx
11341 ->/root/kinginx/nginx
11343 ->/root/kinginx/nginx
11344 ->/root/kinginx/nginx
11345 ->/root/kinginx/nginx
11347 ->/root/kinginx/nginx
11350 ->/root/kinginx/nginx
11352 ->/root/kinginx/nginx
11353 ->/root/kinginx/nginx
11354 ->/root/kinginx/nginx
11355 ->/root/kinginx/nginx
11357 ->/root/kinginx/nginx
11358 ->/root/kinginx/nginx
11359 ->/root/kinginx/nginx
11360 ->/root/kinginx/nginx
11361 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11803 ->/root/kinginx/nginx
11805 ->/root/kinginx/nginx
11807 ->/root/kinginx/nginx
11809 ->/root/kinginx/nginx
11811 ->/root/kinginx/nginx
11813 ->/root/kinginx/nginx
11814 ->/root/kinginx/nginx
11815 ->/root/kinginx/nginx
11817 ->/root/kinginx/nginx
11818 ->/root/kinginx/nginx
11819 ->/root/kinginx/nginx
11820 ->/root/kinginx/nginx
11821 ->/root/kinginx/nginx
11822 ->/root/kinginx/nginx
11823 ->/root/kinginx/nginx
11825 ->/root/kinginx/nginx
11826 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12201 ->/root/kinginx/nginx
12202 ->/root/kinginx/nginx
12203 ->/root/kinginx/nginx
12205 ->/root/kinginx/nginx
12206 ->/root/kinginx/nginx
12210 ->/root/kinginx/nginx
12211 ->/root/kinginx/nginx
12213 ->/root/kinginx/nginx
12214 ->/root/kinginx/nginx
12215 ->/root/kinginx/nginx
12217 ->/root/kinginx/nginx
12218 ->/root/kinginx/nginx
12219 ->/root/kinginx/nginx
12220 ->/root/kinginx/nginx
12221 ->/root/kinginx/nginx
12222 ->/root/kinginx/nginx
12223 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14114 ->/root/kinginx/nginx
14116 ->/root/kinginx/nginx
14117 ->/root/kinginx/nginx
14119 ->/root/kinginx/nginx
14122 ->/root/kinginx/nginx
14124 ->/root/kinginx/nginx
14125 ->/root/kinginx/nginx
14126 ->/root/kinginx/nginx
14128 ->/root/kinginx/nginx
14129 ->/root/kinginx/nginx
14130 ->/root/kinginx/nginx
14131 ->/root/kinginx/nginx
14132 ->/root/kinginx/nginx
14133 ->/root/kinginx/nginx
14134 ->/root/kinginx/nginx
14135 ->/root/kinginx/nginx
14137 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27901 ->/root/kinginx/nginx
27902 ->/root/kinginx/nginx
27904 ->/root/kinginx/nginx
27905 ->/root/kinginx/nginx
27906 ->/root/kinginx/nginx
27908 ->/root/kinginx/nginx
27910 ->/root/kinginx/nginx
27912 ->/root/kinginx/nginx
27914 ->/root/kinginx/nginx
27915 ->/root/kinginx/nginx
27916 ->/root/kinginx/nginx
27918 ->/root/kinginx/nginx
27919 ->/root/kinginx/nginx
27920 ->/root/kinginx/nginx
27921 ->/root/kinginx/nginx
27922 ->/root/kinginx/nginx
27923 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14347 ->/root/kinginx/nginx
14349 ->/root/kinginx/nginx
14350 ->/root/kinginx/nginx
14352 ->/root/kinginx/nginx
14355 ->/root/kinginx/nginx
14357 ->/root/kinginx/nginx
14358 ->/root/kinginx/nginx
14359 ->/root/kinginx/nginx
14361 ->/root/kinginx/nginx
14362 ->/root/kinginx/nginx
14363 ->/root/kinginx/nginx
14364 ->/root/kinginx/nginx
14366 ->/root/kinginx/nginx
14367 ->/root/kinginx/nginx
14369 ->/root/kinginx/nginx
14371 ->/root/kinginx/nginx
14373 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1464 ->/root/kinginx/nginx
1465 ->/root/kinginx/nginx
1467 ->/root/kinginx/nginx
1469 ->/root/kinginx/nginx
1472 ->/root/kinginx/nginx
1473 ->/root/kinginx/nginx
1475 ->/root/kinginx/nginx
1476 ->/root/kinginx/nginx
1477 ->/root/kinginx/nginx
1478 ->/root/kinginx/nginx
1480 ->/root/kinginx/nginx
1481 ->/root/kinginx/nginx
1482 ->/root/kinginx/nginx
1483 ->/root/kinginx/nginx
1484 ->/root/kinginx/nginx
1485 ->/root/kinginx/nginx
1486 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26577 ->/root/kinginx/nginx
26579 ->/root/kinginx/nginx
26580 ->/root/kinginx/nginx
26582 ->/root/kinginx/nginx
26585 ->/root/kinginx/nginx
26587 ->/root/kinginx/nginx
26589 ->/root/kinginx/nginx
26590 ->/root/kinginx/nginx
26591 ->/root/kinginx/nginx
26592 ->/root/kinginx/nginx
26593 ->/root/kinginx/nginx
26594 ->/root/kinginx/nginx
26595 ->/root/kinginx/nginx
26596 ->/root/kinginx/nginx
26597 ->/root/kinginx/nginx
26599 ->/root/kinginx/nginx
26600 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11882 ->/root/kinginx/nginx
11883 ->/root/kinginx/nginx
11884 ->/root/kinginx/nginx
11886 ->/root/kinginx/nginx
11887 ->/root/kinginx/nginx
11889 ->/root/kinginx/nginx
11891 ->/root/kinginx/nginx
11893 ->/root/kinginx/nginx
11895 ->/root/kinginx/nginx
11896 ->/root/kinginx/nginx
11897 ->/root/kinginx/nginx
11899 ->/root/kinginx/nginx
11900 ->/root/kinginx/nginx
11901 ->/root/kinginx/nginx
11902 ->/root/kinginx/nginx
11903 ->/root/kinginx/nginx
11904 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1717 ->/root/kinginx/nginx
1719 ->/root/kinginx/nginx
1721 ->/root/kinginx/nginx
1723 ->/root/kinginx/nginx
1728 ->/root/kinginx/nginx
1729 ->/root/kinginx/nginx
1730 ->/root/kinginx/nginx
1732 ->/root/kinginx/nginx
1733 ->/root/kinginx/nginx
1734 ->/root/kinginx/nginx
1735 ->/root/kinginx/nginx
1737 ->/root/kinginx/nginx
1738 ->/root/kinginx/nginx
1739 ->/root/kinginx/nginx
1741 ->/root/kinginx/nginx
1743 ->/root/kinginx/nginx
1746 ->/root/kinginx/nginx
Nginx Stopping failed, There are nginx processes still running.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
1717 ->/root/kinginx/nginx
Nginx Already Started.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
1717 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
24810 ->/root/kinginx/nginx
24812 ->/root/kinginx/nginx
24813 ->/root/kinginx/nginx
24815 ->/root/kinginx/nginx
24818 ->/root/kinginx/nginx
24820 ->/root/kinginx/nginx
24821 ->/root/kinginx/nginx
24822 ->/root/kinginx/nginx
24824 ->/root/kinginx/nginx
24825 ->/root/kinginx/nginx
24826 ->/root/kinginx/nginx
24827 ->/root/kinginx/nginx
24828 ->/root/kinginx/nginx
24829 ->/root/kinginx/nginx
24830 ->/root/kinginx/nginx
24832 ->/root/kinginx/nginx
24833 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16537 ->/root/kinginx/nginx
16538 ->/root/kinginx/nginx
16539 ->/root/kinginx/nginx
16540 ->/root/kinginx/nginx
16542 ->/root/kinginx/nginx
16543 ->/root/kinginx/nginx
16544 ->/root/kinginx/nginx
16546 ->/root/kinginx/nginx
16548 ->/root/kinginx/nginx
16551 ->/root/kinginx/nginx
16552 ->/root/kinginx/nginx
16553 ->/root/kinginx/nginx
16555 ->/root/kinginx/nginx
16556 ->/root/kinginx/nginx
16557 ->/root/kinginx/nginx
16558 ->/root/kinginx/nginx
16559 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27052 ->/root/kinginx/nginx
27054 ->/root/kinginx/nginx
27056 ->/root/kinginx/nginx
27058 ->/root/kinginx/nginx
27060 ->/root/kinginx/nginx
27062 ->/root/kinginx/nginx
27063 ->/root/kinginx/nginx
27064 ->/root/kinginx/nginx
27066 ->/root/kinginx/nginx
27067 ->/root/kinginx/nginx
27068 ->/root/kinginx/nginx
27069 ->/root/kinginx/nginx
27070 ->/root/kinginx/nginx
27071 ->/root/kinginx/nginx
27072 ->/root/kinginx/nginx
27073 ->/root/kinginx/nginx
27075 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14084 ->/root/kinginx/nginx
14085 ->/root/kinginx/nginx
14086 ->/root/kinginx/nginx
14088 ->/root/kinginx/nginx
14090 ->/root/kinginx/nginx
14093 ->/root/kinginx/nginx
14094 ->/root/kinginx/nginx
14096 ->/root/kinginx/nginx
14097 ->/root/kinginx/nginx
14098 ->/root/kinginx/nginx
14100 ->/root/kinginx/nginx
14101 ->/root/kinginx/nginx
14102 ->/root/kinginx/nginx
14103 ->/root/kinginx/nginx
14104 ->/root/kinginx/nginx
14106 ->/root/kinginx/nginx
14111 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9283 ->/root/kinginx/nginx
9284 ->/root/kinginx/nginx
9285 ->/root/kinginx/nginx
9287 ->/root/kinginx/nginx
9288 ->/root/kinginx/nginx
9290 ->/root/kinginx/nginx
9292 ->/root/kinginx/nginx
9294 ->/root/kinginx/nginx
9296 ->/root/kinginx/nginx
9297 ->/root/kinginx/nginx
9298 ->/root/kinginx/nginx
9300 ->/root/kinginx/nginx
9301 ->/root/kinginx/nginx
9302 ->/root/kinginx/nginx
9303 ->/root/kinginx/nginx
9304 ->/root/kinginx/nginx
9305 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
29974 ->/root/kinginx/nginx
29976 ->/root/kinginx/nginx
29978 ->/root/kinginx/nginx
29980 ->/root/kinginx/nginx
29983 ->/root/kinginx/nginx
29984 ->/root/kinginx/nginx
29985 ->/root/kinginx/nginx
29986 ->/root/kinginx/nginx
29988 ->/root/kinginx/nginx
29989 ->/root/kinginx/nginx
29990 ->/root/kinginx/nginx
29991 ->/root/kinginx/nginx
29992 ->/root/kinginx/nginx
29993 ->/root/kinginx/nginx
29994 ->/root/kinginx/nginx
29996 ->/root/kinginx/nginx
29997 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
25487 ->/root/kinginx/nginx
25488 ->/root/kinginx/nginx
25489 ->/root/kinginx/nginx
25491 ->/root/kinginx/nginx
25492 ->/root/kinginx/nginx
25493 ->/root/kinginx/nginx
25495 ->/root/kinginx/nginx
25497 ->/root/kinginx/nginx
25499 ->/root/kinginx/nginx
25501 ->/root/kinginx/nginx
25502 ->/root/kinginx/nginx
25504 ->/root/kinginx/nginx
25505 ->/root/kinginx/nginx
25506 ->/root/kinginx/nginx
25507 ->/root/kinginx/nginx
25508 ->/root/kinginx/nginx
25509 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14821 ->/root/kinginx/nginx
14823 ->/root/kinginx/nginx
14824 ->/root/kinginx/nginx
14826 ->/root/kinginx/nginx
14829 ->/root/kinginx/nginx
14831 ->/root/kinginx/nginx
14832 ->/root/kinginx/nginx
14833 ->/root/kinginx/nginx
14835 ->/root/kinginx/nginx
14836 ->/root/kinginx/nginx
14837 ->/root/kinginx/nginx
14838 ->/root/kinginx/nginx
14839 ->/root/kinginx/nginx
14840 ->/root/kinginx/nginx
14841 ->/root/kinginx/nginx
14843 ->/root/kinginx/nginx
14844 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
3406 ->/root/kinginx/nginx
3408 ->/root/kinginx/nginx
3410 ->/root/kinginx/nginx
3412 ->/root/kinginx/nginx
3414 ->/root/kinginx/nginx
3416 ->/root/kinginx/nginx
3417 ->/root/kinginx/nginx
3418 ->/root/kinginx/nginx
3420 ->/root/kinginx/nginx
3421 ->/root/kinginx/nginx
3422 ->/root/kinginx/nginx
3423 ->/root/kinginx/nginx
3424 ->/root/kinginx/nginx
3425 ->/root/kinginx/nginx
3426 ->/root/kinginx/nginx
3428 ->/root/kinginx/nginx
3429 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22109 ->/root/kinginx/nginx
22111 ->/root/kinginx/nginx
22112 ->/root/kinginx/nginx
22114 ->/root/kinginx/nginx
22116 ->/root/kinginx/nginx
22118 ->/root/kinginx/nginx
22120 ->/root/kinginx/nginx
22121 ->/root/kinginx/nginx
22122 ->/root/kinginx/nginx
22123 ->/root/kinginx/nginx
22125 ->/root/kinginx/nginx
22126 ->/root/kinginx/nginx
22127 ->/root/kinginx/nginx
22128 ->/root/kinginx/nginx
22129 ->/root/kinginx/nginx
22130 ->/root/kinginx/nginx
22131 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22520 ->/root/kinginx/nginx
22522 ->/root/kinginx/nginx
22523 ->/root/kinginx/nginx
22525 ->/root/kinginx/nginx
22529 ->/root/kinginx/nginx
22530 ->/root/kinginx/nginx
22531 ->/root/kinginx/nginx
22533 ->/root/kinginx/nginx
22534 ->/root/kinginx/nginx
22535 ->/root/kinginx/nginx
22536 ->/root/kinginx/nginx
22537 ->/root/kinginx/nginx
22538 ->/root/kinginx/nginx
22539 ->/root/kinginx/nginx
22540 ->/root/kinginx/nginx
22542 ->/root/kinginx/nginx
22543 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16313 ->/root/kinginx/nginx
16315 ->/root/kinginx/nginx
16316 ->/root/kinginx/nginx
16318 ->/root/kinginx/nginx
16320 ->/root/kinginx/nginx
16323 ->/root/kinginx/nginx
16324 ->/root/kinginx/nginx
16325 ->/root/kinginx/nginx
16327 ->/root/kinginx/nginx
16328 ->/root/kinginx/nginx
16329 ->/root/kinginx/nginx
16330 ->/root/kinginx/nginx
16331 ->/root/kinginx/nginx
16332 ->/root/kinginx/nginx
16333 ->/root/kinginx/nginx
16335 ->/root/kinginx/nginx
16336 ->/root/kinginx/nginx
Nginx Stopping failed, There are nginx processes still running.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
16313 ->/root/kinginx/nginx
Nginx Already Started.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
16313 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10241 ->/root/kinginx/nginx
10243 ->/root/kinginx/nginx
10244 ->/root/kinginx/nginx
10245 ->/root/kinginx/nginx
10247 ->/root/kinginx/nginx
10250 ->/root/kinginx/nginx
10252 ->/root/kinginx/nginx
10253 ->/root/kinginx/nginx
10254 ->/root/kinginx/nginx
10256 ->/root/kinginx/nginx
10257 ->/root/kinginx/nginx
10258 ->/root/kinginx/nginx
10259 ->/root/kinginx/nginx
10260 ->/root/kinginx/nginx
10261 ->/root/kinginx/nginx
10262 ->/root/kinginx/nginx
10264 ->/root/kinginx/nginx
Nginx Stopping failed, There are nginx processes still running.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21424 ->/root/kinginx/nginx
21425 ->/root/kinginx/nginx
21426 ->/root/kinginx/nginx
21428 ->/root/kinginx/nginx
21431 ->/root/kinginx/nginx
21433 ->/root/kinginx/nginx
21434 ->/root/kinginx/nginx
21436 ->/root/kinginx/nginx
21437 ->/root/kinginx/nginx
21438 ->/root/kinginx/nginx
21440 ->/root/kinginx/nginx
21441 ->/root/kinginx/nginx
21442 ->/root/kinginx/nginx
21443 ->/root/kinginx/nginx
21444 ->/root/kinginx/nginx
21445 ->/root/kinginx/nginx
21446 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10272 ->/root/kinginx/nginx
10273 ->/root/kinginx/nginx
10275 ->/root/kinginx/nginx
10276 ->/root/kinginx/nginx
10278 ->/root/kinginx/nginx
10280 ->/root/kinginx/nginx
10282 ->/root/kinginx/nginx
10284 ->/root/kinginx/nginx
10285 ->/root/kinginx/nginx
10286 ->/root/kinginx/nginx
10288 ->/root/kinginx/nginx
10289 ->/root/kinginx/nginx
10290 ->/root/kinginx/nginx
10291 ->/root/kinginx/nginx
10292 ->/root/kinginx/nginx
10293 ->/root/kinginx/nginx
10294 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
15204 ->/root/kinginx/nginx
15205 ->/root/kinginx/nginx
15206 ->/root/kinginx/nginx
15207 ->/root/kinginx/nginx
15209 ->/root/kinginx/nginx
15210 ->/root/kinginx/nginx
15211 ->/root/kinginx/nginx
15213 ->/root/kinginx/nginx
15215 ->/root/kinginx/nginx
15217 ->/root/kinginx/nginx
15219 ->/root/kinginx/nginx
15220 ->/root/kinginx/nginx
15222 ->/root/kinginx/nginx
15223 ->/root/kinginx/nginx
15224 ->/root/kinginx/nginx
15225 ->/root/kinginx/nginx
15226 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1369 ->/root/kinginx/nginx
1371 ->/root/kinginx/nginx
1372 ->/root/kinginx/nginx
1374 ->/root/kinginx/nginx
1377 ->/root/kinginx/nginx
1379 ->/root/kinginx/nginx
1380 ->/root/kinginx/nginx
1382 ->/root/kinginx/nginx
1383 ->/root/kinginx/nginx
1384 ->/root/kinginx/nginx
1385 ->/root/kinginx/nginx
1386 ->/root/kinginx/nginx
1387 ->/root/kinginx/nginx
1388 ->/root/kinginx/nginx
1389 ->/root/kinginx/nginx
1390 ->/root/kinginx/nginx
1392 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12043 ->/root/kinginx/nginx
12045 ->/root/kinginx/nginx
12046 ->/root/kinginx/nginx
12048 ->/root/kinginx/nginx
12050 ->/root/kinginx/nginx
12052 ->/root/kinginx/nginx
12054 ->/root/kinginx/nginx
12056 ->/root/kinginx/nginx
12057 ->/root/kinginx/nginx
12058 ->/root/kinginx/nginx
12059 ->/root/kinginx/nginx
12060 ->/root/kinginx/nginx
12061 ->/root/kinginx/nginx
12062 ->/root/kinginx/nginx
12063 ->/root/kinginx/nginx
12065 ->/root/kinginx/nginx
12066 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
25287 ->/root/kinginx/nginx
25289 ->/root/kinginx/nginx
25291 ->/root/kinginx/nginx
25293 ->/root/kinginx/nginx
25295 ->/root/kinginx/nginx
25297 ->/root/kinginx/nginx
25298 ->/root/kinginx/nginx
25300 ->/root/kinginx/nginx
25301 ->/root/kinginx/nginx
25302 ->/root/kinginx/nginx
25303 ->/root/kinginx/nginx
25304 ->/root/kinginx/nginx
25305 ->/root/kinginx/nginx
25306 ->/root/kinginx/nginx
25308 ->/root/kinginx/nginx
25309 ->/root/kinginx/nginx
25311 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
25691 ->/root/kinginx/nginx
25693 ->/root/kinginx/nginx
25694 ->/root/kinginx/nginx
25696 ->/root/kinginx/nginx
25699 ->/root/kinginx/nginx
25701 ->/root/kinginx/nginx
25702 ->/root/kinginx/nginx
25703 ->/root/kinginx/nginx
25705 ->/root/kinginx/nginx
25706 ->/root/kinginx/nginx
25707 ->/root/kinginx/nginx
25708 ->/root/kinginx/nginx
25709 ->/root/kinginx/nginx
25710 ->/root/kinginx/nginx
25711 ->/root/kinginx/nginx
25713 ->/root/kinginx/nginx
25714 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1389 ->/root/kinginx/nginx
1391 ->/root/kinginx/nginx
1395 ->/root/kinginx/nginx
1396 ->/root/kinginx/nginx
1398 ->/root/kinginx/nginx
1399 ->/root/kinginx/nginx
1401 ->/root/kinginx/nginx
1402 ->/root/kinginx/nginx
1403 ->/root/kinginx/nginx
1404 ->/root/kinginx/nginx
1405 ->/root/kinginx/nginx
1406 ->/root/kinginx/nginx
1407 ->/root/kinginx/nginx
1408 ->/root/kinginx/nginx
1410 ->/root/kinginx/nginx
1412 ->/root/kinginx/nginx
1416 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
32025 ->/root/kinginx/nginx
32027 ->/root/kinginx/nginx
32028 ->/root/kinginx/nginx
32030 ->/root/kinginx/nginx
32032 ->/root/kinginx/nginx
32034 ->/root/kinginx/nginx
32036 ->/root/kinginx/nginx
32037 ->/root/kinginx/nginx
32038 ->/root/kinginx/nginx
32040 ->/root/kinginx/nginx
32041 ->/root/kinginx/nginx
32042 ->/root/kinginx/nginx
32043 ->/root/kinginx/nginx
32044 ->/root/kinginx/nginx
32045 ->/root/kinginx/nginx
32046 ->/root/kinginx/nginx
32048 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
23398 ->/root/kinginx/nginx
23400 ->/root/kinginx/nginx
23401 ->/root/kinginx/nginx
23403 ->/root/kinginx/nginx
23406 ->/root/kinginx/nginx
23408 ->/root/kinginx/nginx
23409 ->/root/kinginx/nginx
23410 ->/root/kinginx/nginx
23412 ->/root/kinginx/nginx
23413 ->/root/kinginx/nginx
23414 ->/root/kinginx/nginx
23415 ->/root/kinginx/nginx
23416 ->/root/kinginx/nginx
23417 ->/root/kinginx/nginx
23418 ->/root/kinginx/nginx
23420 ->/root/kinginx/nginx
23421 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9104 ->/root/kinginx/nginx
9105 ->/root/kinginx/nginx
9106 ->/root/kinginx/nginx
9107 ->/root/kinginx/nginx
9109 ->/root/kinginx/nginx
9110 ->/root/kinginx/nginx
9111 ->/root/kinginx/nginx
9114 ->/root/kinginx/nginx
9116 ->/root/kinginx/nginx
9118 ->/root/kinginx/nginx
9119 ->/root/kinginx/nginx
9120 ->/root/kinginx/nginx
9122 ->/root/kinginx/nginx
9123 ->/root/kinginx/nginx
9124 ->/root/kinginx/nginx
9125 ->/root/kinginx/nginx
9126 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
32034 ->/root/kinginx/nginx
32036 ->/root/kinginx/nginx
32037 ->/root/kinginx/nginx
32039 ->/root/kinginx/nginx
32041 ->/root/kinginx/nginx
32043 ->/root/kinginx/nginx
32045 ->/root/kinginx/nginx
32046 ->/root/kinginx/nginx
32047 ->/root/kinginx/nginx
32049 ->/root/kinginx/nginx
32050 ->/root/kinginx/nginx
32051 ->/root/kinginx/nginx
32052 ->/root/kinginx/nginx
32053 ->/root/kinginx/nginx
32054 ->/root/kinginx/nginx
32055 ->/root/kinginx/nginx
32057 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
32641 ->/root/kinginx/nginx
32642 ->/root/kinginx/nginx
32643 ->/root/kinginx/nginx
32645 ->/root/kinginx/nginx
32646 ->/root/kinginx/nginx
32647 ->/root/kinginx/nginx
32650 ->/root/kinginx/nginx
32652 ->/root/kinginx/nginx
32653 ->/root/kinginx/nginx
32655 ->/root/kinginx/nginx
32656 ->/root/kinginx/nginx
32657 ->/root/kinginx/nginx
32659 ->/root/kinginx/nginx
32660 ->/root/kinginx/nginx
32661 ->/root/kinginx/nginx
32662 ->/root/kinginx/nginx
32663 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21105 ->/root/kinginx/nginx
21107 ->/root/kinginx/nginx
21108 ->/root/kinginx/nginx
21110 ->/root/kinginx/nginx
21112 ->/root/kinginx/nginx
21115 ->/root/kinginx/nginx
21116 ->/root/kinginx/nginx
21117 ->/root/kinginx/nginx
21118 ->/root/kinginx/nginx
21120 ->/root/kinginx/nginx
21121 ->/root/kinginx/nginx
21122 ->/root/kinginx/nginx
21123 ->/root/kinginx/nginx
21124 ->/root/kinginx/nginx
21125 ->/root/kinginx/nginx
21126 ->/root/kinginx/nginx
21128 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26555 ->/root/kinginx/nginx
26556 ->/root/kinginx/nginx
26558 ->/root/kinginx/nginx
26559 ->/root/kinginx/nginx
26560 ->/root/kinginx/nginx
26562 ->/root/kinginx/nginx
26564 ->/root/kinginx/nginx
26566 ->/root/kinginx/nginx
26568 ->/root/kinginx/nginx
26569 ->/root/kinginx/nginx
26570 ->/root/kinginx/nginx
26572 ->/root/kinginx/nginx
26573 ->/root/kinginx/nginx
26574 ->/root/kinginx/nginx
26575 ->/root/kinginx/nginx
26576 ->/root/kinginx/nginx
26577 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10539 ->/root/kinginx/nginx
10540 ->/root/kinginx/nginx
10541 ->/root/kinginx/nginx
10543 ->/root/kinginx/nginx
10544 ->/root/kinginx/nginx
10546 ->/root/kinginx/nginx
10548 ->/root/kinginx/nginx
10550 ->/root/kinginx/nginx
10552 ->/root/kinginx/nginx
10553 ->/root/kinginx/nginx
10555 ->/root/kinginx/nginx
10556 ->/root/kinginx/nginx
10557 ->/root/kinginx/nginx
10558 ->/root/kinginx/nginx
10560 ->/root/kinginx/nginx
10561 ->/root/kinginx/nginx
10564 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2919 ->/root/kinginx/nginx
2922 ->/root/kinginx/nginx
2923 ->/root/kinginx/nginx
2925 ->/root/kinginx/nginx
2926 ->/root/kinginx/nginx
2929 ->/root/kinginx/nginx
2931 ->/root/kinginx/nginx
2932 ->/root/kinginx/nginx
2934 ->/root/kinginx/nginx
2935 ->/root/kinginx/nginx
2936 ->/root/kinginx/nginx
2937 ->/root/kinginx/nginx
2938 ->/root/kinginx/nginx
2939 ->/root/kinginx/nginx
2940 ->/root/kinginx/nginx
2942 ->/root/kinginx/nginx
2943 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22764 ->/root/kinginx/nginx
22766 ->/root/kinginx/nginx
22767 ->/root/kinginx/nginx
22769 ->/root/kinginx/nginx
22771 ->/root/kinginx/nginx
22773 ->/root/kinginx/nginx
22775 ->/root/kinginx/nginx
22776 ->/root/kinginx/nginx
22777 ->/root/kinginx/nginx
22778 ->/root/kinginx/nginx
22780 ->/root/kinginx/nginx
22781 ->/root/kinginx/nginx
22782 ->/root/kinginx/nginx
22783 ->/root/kinginx/nginx
22784 ->/root/kinginx/nginx
22785 ->/root/kinginx/nginx
22786 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10431 ->/root/kinginx/nginx
10433 ->/root/kinginx/nginx
10434 ->/root/kinginx/nginx
10436 ->/root/kinginx/nginx
10438 ->/root/kinginx/nginx
10441 ->/root/kinginx/nginx
10442 ->/root/kinginx/nginx
10443 ->/root/kinginx/nginx
10445 ->/root/kinginx/nginx
10446 ->/root/kinginx/nginx
10447 ->/root/kinginx/nginx
10448 ->/root/kinginx/nginx
10449 ->/root/kinginx/nginx
10450 ->/root/kinginx/nginx
10451 ->/root/kinginx/nginx
10453 ->/root/kinginx/nginx
10454 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
30082 ->/root/kinginx/nginx
30084 ->/root/kinginx/nginx
30085 ->/root/kinginx/nginx
30087 ->/root/kinginx/nginx
30090 ->/root/kinginx/nginx
30092 ->/root/kinginx/nginx
30093 ->/root/kinginx/nginx
30094 ->/root/kinginx/nginx
30096 ->/root/kinginx/nginx
30097 ->/root/kinginx/nginx
30098 ->/root/kinginx/nginx
30099 ->/root/kinginx/nginx
30100 ->/root/kinginx/nginx
30101 ->/root/kinginx/nginx
30102 ->/root/kinginx/nginx
30104 ->/root/kinginx/nginx
30105 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22379 ->/root/kinginx/nginx
22380 ->/root/kinginx/nginx
22381 ->/root/kinginx/nginx
22382 ->/root/kinginx/nginx
22383 ->/root/kinginx/nginx
22385 ->/root/kinginx/nginx
22386 ->/root/kinginx/nginx
22387 ->/root/kinginx/nginx
22389 ->/root/kinginx/nginx
22392 ->/root/kinginx/nginx
22393 ->/root/kinginx/nginx
22395 ->/root/kinginx/nginx
22396 ->/root/kinginx/nginx
22397 ->/root/kinginx/nginx
22399 ->/root/kinginx/nginx
22400 ->/root/kinginx/nginx
22401 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8931 ->/root/kinginx/nginx
8933 ->/root/kinginx/nginx
8934 ->/root/kinginx/nginx
8936 ->/root/kinginx/nginx
8939 ->/root/kinginx/nginx
8941 ->/root/kinginx/nginx
8942 ->/root/kinginx/nginx
8943 ->/root/kinginx/nginx
8945 ->/root/kinginx/nginx
8946 ->/root/kinginx/nginx
8947 ->/root/kinginx/nginx
8949 ->/root/kinginx/nginx
8950 ->/root/kinginx/nginx
8951 ->/root/kinginx/nginx
8953 ->/root/kinginx/nginx
8955 ->/root/kinginx/nginx
8957 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
5007 ->/root/kinginx/nginx
5009 ->/root/kinginx/nginx
5010 ->/root/kinginx/nginx
5012 ->/root/kinginx/nginx
5014 ->/root/kinginx/nginx
5016 ->/root/kinginx/nginx
5018 ->/root/kinginx/nginx
5019 ->/root/kinginx/nginx
5020 ->/root/kinginx/nginx
5022 ->/root/kinginx/nginx
5023 ->/root/kinginx/nginx
5024 ->/root/kinginx/nginx
5025 ->/root/kinginx/nginx
5026 ->/root/kinginx/nginx
5027 ->/root/kinginx/nginx
5028 ->/root/kinginx/nginx
5029 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1994 ->/root/kinginx/nginx
1996 ->/root/kinginx/nginx
1997 ->/root/kinginx/nginx
1999 ->/root/kinginx/nginx
2001 ->/root/kinginx/nginx
2004 ->/root/kinginx/nginx
2005 ->/root/kinginx/nginx
2006 ->/root/kinginx/nginx
2007 ->/root/kinginx/nginx
2009 ->/root/kinginx/nginx
2010 ->/root/kinginx/nginx
2011 ->/root/kinginx/nginx
2012 ->/root/kinginx/nginx
2013 ->/root/kinginx/nginx
2014 ->/root/kinginx/nginx
2016 ->/root/kinginx/nginx
2017 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6631 ->/root/kinginx/nginx
6632 ->/root/kinginx/nginx
6633 ->/root/kinginx/nginx
6635 ->/root/kinginx/nginx
6636 ->/root/kinginx/nginx
6637 ->/root/kinginx/nginx
6639 ->/root/kinginx/nginx
6641 ->/root/kinginx/nginx
6643 ->/root/kinginx/nginx
6645 ->/root/kinginx/nginx
6646 ->/root/kinginx/nginx
6647 ->/root/kinginx/nginx
6649 ->/root/kinginx/nginx
6650 ->/root/kinginx/nginx
6651 ->/root/kinginx/nginx
6652 ->/root/kinginx/nginx
6653 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9533 ->/root/kinginx/nginx
9535 ->/root/kinginx/nginx
9537 ->/root/kinginx/nginx
9539 ->/root/kinginx/nginx
9541 ->/root/kinginx/nginx
9543 ->/root/kinginx/nginx
9544 ->/root/kinginx/nginx
9545 ->/root/kinginx/nginx
9547 ->/root/kinginx/nginx
9548 ->/root/kinginx/nginx
9549 ->/root/kinginx/nginx
9550 ->/root/kinginx/nginx
9551 ->/root/kinginx/nginx
9552 ->/root/kinginx/nginx
9553 ->/root/kinginx/nginx
9554 ->/root/kinginx/nginx
9556 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9916 ->/root/kinginx/nginx
9918 ->/root/kinginx/nginx
9919 ->/root/kinginx/nginx
9921 ->/root/kinginx/nginx
9924 ->/root/kinginx/nginx
9926 ->/root/kinginx/nginx
9927 ->/root/kinginx/nginx
9929 ->/root/kinginx/nginx
9930 ->/root/kinginx/nginx
9931 ->/root/kinginx/nginx
9932 ->/root/kinginx/nginx
9933 ->/root/kinginx/nginx
9934 ->/root/kinginx/nginx
9935 ->/root/kinginx/nginx
9936 ->/root/kinginx/nginx
9937 ->/root/kinginx/nginx
9939 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
32308 ->/root/kinginx/nginx
32309 ->/root/kinginx/nginx
32310 ->/root/kinginx/nginx
32312 ->/root/kinginx/nginx
32314 ->/root/kinginx/nginx
32318 ->/root/kinginx/nginx
32319 ->/root/kinginx/nginx
32320 ->/root/kinginx/nginx
32321 ->/root/kinginx/nginx
32323 ->/root/kinginx/nginx
32324 ->/root/kinginx/nginx
32325 ->/root/kinginx/nginx
32326 ->/root/kinginx/nginx
32328 ->/root/kinginx/nginx
32329 ->/root/kinginx/nginx
32331 ->/root/kinginx/nginx
32333 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19338 ->/root/kinginx/nginx
19341 ->/root/kinginx/nginx
19344 ->/root/kinginx/nginx
19345 ->/root/kinginx/nginx
19347 ->/root/kinginx/nginx
19348 ->/root/kinginx/nginx
19349 ->/root/kinginx/nginx
19350 ->/root/kinginx/nginx
19352 ->/root/kinginx/nginx
19353 ->/root/kinginx/nginx
19354 ->/root/kinginx/nginx
19355 ->/root/kinginx/nginx
19356 ->/root/kinginx/nginx
19357 ->/root/kinginx/nginx
19358 ->/root/kinginx/nginx
19359 ->/root/kinginx/nginx
19361 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13703 ->/root/kinginx/nginx
13704 ->/root/kinginx/nginx
13705 ->/root/kinginx/nginx
13707 ->/root/kinginx/nginx
13708 ->/root/kinginx/nginx
13709 ->/root/kinginx/nginx
13711 ->/root/kinginx/nginx
13713 ->/root/kinginx/nginx
13715 ->/root/kinginx/nginx
13717 ->/root/kinginx/nginx
13718 ->/root/kinginx/nginx
13720 ->/root/kinginx/nginx
13721 ->/root/kinginx/nginx
13722 ->/root/kinginx/nginx
13724 ->/root/kinginx/nginx
13728 ->/root/kinginx/nginx
13731 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
5515 ->/root/kinginx/nginx
5516 ->/root/kinginx/nginx
5517 ->/root/kinginx/nginx
5519 ->/root/kinginx/nginx
5520 ->/root/kinginx/nginx
5521 ->/root/kinginx/nginx
5523 ->/root/kinginx/nginx
5525 ->/root/kinginx/nginx
5527 ->/root/kinginx/nginx
5529 ->/root/kinginx/nginx
5530 ->/root/kinginx/nginx
5531 ->/root/kinginx/nginx
5533 ->/root/kinginx/nginx
5534 ->/root/kinginx/nginx
5535 ->/root/kinginx/nginx
5536 ->/root/kinginx/nginx
5537 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22868 ->/root/kinginx/nginx
22870 ->/root/kinginx/nginx
22871 ->/root/kinginx/nginx
22873 ->/root/kinginx/nginx
22875 ->/root/kinginx/nginx
22877 ->/root/kinginx/nginx
22879 ->/root/kinginx/nginx
22880 ->/root/kinginx/nginx
22881 ->/root/kinginx/nginx
22883 ->/root/kinginx/nginx
22884 ->/root/kinginx/nginx
22885 ->/root/kinginx/nginx
22886 ->/root/kinginx/nginx
22887 ->/root/kinginx/nginx
22888 ->/root/kinginx/nginx
22889 ->/root/kinginx/nginx
22890 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
18437 ->/root/kinginx/nginx
18438 ->/root/kinginx/nginx
18439 ->/root/kinginx/nginx
18441 ->/root/kinginx/nginx
18442 ->/root/kinginx/nginx
18443 ->/root/kinginx/nginx
18445 ->/root/kinginx/nginx
18448 ->/root/kinginx/nginx
18450 ->/root/kinginx/nginx
18451 ->/root/kinginx/nginx
18452 ->/root/kinginx/nginx
18454 ->/root/kinginx/nginx
18455 ->/root/kinginx/nginx
18456 ->/root/kinginx/nginx
18457 ->/root/kinginx/nginx
18458 ->/root/kinginx/nginx
18459 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2068 ->/root/kinginx/nginx
2070 ->/root/kinginx/nginx
2071 ->/root/kinginx/nginx
2073 ->/root/kinginx/nginx
2075 ->/root/kinginx/nginx
2077 ->/root/kinginx/nginx
2079 ->/root/kinginx/nginx
2080 ->/root/kinginx/nginx
2081 ->/root/kinginx/nginx
2083 ->/root/kinginx/nginx
2084 ->/root/kinginx/nginx
2085 ->/root/kinginx/nginx
2086 ->/root/kinginx/nginx
2087 ->/root/kinginx/nginx
2088 ->/root/kinginx/nginx
2089 ->/root/kinginx/nginx
2091 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27156 ->/root/kinginx/nginx
27157 ->/root/kinginx/nginx
27158 ->/root/kinginx/nginx
27160 ->/root/kinginx/nginx
27161 ->/root/kinginx/nginx
27163 ->/root/kinginx/nginx
27166 ->/root/kinginx/nginx
27167 ->/root/kinginx/nginx
27169 ->/root/kinginx/nginx
27170 ->/root/kinginx/nginx
27171 ->/root/kinginx/nginx
27173 ->/root/kinginx/nginx
27174 ->/root/kinginx/nginx
27175 ->/root/kinginx/nginx
27176 ->/root/kinginx/nginx
27177 ->/root/kinginx/nginx
27178 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1188 ->/root/kinginx/nginx
1190 ->/root/kinginx/nginx
1191 ->/root/kinginx/nginx
1193 ->/root/kinginx/nginx
1197 ->/root/kinginx/nginx
1198 ->/root/kinginx/nginx
1199 ->/root/kinginx/nginx
1200 ->/root/kinginx/nginx
1202 ->/root/kinginx/nginx
1203 ->/root/kinginx/nginx
1204 ->/root/kinginx/nginx
1205 ->/root/kinginx/nginx
1206 ->/root/kinginx/nginx
1207 ->/root/kinginx/nginx
1208 ->/root/kinginx/nginx
1209 ->/root/kinginx/nginx
1210 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2101 ->/root/kinginx/nginx
2103 ->/root/kinginx/nginx
2105 ->/root/kinginx/nginx
2107 ->/root/kinginx/nginx
2110 ->/root/kinginx/nginx
2111 ->/root/kinginx/nginx
2112 ->/root/kinginx/nginx
2114 ->/root/kinginx/nginx
2115 ->/root/kinginx/nginx
2116 ->/root/kinginx/nginx
2117 ->/root/kinginx/nginx
2118 ->/root/kinginx/nginx
2119 ->/root/kinginx/nginx
2120 ->/root/kinginx/nginx
2121 ->/root/kinginx/nginx
2122 ->/root/kinginx/nginx
2123 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22064 ->/root/kinginx/nginx
22066 ->/root/kinginx/nginx
22067 ->/root/kinginx/nginx
22069 ->/root/kinginx/nginx
22071 ->/root/kinginx/nginx
22074 ->/root/kinginx/nginx
22075 ->/root/kinginx/nginx
22076 ->/root/kinginx/nginx
22078 ->/root/kinginx/nginx
22079 ->/root/kinginx/nginx
22080 ->/root/kinginx/nginx
22081 ->/root/kinginx/nginx
22082 ->/root/kinginx/nginx
22083 ->/root/kinginx/nginx
22084 ->/root/kinginx/nginx
22085 ->/root/kinginx/nginx
22086 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9414 ->/root/kinginx/nginx
9415 ->/root/kinginx/nginx
9416 ->/root/kinginx/nginx
9418 ->/root/kinginx/nginx
9419 ->/root/kinginx/nginx
9420 ->/root/kinginx/nginx
9422 ->/root/kinginx/nginx
9424 ->/root/kinginx/nginx
9426 ->/root/kinginx/nginx
9428 ->/root/kinginx/nginx
9429 ->/root/kinginx/nginx
9430 ->/root/kinginx/nginx
9432 ->/root/kinginx/nginx
9433 ->/root/kinginx/nginx
9434 ->/root/kinginx/nginx
9435 ->/root/kinginx/nginx
9436 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2322 ->/root/kinginx/nginx
2324 ->/root/kinginx/nginx
2325 ->/root/kinginx/nginx
2327 ->/root/kinginx/nginx
2329 ->/root/kinginx/nginx
2331 ->/root/kinginx/nginx
2333 ->/root/kinginx/nginx
2334 ->/root/kinginx/nginx
2335 ->/root/kinginx/nginx
2337 ->/root/kinginx/nginx
2338 ->/root/kinginx/nginx
2339 ->/root/kinginx/nginx
2340 ->/root/kinginx/nginx
2341 ->/root/kinginx/nginx
2342 ->/root/kinginx/nginx
2343 ->/root/kinginx/nginx
2345 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21547 ->/root/kinginx/nginx
21548 ->/root/kinginx/nginx
21549 ->/root/kinginx/nginx
21551 ->/root/kinginx/nginx
21552 ->/root/kinginx/nginx
21554 ->/root/kinginx/nginx
21556 ->/root/kinginx/nginx
21558 ->/root/kinginx/nginx
21560 ->/root/kinginx/nginx
21561 ->/root/kinginx/nginx
21562 ->/root/kinginx/nginx
21564 ->/root/kinginx/nginx
21565 ->/root/kinginx/nginx
21566 ->/root/kinginx/nginx
21567 ->/root/kinginx/nginx
21568 ->/root/kinginx/nginx
21569 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8096 ->/root/kinginx/nginx
8098 ->/root/kinginx/nginx
8099 ->/root/kinginx/nginx
8101 ->/root/kinginx/nginx
8103 ->/root/kinginx/nginx
8105 ->/root/kinginx/nginx
8107 ->/root/kinginx/nginx
8109 ->/root/kinginx/nginx
8110 ->/root/kinginx/nginx
8111 ->/root/kinginx/nginx
8112 ->/root/kinginx/nginx
8113 ->/root/kinginx/nginx
8114 ->/root/kinginx/nginx
8115 ->/root/kinginx/nginx
8116 ->/root/kinginx/nginx
8118 ->/root/kinginx/nginx
8119 ->/root/kinginx/nginx
Nginx Stopping failed, There are nginx processes still running.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
8096 ->/root/kinginx/nginx
Nginx Already Started.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:1
8096 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
18784 ->/root/kinginx/nginx
18785 ->/root/kinginx/nginx
18786 ->/root/kinginx/nginx
18788 ->/root/kinginx/nginx
18789 ->/root/kinginx/nginx
18790 ->/root/kinginx/nginx
18792 ->/root/kinginx/nginx
18794 ->/root/kinginx/nginx
18796 ->/root/kinginx/nginx
18798 ->/root/kinginx/nginx
18799 ->/root/kinginx/nginx
18800 ->/root/kinginx/nginx
18802 ->/root/kinginx/nginx
18803 ->/root/kinginx/nginx
18804 ->/root/kinginx/nginx
18805 ->/root/kinginx/nginx
18806 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19194 ->/root/kinginx/nginx
19196 ->/root/kinginx/nginx
19197 ->/root/kinginx/nginx
19199 ->/root/kinginx/nginx
19202 ->/root/kinginx/nginx
19204 ->/root/kinginx/nginx
19205 ->/root/kinginx/nginx
19206 ->/root/kinginx/nginx
19208 ->/root/kinginx/nginx
19209 ->/root/kinginx/nginx
19210 ->/root/kinginx/nginx
19211 ->/root/kinginx/nginx
19212 ->/root/kinginx/nginx
19213 ->/root/kinginx/nginx
19214 ->/root/kinginx/nginx
19215 ->/root/kinginx/nginx
19216 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19580 ->/root/kinginx/nginx
19582 ->/root/kinginx/nginx
19583 ->/root/kinginx/nginx
19585 ->/root/kinginx/nginx
19588 ->/root/kinginx/nginx
19589 ->/root/kinginx/nginx
19591 ->/root/kinginx/nginx
19593 ->/root/kinginx/nginx
19594 ->/root/kinginx/nginx
19595 ->/root/kinginx/nginx
19596 ->/root/kinginx/nginx
19597 ->/root/kinginx/nginx
19598 ->/root/kinginx/nginx
19599 ->/root/kinginx/nginx
19600 ->/root/kinginx/nginx
19601 ->/root/kinginx/nginx
19603 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19944 ->/root/kinginx/nginx
19946 ->/root/kinginx/nginx
19947 ->/root/kinginx/nginx
19949 ->/root/kinginx/nginx
19951 ->/root/kinginx/nginx
19953 ->/root/kinginx/nginx
19955 ->/root/kinginx/nginx
19956 ->/root/kinginx/nginx
19958 ->/root/kinginx/nginx
19959 ->/root/kinginx/nginx
19960 ->/root/kinginx/nginx
19961 ->/root/kinginx/nginx
19962 ->/root/kinginx/nginx
19963 ->/root/kinginx/nginx
19964 ->/root/kinginx/nginx
19966 ->/root/kinginx/nginx
19967 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13099 ->/root/kinginx/nginx
13100 ->/root/kinginx/nginx
13101 ->/root/kinginx/nginx
13102 ->/root/kinginx/nginx
13104 ->/root/kinginx/nginx
13106 ->/root/kinginx/nginx
13109 ->/root/kinginx/nginx
13110 ->/root/kinginx/nginx
13112 ->/root/kinginx/nginx
13113 ->/root/kinginx/nginx
13114 ->/root/kinginx/nginx
13116 ->/root/kinginx/nginx
13117 ->/root/kinginx/nginx
13118 ->/root/kinginx/nginx
13119 ->/root/kinginx/nginx
13120 ->/root/kinginx/nginx
13121 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2381 ->/root/kinginx/nginx
2382 ->/root/kinginx/nginx
2383 ->/root/kinginx/nginx
2385 ->/root/kinginx/nginx
2386 ->/root/kinginx/nginx
2387 ->/root/kinginx/nginx
2389 ->/root/kinginx/nginx
2391 ->/root/kinginx/nginx
2393 ->/root/kinginx/nginx
2395 ->/root/kinginx/nginx
2396 ->/root/kinginx/nginx
2397 ->/root/kinginx/nginx
2399 ->/root/kinginx/nginx
2400 ->/root/kinginx/nginx
2401 ->/root/kinginx/nginx
2402 ->/root/kinginx/nginx
2403 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31715 ->/root/kinginx/nginx
31717 ->/root/kinginx/nginx
31718 ->/root/kinginx/nginx
31720 ->/root/kinginx/nginx
31722 ->/root/kinginx/nginx
31724 ->/root/kinginx/nginx
31726 ->/root/kinginx/nginx
31727 ->/root/kinginx/nginx
31728 ->/root/kinginx/nginx
31730 ->/root/kinginx/nginx
31731 ->/root/kinginx/nginx
31732 ->/root/kinginx/nginx
31733 ->/root/kinginx/nginx
31734 ->/root/kinginx/nginx
31735 ->/root/kinginx/nginx
31736 ->/root/kinginx/nginx
31738 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27375 ->/root/kinginx/nginx
27376 ->/root/kinginx/nginx
27377 ->/root/kinginx/nginx
27379 ->/root/kinginx/nginx
27380 ->/root/kinginx/nginx
27381 ->/root/kinginx/nginx
27383 ->/root/kinginx/nginx
27385 ->/root/kinginx/nginx
27387 ->/root/kinginx/nginx
27389 ->/root/kinginx/nginx
27390 ->/root/kinginx/nginx
27392 ->/root/kinginx/nginx
27393 ->/root/kinginx/nginx
27394 ->/root/kinginx/nginx
27395 ->/root/kinginx/nginx
27396 ->/root/kinginx/nginx
27397 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12029 ->/root/kinginx/nginx
12031 ->/root/kinginx/nginx
12033 ->/root/kinginx/nginx
12035 ->/root/kinginx/nginx
12037 ->/root/kinginx/nginx
12039 ->/root/kinginx/nginx
12040 ->/root/kinginx/nginx
12042 ->/root/kinginx/nginx
12043 ->/root/kinginx/nginx
12044 ->/root/kinginx/nginx
12045 ->/root/kinginx/nginx
12046 ->/root/kinginx/nginx
12047 ->/root/kinginx/nginx
12048 ->/root/kinginx/nginx
12050 ->/root/kinginx/nginx
12051 ->/root/kinginx/nginx
12053 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16452 ->/root/kinginx/nginx
16453 ->/root/kinginx/nginx
16454 ->/root/kinginx/nginx
16456 ->/root/kinginx/nginx
16457 ->/root/kinginx/nginx
16458 ->/root/kinginx/nginx
16460 ->/root/kinginx/nginx
16463 ->/root/kinginx/nginx
16465 ->/root/kinginx/nginx
16466 ->/root/kinginx/nginx
16467 ->/root/kinginx/nginx
16469 ->/root/kinginx/nginx
16470 ->/root/kinginx/nginx
16471 ->/root/kinginx/nginx
16472 ->/root/kinginx/nginx
16473 ->/root/kinginx/nginx
16474 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19881 ->/root/kinginx/nginx
19883 ->/root/kinginx/nginx
19885 ->/root/kinginx/nginx
19887 ->/root/kinginx/nginx
19889 ->/root/kinginx/nginx
19891 ->/root/kinginx/nginx
19892 ->/root/kinginx/nginx
19893 ->/root/kinginx/nginx
19894 ->/root/kinginx/nginx
19896 ->/root/kinginx/nginx
19897 ->/root/kinginx/nginx
19898 ->/root/kinginx/nginx
19899 ->/root/kinginx/nginx
19900 ->/root/kinginx/nginx
19901 ->/root/kinginx/nginx
19902 ->/root/kinginx/nginx
19903 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9984 ->/root/kinginx/nginx
9986 ->/root/kinginx/nginx
9987 ->/root/kinginx/nginx
9989 ->/root/kinginx/nginx
9990 ->/root/kinginx/nginx
9992 ->/root/kinginx/nginx
9995 ->/root/kinginx/nginx
9996 ->/root/kinginx/nginx
9998 ->/root/kinginx/nginx
9999 ->/root/kinginx/nginx
10000 ->/root/kinginx/nginx
10001 ->/root/kinginx/nginx
10002 ->/root/kinginx/nginx
10003 ->/root/kinginx/nginx
10004 ->/root/kinginx/nginx
10006 ->/root/kinginx/nginx
10007 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27086 ->/root/kinginx/nginx
27087 ->/root/kinginx/nginx
27088 ->/root/kinginx/nginx
27090 ->/root/kinginx/nginx
27091 ->/root/kinginx/nginx
27092 ->/root/kinginx/nginx
27094 ->/root/kinginx/nginx
27096 ->/root/kinginx/nginx
27098 ->/root/kinginx/nginx
27100 ->/root/kinginx/nginx
27101 ->/root/kinginx/nginx
27102 ->/root/kinginx/nginx
27104 ->/root/kinginx/nginx
27105 ->/root/kinginx/nginx
27106 ->/root/kinginx/nginx
27107 ->/root/kinginx/nginx
27108 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22495 ->/root/kinginx/nginx
22496 ->/root/kinginx/nginx
22497 ->/root/kinginx/nginx
22499 ->/root/kinginx/nginx
22500 ->/root/kinginx/nginx
22503 ->/root/kinginx/nginx
22506 ->/root/kinginx/nginx
22507 ->/root/kinginx/nginx
22508 ->/root/kinginx/nginx
22510 ->/root/kinginx/nginx
22511 ->/root/kinginx/nginx
22512 ->/root/kinginx/nginx
22513 ->/root/kinginx/nginx
22514 ->/root/kinginx/nginx
22515 ->/root/kinginx/nginx
22516 ->/root/kinginx/nginx
22517 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31237 ->/root/kinginx/nginx
31238 ->/root/kinginx/nginx
31239 ->/root/kinginx/nginx
31240 ->/root/kinginx/nginx
31242 ->/root/kinginx/nginx
31243 ->/root/kinginx/nginx
31245 ->/root/kinginx/nginx
31247 ->/root/kinginx/nginx
31248 ->/root/kinginx/nginx
31251 ->/root/kinginx/nginx
31252 ->/root/kinginx/nginx
31253 ->/root/kinginx/nginx
31254 ->/root/kinginx/nginx
31256 ->/root/kinginx/nginx
31257 ->/root/kinginx/nginx
31258 ->/root/kinginx/nginx
31259 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
756 ->/root/kinginx/nginx
758 ->/root/kinginx/nginx
760 ->/root/kinginx/nginx
762 ->/root/kinginx/nginx
764 ->/root/kinginx/nginx
766 ->/root/kinginx/nginx
767 ->/root/kinginx/nginx
768 ->/root/kinginx/nginx
770 ->/root/kinginx/nginx
771 ->/root/kinginx/nginx
772 ->/root/kinginx/nginx
773 ->/root/kinginx/nginx
774 ->/root/kinginx/nginx
775 ->/root/kinginx/nginx
776 ->/root/kinginx/nginx
777 ->/root/kinginx/nginx
779 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12051 ->/root/kinginx/nginx
12056 ->/root/kinginx/nginx
12058 ->/root/kinginx/nginx
12061 ->/root/kinginx/nginx
12063 ->/root/kinginx/nginx
12064 ->/root/kinginx/nginx
12066 ->/root/kinginx/nginx
12067 ->/root/kinginx/nginx
12069 ->/root/kinginx/nginx
12070 ->/root/kinginx/nginx
12071 ->/root/kinginx/nginx
12072 ->/root/kinginx/nginx
12073 ->/root/kinginx/nginx
12074 ->/root/kinginx/nginx
12075 ->/root/kinginx/nginx
12076 ->/root/kinginx/nginx
12077 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22047 ->/root/kinginx/nginx
22048 ->/root/kinginx/nginx
22049 ->/root/kinginx/nginx
22051 ->/root/kinginx/nginx
22052 ->/root/kinginx/nginx
22054 ->/root/kinginx/nginx
22056 ->/root/kinginx/nginx
22057 ->/root/kinginx/nginx
22060 ->/root/kinginx/nginx
22061 ->/root/kinginx/nginx
22062 ->/root/kinginx/nginx
22064 ->/root/kinginx/nginx
22065 ->/root/kinginx/nginx
22066 ->/root/kinginx/nginx
22067 ->/root/kinginx/nginx
22068 ->/root/kinginx/nginx
22069 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21199 ->/root/kinginx/nginx
21201 ->/root/kinginx/nginx
21203 ->/root/kinginx/nginx
21205 ->/root/kinginx/nginx
21208 ->/root/kinginx/nginx
21209 ->/root/kinginx/nginx
21210 ->/root/kinginx/nginx
21212 ->/root/kinginx/nginx
21213 ->/root/kinginx/nginx
21214 ->/root/kinginx/nginx
21215 ->/root/kinginx/nginx
21216 ->/root/kinginx/nginx
21218 ->/root/kinginx/nginx
21219 ->/root/kinginx/nginx
21221 ->/root/kinginx/nginx
21223 ->/root/kinginx/nginx
21225 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
20016 ->/root/kinginx/nginx
20017 ->/root/kinginx/nginx
20018 ->/root/kinginx/nginx
20020 ->/root/kinginx/nginx
20021 ->/root/kinginx/nginx
20023 ->/root/kinginx/nginx
20025 ->/root/kinginx/nginx
20027 ->/root/kinginx/nginx
20029 ->/root/kinginx/nginx
20030 ->/root/kinginx/nginx
20031 ->/root/kinginx/nginx
20033 ->/root/kinginx/nginx
20034 ->/root/kinginx/nginx
20035 ->/root/kinginx/nginx
20036 ->/root/kinginx/nginx
20037 ->/root/kinginx/nginx
20038 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16471 ->/root/kinginx/nginx
16472 ->/root/kinginx/nginx
16474 ->/root/kinginx/nginx
16475 ->/root/kinginx/nginx
16477 ->/root/kinginx/nginx
16479 ->/root/kinginx/nginx
16481 ->/root/kinginx/nginx
16483 ->/root/kinginx/nginx
16485 ->/root/kinginx/nginx
16486 ->/root/kinginx/nginx
16487 ->/root/kinginx/nginx
16488 ->/root/kinginx/nginx
16489 ->/root/kinginx/nginx
16490 ->/root/kinginx/nginx
16491 ->/root/kinginx/nginx
16492 ->/root/kinginx/nginx
16494 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10532 ->/root/kinginx/nginx
10533 ->/root/kinginx/nginx
10534 ->/root/kinginx/nginx
10536 ->/root/kinginx/nginx
10537 ->/root/kinginx/nginx
10538 ->/root/kinginx/nginx
10540 ->/root/kinginx/nginx
10542 ->/root/kinginx/nginx
10544 ->/root/kinginx/nginx
10546 ->/root/kinginx/nginx
10547 ->/root/kinginx/nginx
10549 ->/root/kinginx/nginx
10550 ->/root/kinginx/nginx
10551 ->/root/kinginx/nginx
10552 ->/root/kinginx/nginx
10553 ->/root/kinginx/nginx
10554 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9903 ->/root/kinginx/nginx
9904 ->/root/kinginx/nginx
9906 ->/root/kinginx/nginx
9907 ->/root/kinginx/nginx
9909 ->/root/kinginx/nginx
9911 ->/root/kinginx/nginx
9914 ->/root/kinginx/nginx
9915 ->/root/kinginx/nginx
9916 ->/root/kinginx/nginx
9918 ->/root/kinginx/nginx
9919 ->/root/kinginx/nginx
9920 ->/root/kinginx/nginx
9921 ->/root/kinginx/nginx
9922 ->/root/kinginx/nginx
9923 ->/root/kinginx/nginx
9924 ->/root/kinginx/nginx
9926 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
5547 ->/root/kinginx/nginx
5548 ->/root/kinginx/nginx
5549 ->/root/kinginx/nginx
5551 ->/root/kinginx/nginx
5552 ->/root/kinginx/nginx
5553 ->/root/kinginx/nginx
5555 ->/root/kinginx/nginx
5557 ->/root/kinginx/nginx
5559 ->/root/kinginx/nginx
5561 ->/root/kinginx/nginx
5562 ->/root/kinginx/nginx
5563 ->/root/kinginx/nginx
5564 ->/root/kinginx/nginx
5566 ->/root/kinginx/nginx
5567 ->/root/kinginx/nginx
5568 ->/root/kinginx/nginx
5569 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1805 ->/root/kinginx/nginx
1807 ->/root/kinginx/nginx
1808 ->/root/kinginx/nginx
1811 ->/root/kinginx/nginx
1813 ->/root/kinginx/nginx
1815 ->/root/kinginx/nginx
1816 ->/root/kinginx/nginx
1817 ->/root/kinginx/nginx
1818 ->/root/kinginx/nginx
1820 ->/root/kinginx/nginx
1821 ->/root/kinginx/nginx
1822 ->/root/kinginx/nginx
1823 ->/root/kinginx/nginx
1824 ->/root/kinginx/nginx
1825 ->/root/kinginx/nginx
1826 ->/root/kinginx/nginx
1828 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
23638 ->/root/kinginx/nginx
23639 ->/root/kinginx/nginx
23640 ->/root/kinginx/nginx
23641 ->/root/kinginx/nginx
23643 ->/root/kinginx/nginx
23644 ->/root/kinginx/nginx
23646 ->/root/kinginx/nginx
23648 ->/root/kinginx/nginx
23650 ->/root/kinginx/nginx
23652 ->/root/kinginx/nginx
23653 ->/root/kinginx/nginx
23654 ->/root/kinginx/nginx
23656 ->/root/kinginx/nginx
23657 ->/root/kinginx/nginx
23658 ->/root/kinginx/nginx
23659 ->/root/kinginx/nginx
23660 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11723 ->/root/kinginx/nginx
11724 ->/root/kinginx/nginx
11726 ->/root/kinginx/nginx
11729 ->/root/kinginx/nginx
11732 ->/root/kinginx/nginx
11733 ->/root/kinginx/nginx
11734 ->/root/kinginx/nginx
11735 ->/root/kinginx/nginx
11737 ->/root/kinginx/nginx
11738 ->/root/kinginx/nginx
11739 ->/root/kinginx/nginx
11740 ->/root/kinginx/nginx
11741 ->/root/kinginx/nginx
11742 ->/root/kinginx/nginx
11743 ->/root/kinginx/nginx
11744 ->/root/kinginx/nginx
11746 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
18410 ->/root/kinginx/nginx
18411 ->/root/kinginx/nginx
18412 ->/root/kinginx/nginx
18414 ->/root/kinginx/nginx
18415 ->/root/kinginx/nginx
18417 ->/root/kinginx/nginx
18419 ->/root/kinginx/nginx
18421 ->/root/kinginx/nginx
18423 ->/root/kinginx/nginx
18424 ->/root/kinginx/nginx
18426 ->/root/kinginx/nginx
18427 ->/root/kinginx/nginx
18428 ->/root/kinginx/nginx
18429 ->/root/kinginx/nginx
18430 ->/root/kinginx/nginx
18431 ->/root/kinginx/nginx
18433 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
18999 ->/root/kinginx/nginx
19001 ->/root/kinginx/nginx
19002 ->/root/kinginx/nginx
19004 ->/root/kinginx/nginx
19006 ->/root/kinginx/nginx
19008 ->/root/kinginx/nginx
19010 ->/root/kinginx/nginx
19011 ->/root/kinginx/nginx
19012 ->/root/kinginx/nginx
19013 ->/root/kinginx/nginx
19015 ->/root/kinginx/nginx
19016 ->/root/kinginx/nginx
19017 ->/root/kinginx/nginx
19018 ->/root/kinginx/nginx
19019 ->/root/kinginx/nginx
19020 ->/root/kinginx/nginx
19021 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14716 ->/root/kinginx/nginx
14718 ->/root/kinginx/nginx
14719 ->/root/kinginx/nginx
14722 ->/root/kinginx/nginx
14725 ->/root/kinginx/nginx
14726 ->/root/kinginx/nginx
14727 ->/root/kinginx/nginx
14729 ->/root/kinginx/nginx
14730 ->/root/kinginx/nginx
14731 ->/root/kinginx/nginx
14732 ->/root/kinginx/nginx
14733 ->/root/kinginx/nginx
14734 ->/root/kinginx/nginx
14735 ->/root/kinginx/nginx
14736 ->/root/kinginx/nginx
14738 ->/root/kinginx/nginx
14739 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26118 ->/root/kinginx/nginx
26119 ->/root/kinginx/nginx
26120 ->/root/kinginx/nginx
26121 ->/root/kinginx/nginx
26123 ->/root/kinginx/nginx
26124 ->/root/kinginx/nginx
26126 ->/root/kinginx/nginx
26129 ->/root/kinginx/nginx
26130 ->/root/kinginx/nginx
26132 ->/root/kinginx/nginx
26133 ->/root/kinginx/nginx
26134 ->/root/kinginx/nginx
26136 ->/root/kinginx/nginx
26137 ->/root/kinginx/nginx
26138 ->/root/kinginx/nginx
26139 ->/root/kinginx/nginx
26140 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26660 ->/root/kinginx/nginx
26662 ->/root/kinginx/nginx
26663 ->/root/kinginx/nginx
26665 ->/root/kinginx/nginx
26668 ->/root/kinginx/nginx
26670 ->/root/kinginx/nginx
26671 ->/root/kinginx/nginx
26673 ->/root/kinginx/nginx
26674 ->/root/kinginx/nginx
26675 ->/root/kinginx/nginx
26676 ->/root/kinginx/nginx
26677 ->/root/kinginx/nginx
26678 ->/root/kinginx/nginx
26679 ->/root/kinginx/nginx
26680 ->/root/kinginx/nginx
26682 ->/root/kinginx/nginx
26683 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
20348 ->/root/kinginx/nginx
20350 ->/root/kinginx/nginx
20353 ->/root/kinginx/nginx
20355 ->/root/kinginx/nginx
20356 ->/root/kinginx/nginx
20357 ->/root/kinginx/nginx
20358 ->/root/kinginx/nginx
20360 ->/root/kinginx/nginx
20361 ->/root/kinginx/nginx
20362 ->/root/kinginx/nginx
20364 ->/root/kinginx/nginx
20365 ->/root/kinginx/nginx
20366 ->/root/kinginx/nginx
20367 ->/root/kinginx/nginx
20368 ->/root/kinginx/nginx
20369 ->/root/kinginx/nginx
20370 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10131 ->/root/kinginx/nginx
10132 ->/root/kinginx/nginx
10133 ->/root/kinginx/nginx
10134 ->/root/kinginx/nginx
10136 ->/root/kinginx/nginx
10137 ->/root/kinginx/nginx
10138 ->/root/kinginx/nginx
10140 ->/root/kinginx/nginx
10142 ->/root/kinginx/nginx
10144 ->/root/kinginx/nginx
10146 ->/root/kinginx/nginx
10147 ->/root/kinginx/nginx
10148 ->/root/kinginx/nginx
10150 ->/root/kinginx/nginx
10151 ->/root/kinginx/nginx
10152 ->/root/kinginx/nginx
10153 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21988 ->/root/kinginx/nginx
21990 ->/root/kinginx/nginx
21991 ->/root/kinginx/nginx
21993 ->/root/kinginx/nginx
21995 ->/root/kinginx/nginx
21998 ->/root/kinginx/nginx
21999 ->/root/kinginx/nginx
22000 ->/root/kinginx/nginx
22002 ->/root/kinginx/nginx
22003 ->/root/kinginx/nginx
22004 ->/root/kinginx/nginx
22005 ->/root/kinginx/nginx
22006 ->/root/kinginx/nginx
22007 ->/root/kinginx/nginx
22008 ->/root/kinginx/nginx
22009 ->/root/kinginx/nginx
22011 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31427 ->/root/kinginx/nginx
31428 ->/root/kinginx/nginx
31429 ->/root/kinginx/nginx
31431 ->/root/kinginx/nginx
31432 ->/root/kinginx/nginx
31433 ->/root/kinginx/nginx
31435 ->/root/kinginx/nginx
31438 ->/root/kinginx/nginx
31439 ->/root/kinginx/nginx
31441 ->/root/kinginx/nginx
31442 ->/root/kinginx/nginx
31444 ->/root/kinginx/nginx
31445 ->/root/kinginx/nginx
31446 ->/root/kinginx/nginx
31447 ->/root/kinginx/nginx
31449 ->/root/kinginx/nginx
31450 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19399 ->/root/kinginx/nginx
19401 ->/root/kinginx/nginx
19402 ->/root/kinginx/nginx
19404 ->/root/kinginx/nginx
19407 ->/root/kinginx/nginx
19409 ->/root/kinginx/nginx
19410 ->/root/kinginx/nginx
19412 ->/root/kinginx/nginx
19413 ->/root/kinginx/nginx
19414 ->/root/kinginx/nginx
19415 ->/root/kinginx/nginx
19416 ->/root/kinginx/nginx
19417 ->/root/kinginx/nginx
19418 ->/root/kinginx/nginx
19419 ->/root/kinginx/nginx
19421 ->/root/kinginx/nginx
19422 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
30996 ->/root/kinginx/nginx
30997 ->/root/kinginx/nginx
30998 ->/root/kinginx/nginx
31000 ->/root/kinginx/nginx
31001 ->/root/kinginx/nginx
31004 ->/root/kinginx/nginx
31006 ->/root/kinginx/nginx
31007 ->/root/kinginx/nginx
31009 ->/root/kinginx/nginx
31010 ->/root/kinginx/nginx
31011 ->/root/kinginx/nginx
31013 ->/root/kinginx/nginx
31014 ->/root/kinginx/nginx
31015 ->/root/kinginx/nginx
31016 ->/root/kinginx/nginx
31017 ->/root/kinginx/nginx
31018 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14421 ->/root/kinginx/nginx
14422 ->/root/kinginx/nginx
14423 ->/root/kinginx/nginx
14425 ->/root/kinginx/nginx
14426 ->/root/kinginx/nginx
14428 ->/root/kinginx/nginx
14430 ->/root/kinginx/nginx
14432 ->/root/kinginx/nginx
14434 ->/root/kinginx/nginx
14436 ->/root/kinginx/nginx
14437 ->/root/kinginx/nginx
14438 ->/root/kinginx/nginx
14439 ->/root/kinginx/nginx
14440 ->/root/kinginx/nginx
14441 ->/root/kinginx/nginx
14442 ->/root/kinginx/nginx
14443 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
24639 ->/root/kinginx/nginx
24640 ->/root/kinginx/nginx
24641 ->/root/kinginx/nginx
24642 ->/root/kinginx/nginx
24644 ->/root/kinginx/nginx
24645 ->/root/kinginx/nginx
24647 ->/root/kinginx/nginx
24649 ->/root/kinginx/nginx
24651 ->/root/kinginx/nginx
24653 ->/root/kinginx/nginx
24654 ->/root/kinginx/nginx
24655 ->/root/kinginx/nginx
24657 ->/root/kinginx/nginx
24658 ->/root/kinginx/nginx
24659 ->/root/kinginx/nginx
24660 ->/root/kinginx/nginx
24661 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26625 ->/root/kinginx/nginx
26627 ->/root/kinginx/nginx
26629 ->/root/kinginx/nginx
26631 ->/root/kinginx/nginx
26633 ->/root/kinginx/nginx
26635 ->/root/kinginx/nginx
26636 ->/root/kinginx/nginx
26637 ->/root/kinginx/nginx
26639 ->/root/kinginx/nginx
26640 ->/root/kinginx/nginx
26641 ->/root/kinginx/nginx
26642 ->/root/kinginx/nginx
26643 ->/root/kinginx/nginx
26644 ->/root/kinginx/nginx
26645 ->/root/kinginx/nginx
26646 ->/root/kinginx/nginx
26647 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21531 ->/root/kinginx/nginx
21533 ->/root/kinginx/nginx
21534 ->/root/kinginx/nginx
21536 ->/root/kinginx/nginx
21539 ->/root/kinginx/nginx
21541 ->/root/kinginx/nginx
21542 ->/root/kinginx/nginx
21543 ->/root/kinginx/nginx
21545 ->/root/kinginx/nginx
21546 ->/root/kinginx/nginx
21547 ->/root/kinginx/nginx
21548 ->/root/kinginx/nginx
21549 ->/root/kinginx/nginx
21550 ->/root/kinginx/nginx
21551 ->/root/kinginx/nginx
21552 ->/root/kinginx/nginx
21554 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12188 ->/root/kinginx/nginx
12190 ->/root/kinginx/nginx
12192 ->/root/kinginx/nginx
12195 ->/root/kinginx/nginx
12196 ->/root/kinginx/nginx
12198 ->/root/kinginx/nginx
12199 ->/root/kinginx/nginx
12200 ->/root/kinginx/nginx
12202 ->/root/kinginx/nginx
12204 ->/root/kinginx/nginx
12205 ->/root/kinginx/nginx
12206 ->/root/kinginx/nginx
12207 ->/root/kinginx/nginx
12208 ->/root/kinginx/nginx
12209 ->/root/kinginx/nginx
12210 ->/root/kinginx/nginx
12211 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2107 ->/root/kinginx/nginx
2108 ->/root/kinginx/nginx
2109 ->/root/kinginx/nginx
2111 ->/root/kinginx/nginx
2112 ->/root/kinginx/nginx
2113 ->/root/kinginx/nginx
2116 ->/root/kinginx/nginx
2119 ->/root/kinginx/nginx
2120 ->/root/kinginx/nginx
2121 ->/root/kinginx/nginx
2123 ->/root/kinginx/nginx
2124 ->/root/kinginx/nginx
2125 ->/root/kinginx/nginx
2126 ->/root/kinginx/nginx
2127 ->/root/kinginx/nginx
2128 ->/root/kinginx/nginx
2129 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10689 ->/root/kinginx/nginx
10691 ->/root/kinginx/nginx
10692 ->/root/kinginx/nginx
10694 ->/root/kinginx/nginx
10696 ->/root/kinginx/nginx
10699 ->/root/kinginx/nginx
10700 ->/root/kinginx/nginx
10701 ->/root/kinginx/nginx
10703 ->/root/kinginx/nginx
10704 ->/root/kinginx/nginx
10705 ->/root/kinginx/nginx
10706 ->/root/kinginx/nginx
10707 ->/root/kinginx/nginx
10709 ->/root/kinginx/nginx
10712 ->/root/kinginx/nginx
10716 ->/root/kinginx/nginx
10717 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
28104 ->/root/kinginx/nginx
28106 ->/root/kinginx/nginx
28107 ->/root/kinginx/nginx
28108 ->/root/kinginx/nginx
28110 ->/root/kinginx/nginx
28112 ->/root/kinginx/nginx
28114 ->/root/kinginx/nginx
28116 ->/root/kinginx/nginx
28117 ->/root/kinginx/nginx
28118 ->/root/kinginx/nginx
28120 ->/root/kinginx/nginx
28121 ->/root/kinginx/nginx
28122 ->/root/kinginx/nginx
28123 ->/root/kinginx/nginx
28124 ->/root/kinginx/nginx
28125 ->/root/kinginx/nginx
28126 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8469 ->/root/kinginx/nginx
8471 ->/root/kinginx/nginx
8472 ->/root/kinginx/nginx
8474 ->/root/kinginx/nginx
8476 ->/root/kinginx/nginx
8478 ->/root/kinginx/nginx
8480 ->/root/kinginx/nginx
8481 ->/root/kinginx/nginx
8483 ->/root/kinginx/nginx
8484 ->/root/kinginx/nginx
8485 ->/root/kinginx/nginx
8486 ->/root/kinginx/nginx
8487 ->/root/kinginx/nginx
8488 ->/root/kinginx/nginx
8489 ->/root/kinginx/nginx
8491 ->/root/kinginx/nginx
8492 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2719 ->/root/kinginx/nginx
2720 ->/root/kinginx/nginx
2721 ->/root/kinginx/nginx
2722 ->/root/kinginx/nginx
2724 ->/root/kinginx/nginx
2725 ->/root/kinginx/nginx
2728 ->/root/kinginx/nginx
2730 ->/root/kinginx/nginx
2732 ->/root/kinginx/nginx
2733 ->/root/kinginx/nginx
2736 ->/root/kinginx/nginx
2738 ->/root/kinginx/nginx
2739 ->/root/kinginx/nginx
2740 ->/root/kinginx/nginx
2741 ->/root/kinginx/nginx
2742 ->/root/kinginx/nginx
2743 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
29543 ->/root/kinginx/nginx
29545 ->/root/kinginx/nginx
29546 ->/root/kinginx/nginx
29548 ->/root/kinginx/nginx
29550 ->/root/kinginx/nginx
29552 ->/root/kinginx/nginx
29554 ->/root/kinginx/nginx
29555 ->/root/kinginx/nginx
29556 ->/root/kinginx/nginx
29558 ->/root/kinginx/nginx
29559 ->/root/kinginx/nginx
29560 ->/root/kinginx/nginx
29561 ->/root/kinginx/nginx
29562 ->/root/kinginx/nginx
29563 ->/root/kinginx/nginx
29564 ->/root/kinginx/nginx
29565 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31180 ->/root/kinginx/nginx
31181 ->/root/kinginx/nginx
31182 ->/root/kinginx/nginx
31184 ->/root/kinginx/nginx
31185 ->/root/kinginx/nginx
31187 ->/root/kinginx/nginx
31189 ->/root/kinginx/nginx
31191 ->/root/kinginx/nginx
31193 ->/root/kinginx/nginx
31194 ->/root/kinginx/nginx
31195 ->/root/kinginx/nginx
31197 ->/root/kinginx/nginx
31198 ->/root/kinginx/nginx
31199 ->/root/kinginx/nginx
31200 ->/root/kinginx/nginx
31201 ->/root/kinginx/nginx
31202 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26364 ->/root/kinginx/nginx
26365 ->/root/kinginx/nginx
26366 ->/root/kinginx/nginx
26368 ->/root/kinginx/nginx
26369 ->/root/kinginx/nginx
26371 ->/root/kinginx/nginx
26374 ->/root/kinginx/nginx
26376 ->/root/kinginx/nginx
26377 ->/root/kinginx/nginx
26378 ->/root/kinginx/nginx
26380 ->/root/kinginx/nginx
26381 ->/root/kinginx/nginx
26382 ->/root/kinginx/nginx
26383 ->/root/kinginx/nginx
26384 ->/root/kinginx/nginx
26385 ->/root/kinginx/nginx
26386 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
3176 ->/root/kinginx/nginx
3177 ->/root/kinginx/nginx
3178 ->/root/kinginx/nginx
3179 ->/root/kinginx/nginx
3181 ->/root/kinginx/nginx
3182 ->/root/kinginx/nginx
3183 ->/root/kinginx/nginx
3185 ->/root/kinginx/nginx
3187 ->/root/kinginx/nginx
3189 ->/root/kinginx/nginx
3190 ->/root/kinginx/nginx
3192 ->/root/kinginx/nginx
3193 ->/root/kinginx/nginx
3194 ->/root/kinginx/nginx
3196 ->/root/kinginx/nginx
3197 ->/root/kinginx/nginx
3198 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
3756 ->/root/kinginx/nginx
3758 ->/root/kinginx/nginx
3759 ->/root/kinginx/nginx
3761 ->/root/kinginx/nginx
3764 ->/root/kinginx/nginx
3765 ->/root/kinginx/nginx
3767 ->/root/kinginx/nginx
3769 ->/root/kinginx/nginx
3770 ->/root/kinginx/nginx
3771 ->/root/kinginx/nginx
3772 ->/root/kinginx/nginx
3773 ->/root/kinginx/nginx
3774 ->/root/kinginx/nginx
3775 ->/root/kinginx/nginx
3776 ->/root/kinginx/nginx
3777 ->/root/kinginx/nginx
3779 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31520 ->/root/kinginx/nginx
31522 ->/root/kinginx/nginx
31523 ->/root/kinginx/nginx
31525 ->/root/kinginx/nginx
31527 ->/root/kinginx/nginx
31530 ->/root/kinginx/nginx
31531 ->/root/kinginx/nginx
31532 ->/root/kinginx/nginx
31533 ->/root/kinginx/nginx
31535 ->/root/kinginx/nginx
31536 ->/root/kinginx/nginx
31537 ->/root/kinginx/nginx
31538 ->/root/kinginx/nginx
31539 ->/root/kinginx/nginx
31540 ->/root/kinginx/nginx
31541 ->/root/kinginx/nginx
31542 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9940 ->/root/kinginx/nginx
9941 ->/root/kinginx/nginx
9942 ->/root/kinginx/nginx
9944 ->/root/kinginx/nginx
9945 ->/root/kinginx/nginx
9947 ->/root/kinginx/nginx
9949 ->/root/kinginx/nginx
9951 ->/root/kinginx/nginx
9952 ->/root/kinginx/nginx
9954 ->/root/kinginx/nginx
9955 ->/root/kinginx/nginx
9957 ->/root/kinginx/nginx
9958 ->/root/kinginx/nginx
9959 ->/root/kinginx/nginx
9960 ->/root/kinginx/nginx
9961 ->/root/kinginx/nginx
9962 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
10556 ->/root/kinginx/nginx
10558 ->/root/kinginx/nginx
10559 ->/root/kinginx/nginx
10561 ->/root/kinginx/nginx
10562 ->/root/kinginx/nginx
10565 ->/root/kinginx/nginx
10567 ->/root/kinginx/nginx
10568 ->/root/kinginx/nginx
10569 ->/root/kinginx/nginx
10570 ->/root/kinginx/nginx
10572 ->/root/kinginx/nginx
10573 ->/root/kinginx/nginx
10574 ->/root/kinginx/nginx
10575 ->/root/kinginx/nginx
10576 ->/root/kinginx/nginx
10577 ->/root/kinginx/nginx
10578 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2156 ->/root/kinginx/nginx
2158 ->/root/kinginx/nginx
2159 ->/root/kinginx/nginx
2162 ->/root/kinginx/nginx
2164 ->/root/kinginx/nginx
2166 ->/root/kinginx/nginx
2167 ->/root/kinginx/nginx
2168 ->/root/kinginx/nginx
2169 ->/root/kinginx/nginx
2171 ->/root/kinginx/nginx
2172 ->/root/kinginx/nginx
2173 ->/root/kinginx/nginx
2174 ->/root/kinginx/nginx
2175 ->/root/kinginx/nginx
2176 ->/root/kinginx/nginx
2177 ->/root/kinginx/nginx
2179 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21586 ->/root/kinginx/nginx
21587 ->/root/kinginx/nginx
21588 ->/root/kinginx/nginx
21589 ->/root/kinginx/nginx
21591 ->/root/kinginx/nginx
21593 ->/root/kinginx/nginx
21595 ->/root/kinginx/nginx
21598 ->/root/kinginx/nginx
21599 ->/root/kinginx/nginx
21601 ->/root/kinginx/nginx
21602 ->/root/kinginx/nginx
21603 ->/root/kinginx/nginx
21604 ->/root/kinginx/nginx
21606 ->/root/kinginx/nginx
21607 ->/root/kinginx/nginx
21609 ->/root/kinginx/nginx
21611 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
20317 ->/root/kinginx/nginx
20318 ->/root/kinginx/nginx
20319 ->/root/kinginx/nginx
20320 ->/root/kinginx/nginx
20321 ->/root/kinginx/nginx
20324 ->/root/kinginx/nginx
20328 ->/root/kinginx/nginx
20330 ->/root/kinginx/nginx
20331 ->/root/kinginx/nginx
20332 ->/root/kinginx/nginx
20334 ->/root/kinginx/nginx
20336 ->/root/kinginx/nginx
20338 ->/root/kinginx/nginx
20340 ->/root/kinginx/nginx
20342 ->/root/kinginx/nginx
20344 ->/root/kinginx/nginx
20345 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14839 ->/root/kinginx/nginx
14840 ->/root/kinginx/nginx
14841 ->/root/kinginx/nginx
14843 ->/root/kinginx/nginx
14846 ->/root/kinginx/nginx
14849 ->/root/kinginx/nginx
14850 ->/root/kinginx/nginx
14852 ->/root/kinginx/nginx
14853 ->/root/kinginx/nginx
14854 ->/root/kinginx/nginx
14856 ->/root/kinginx/nginx
14858 ->/root/kinginx/nginx
14861 ->/root/kinginx/nginx
14864 ->/root/kinginx/nginx
14865 ->/root/kinginx/nginx
14866 ->/root/kinginx/nginx
14867 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
15526 ->/root/kinginx/nginx
15527 ->/root/kinginx/nginx
15528 ->/root/kinginx/nginx
15529 ->/root/kinginx/nginx
15531 ->/root/kinginx/nginx
15532 ->/root/kinginx/nginx
15534 ->/root/kinginx/nginx
15536 ->/root/kinginx/nginx
15538 ->/root/kinginx/nginx
15540 ->/root/kinginx/nginx
15541 ->/root/kinginx/nginx
15543 ->/root/kinginx/nginx
15544 ->/root/kinginx/nginx
15545 ->/root/kinginx/nginx
15546 ->/root/kinginx/nginx
15547 ->/root/kinginx/nginx
15548 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16793 ->/root/kinginx/nginx
16794 ->/root/kinginx/nginx
16795 ->/root/kinginx/nginx
16797 ->/root/kinginx/nginx
16798 ->/root/kinginx/nginx
16799 ->/root/kinginx/nginx
16802 ->/root/kinginx/nginx
16804 ->/root/kinginx/nginx
16806 ->/root/kinginx/nginx
16807 ->/root/kinginx/nginx
16808 ->/root/kinginx/nginx
16810 ->/root/kinginx/nginx
16811 ->/root/kinginx/nginx
16812 ->/root/kinginx/nginx
16813 ->/root/kinginx/nginx
16814 ->/root/kinginx/nginx
16815 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12014 ->/root/kinginx/nginx
12015 ->/root/kinginx/nginx
12016 ->/root/kinginx/nginx
12017 ->/root/kinginx/nginx
12019 ->/root/kinginx/nginx
12021 ->/root/kinginx/nginx
12023 ->/root/kinginx/nginx
12026 ->/root/kinginx/nginx
12027 ->/root/kinginx/nginx
12029 ->/root/kinginx/nginx
12030 ->/root/kinginx/nginx
12031 ->/root/kinginx/nginx
12032 ->/root/kinginx/nginx
12034 ->/root/kinginx/nginx
12036 ->/root/kinginx/nginx
12038 ->/root/kinginx/nginx
12041 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
15392 ->/root/kinginx/nginx
15393 ->/root/kinginx/nginx
15394 ->/root/kinginx/nginx
15395 ->/root/kinginx/nginx
15397 ->/root/kinginx/nginx
15398 ->/root/kinginx/nginx
15399 ->/root/kinginx/nginx
15402 ->/root/kinginx/nginx
15406 ->/root/kinginx/nginx
15407 ->/root/kinginx/nginx
15408 ->/root/kinginx/nginx
15410 ->/root/kinginx/nginx
15411 ->/root/kinginx/nginx
15413 ->/root/kinginx/nginx
15415 ->/root/kinginx/nginx
15417 ->/root/kinginx/nginx
15419 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31483 ->/root/kinginx/nginx
31484 ->/root/kinginx/nginx
31485 ->/root/kinginx/nginx
31486 ->/root/kinginx/nginx
31487 ->/root/kinginx/nginx
31489 ->/root/kinginx/nginx
31492 ->/root/kinginx/nginx
31494 ->/root/kinginx/nginx
31497 ->/root/kinginx/nginx
31498 ->/root/kinginx/nginx
31500 ->/root/kinginx/nginx
31502 ->/root/kinginx/nginx
31505 ->/root/kinginx/nginx
31508 ->/root/kinginx/nginx
31509 ->/root/kinginx/nginx
31510 ->/root/kinginx/nginx
31511 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9298 ->/root/kinginx/nginx
9299 ->/root/kinginx/nginx
9300 ->/root/kinginx/nginx
9301 ->/root/kinginx/nginx
9303 ->/root/kinginx/nginx
9304 ->/root/kinginx/nginx
9306 ->/root/kinginx/nginx
9309 ->/root/kinginx/nginx
9311 ->/root/kinginx/nginx
9312 ->/root/kinginx/nginx
9314 ->/root/kinginx/nginx
9316 ->/root/kinginx/nginx
9320 ->/root/kinginx/nginx
9322 ->/root/kinginx/nginx
9324 ->/root/kinginx/nginx
9325 ->/root/kinginx/nginx
9326 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6575 ->/root/kinginx/nginx
6576 ->/root/kinginx/nginx
6577 ->/root/kinginx/nginx
6578 ->/root/kinginx/nginx
6580 ->/root/kinginx/nginx
6584 ->/root/kinginx/nginx
6587 ->/root/kinginx/nginx
6588 ->/root/kinginx/nginx
6589 ->/root/kinginx/nginx
6590 ->/root/kinginx/nginx
6592 ->/root/kinginx/nginx
6595 ->/root/kinginx/nginx
6599 ->/root/kinginx/nginx
6600 ->/root/kinginx/nginx
6601 ->/root/kinginx/nginx
6602 ->/root/kinginx/nginx
6603 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
28888 ->/root/kinginx/nginx
28889 ->/root/kinginx/nginx
28890 ->/root/kinginx/nginx
28891 ->/root/kinginx/nginx
28893 ->/root/kinginx/nginx
28894 ->/root/kinginx/nginx
28896 ->/root/kinginx/nginx
28897 ->/root/kinginx/nginx
28899 ->/root/kinginx/nginx
28902 ->/root/kinginx/nginx
28903 ->/root/kinginx/nginx
28905 ->/root/kinginx/nginx
28908 ->/root/kinginx/nginx
28910 ->/root/kinginx/nginx
28912 ->/root/kinginx/nginx
28915 ->/root/kinginx/nginx
28916 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26821 ->/root/kinginx/nginx
26822 ->/root/kinginx/nginx
26823 ->/root/kinginx/nginx
26824 ->/root/kinginx/nginx
26825 ->/root/kinginx/nginx
26826 ->/root/kinginx/nginx
26829 ->/root/kinginx/nginx
26832 ->/root/kinginx/nginx
26834 ->/root/kinginx/nginx
26835 ->/root/kinginx/nginx
26836 ->/root/kinginx/nginx
26838 ->/root/kinginx/nginx
26839 ->/root/kinginx/nginx
26840 ->/root/kinginx/nginx
26841 ->/root/kinginx/nginx
26842 ->/root/kinginx/nginx
26846 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31007 ->/root/kinginx/nginx
31008 ->/root/kinginx/nginx
31009 ->/root/kinginx/nginx
31010 ->/root/kinginx/nginx
31011 ->/root/kinginx/nginx
31012 ->/root/kinginx/nginx
31018 ->/root/kinginx/nginx
31019 ->/root/kinginx/nginx
31021 ->/root/kinginx/nginx
31022 ->/root/kinginx/nginx
31023 ->/root/kinginx/nginx
31025 ->/root/kinginx/nginx
31029 ->/root/kinginx/nginx
31032 ->/root/kinginx/nginx
31033 ->/root/kinginx/nginx
31034 ->/root/kinginx/nginx
31035 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6657 ->/root/kinginx/nginx
6658 ->/root/kinginx/nginx
6659 ->/root/kinginx/nginx
6660 ->/root/kinginx/nginx
6661 ->/root/kinginx/nginx
6662 ->/root/kinginx/nginx
6663 ->/root/kinginx/nginx
6668 ->/root/kinginx/nginx
6669 ->/root/kinginx/nginx
6671 ->/root/kinginx/nginx
6673 ->/root/kinginx/nginx
6674 ->/root/kinginx/nginx
6675 ->/root/kinginx/nginx
6676 ->/root/kinginx/nginx
6677 ->/root/kinginx/nginx
6678 ->/root/kinginx/nginx
6679 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
20332 ->/root/kinginx/nginx
20333 ->/root/kinginx/nginx
20334 ->/root/kinginx/nginx
20335 ->/root/kinginx/nginx
20336 ->/root/kinginx/nginx
20338 ->/root/kinginx/nginx
20341 ->/root/kinginx/nginx
20344 ->/root/kinginx/nginx
20346 ->/root/kinginx/nginx
20347 ->/root/kinginx/nginx
20348 ->/root/kinginx/nginx
20350 ->/root/kinginx/nginx
20351 ->/root/kinginx/nginx
20354 ->/root/kinginx/nginx
20356 ->/root/kinginx/nginx
20359 ->/root/kinginx/nginx
20360 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
4280 ->/root/kinginx/nginx
4281 ->/root/kinginx/nginx
4282 ->/root/kinginx/nginx
4283 ->/root/kinginx/nginx
4284 ->/root/kinginx/nginx
4285 ->/root/kinginx/nginx
4287 ->/root/kinginx/nginx
4288 ->/root/kinginx/nginx
4290 ->/root/kinginx/nginx
4293 ->/root/kinginx/nginx
4294 ->/root/kinginx/nginx
4295 ->/root/kinginx/nginx
4296 ->/root/kinginx/nginx
4297 ->/root/kinginx/nginx
4299 ->/root/kinginx/nginx
4300 ->/root/kinginx/nginx
4301 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
23375 ->/root/kinginx/nginx
23376 ->/root/kinginx/nginx
23377 ->/root/kinginx/nginx
23378 ->/root/kinginx/nginx
23379 ->/root/kinginx/nginx
23380 ->/root/kinginx/nginx
23382 ->/root/kinginx/nginx
23384 ->/root/kinginx/nginx
23386 ->/root/kinginx/nginx
23388 ->/root/kinginx/nginx
23390 ->/root/kinginx/nginx
23392 ->/root/kinginx/nginx
23393 ->/root/kinginx/nginx
23394 ->/root/kinginx/nginx
23396 ->/root/kinginx/nginx
23397 ->/root/kinginx/nginx
23398 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13948 ->/root/kinginx/nginx
13949 ->/root/kinginx/nginx
13950 ->/root/kinginx/nginx
13951 ->/root/kinginx/nginx
13952 ->/root/kinginx/nginx
13953 ->/root/kinginx/nginx
13954 ->/root/kinginx/nginx
13955 ->/root/kinginx/nginx
13956 ->/root/kinginx/nginx
13957 ->/root/kinginx/nginx
13958 ->/root/kinginx/nginx
13959 ->/root/kinginx/nginx
13960 ->/root/kinginx/nginx
13961 ->/root/kinginx/nginx
13962 ->/root/kinginx/nginx
13963 ->/root/kinginx/nginx
13964 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8512 ->/root/kinginx/nginx
8513 ->/root/kinginx/nginx
8514 ->/root/kinginx/nginx
8515 ->/root/kinginx/nginx
8517 ->/root/kinginx/nginx
8518 ->/root/kinginx/nginx
8520 ->/root/kinginx/nginx
8521 ->/root/kinginx/nginx
8525 ->/root/kinginx/nginx
8526 ->/root/kinginx/nginx
8528 ->/root/kinginx/nginx
8529 ->/root/kinginx/nginx
8530 ->/root/kinginx/nginx
8531 ->/root/kinginx/nginx
8533 ->/root/kinginx/nginx
8534 ->/root/kinginx/nginx
8535 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
25326 ->/root/kinginx/nginx
25327 ->/root/kinginx/nginx
25328 ->/root/kinginx/nginx
25329 ->/root/kinginx/nginx
25330 ->/root/kinginx/nginx
25331 ->/root/kinginx/nginx
25332 ->/root/kinginx/nginx
25334 ->/root/kinginx/nginx
25335 ->/root/kinginx/nginx
25337 ->/root/kinginx/nginx
25339 ->/root/kinginx/nginx
25341 ->/root/kinginx/nginx
25342 ->/root/kinginx/nginx
25343 ->/root/kinginx/nginx
25345 ->/root/kinginx/nginx
25346 ->/root/kinginx/nginx
25348 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13897 ->/root/kinginx/nginx
13898 ->/root/kinginx/nginx
13899 ->/root/kinginx/nginx
13901 ->/root/kinginx/nginx
13903 ->/root/kinginx/nginx
13907 ->/root/kinginx/nginx
13908 ->/root/kinginx/nginx
13910 ->/root/kinginx/nginx
13911 ->/root/kinginx/nginx
13912 ->/root/kinginx/nginx
13914 ->/root/kinginx/nginx
13916 ->/root/kinginx/nginx
13919 ->/root/kinginx/nginx
13921 ->/root/kinginx/nginx
13923 ->/root/kinginx/nginx
13924 ->/root/kinginx/nginx
13925 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
17269 ->/root/kinginx/nginx
17270 ->/root/kinginx/nginx
17271 ->/root/kinginx/nginx
17272 ->/root/kinginx/nginx
17274 ->/root/kinginx/nginx
17275 ->/root/kinginx/nginx
17277 ->/root/kinginx/nginx
17280 ->/root/kinginx/nginx
17282 ->/root/kinginx/nginx
17283 ->/root/kinginx/nginx
17285 ->/root/kinginx/nginx
17286 ->/root/kinginx/nginx
17288 ->/root/kinginx/nginx
17289 ->/root/kinginx/nginx
17290 ->/root/kinginx/nginx
17292 ->/root/kinginx/nginx
17294 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9162 ->/root/kinginx/nginx
9163 ->/root/kinginx/nginx
9164 ->/root/kinginx/nginx
9165 ->/root/kinginx/nginx
9167 ->/root/kinginx/nginx
9168 ->/root/kinginx/nginx
9169 ->/root/kinginx/nginx
9171 ->/root/kinginx/nginx
9174 ->/root/kinginx/nginx
9176 ->/root/kinginx/nginx
9177 ->/root/kinginx/nginx
9178 ->/root/kinginx/nginx
9179 ->/root/kinginx/nginx
9180 ->/root/kinginx/nginx
9182 ->/root/kinginx/nginx
9183 ->/root/kinginx/nginx
9184 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13124 ->/root/kinginx/nginx
13125 ->/root/kinginx/nginx
13126 ->/root/kinginx/nginx
13127 ->/root/kinginx/nginx
13128 ->/root/kinginx/nginx
13129 ->/root/kinginx/nginx
13131 ->/root/kinginx/nginx
13132 ->/root/kinginx/nginx
13134 ->/root/kinginx/nginx
13139 ->/root/kinginx/nginx
13140 ->/root/kinginx/nginx
13141 ->/root/kinginx/nginx
13142 ->/root/kinginx/nginx
13143 ->/root/kinginx/nginx
13144 ->/root/kinginx/nginx
13145 ->/root/kinginx/nginx
13146 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
4094 ->/root/kinginx/nginx
4095 ->/root/kinginx/nginx
4096 ->/root/kinginx/nginx
4097 ->/root/kinginx/nginx
4099 ->/root/kinginx/nginx
4100 ->/root/kinginx/nginx
4102 ->/root/kinginx/nginx
4103 ->/root/kinginx/nginx
4105 ->/root/kinginx/nginx
4108 ->/root/kinginx/nginx
4109 ->/root/kinginx/nginx
4111 ->/root/kinginx/nginx
4112 ->/root/kinginx/nginx
4113 ->/root/kinginx/nginx
4114 ->/root/kinginx/nginx
4115 ->/root/kinginx/nginx
4116 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6939 ->/root/kinginx/nginx
6940 ->/root/kinginx/nginx
6941 ->/root/kinginx/nginx
6943 ->/root/kinginx/nginx
6944 ->/root/kinginx/nginx
6946 ->/root/kinginx/nginx
6948 ->/root/kinginx/nginx
6950 ->/root/kinginx/nginx
6951 ->/root/kinginx/nginx
6952 ->/root/kinginx/nginx
6953 ->/root/kinginx/nginx
6954 ->/root/kinginx/nginx
6955 ->/root/kinginx/nginx
6956 ->/root/kinginx/nginx
6957 ->/root/kinginx/nginx
6958 ->/root/kinginx/nginx
6959 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21194 ->/root/kinginx/nginx
21195 ->/root/kinginx/nginx
21196 ->/root/kinginx/nginx
21197 ->/root/kinginx/nginx
21199 ->/root/kinginx/nginx
21200 ->/root/kinginx/nginx
21201 ->/root/kinginx/nginx
21203 ->/root/kinginx/nginx
21204 ->/root/kinginx/nginx
21206 ->/root/kinginx/nginx
21209 ->/root/kinginx/nginx
21210 ->/root/kinginx/nginx
21212 ->/root/kinginx/nginx
21213 ->/root/kinginx/nginx
21214 ->/root/kinginx/nginx
21215 ->/root/kinginx/nginx
21216 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22433 ->/root/kinginx/nginx
22434 ->/root/kinginx/nginx
22435 ->/root/kinginx/nginx
22436 ->/root/kinginx/nginx
22438 ->/root/kinginx/nginx
22439 ->/root/kinginx/nginx
22441 ->/root/kinginx/nginx
22443 ->/root/kinginx/nginx
22446 ->/root/kinginx/nginx
22447 ->/root/kinginx/nginx
22448 ->/root/kinginx/nginx
22450 ->/root/kinginx/nginx
22451 ->/root/kinginx/nginx
22452 ->/root/kinginx/nginx
22453 ->/root/kinginx/nginx
22454 ->/root/kinginx/nginx
22455 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
25669 ->/root/kinginx/nginx
25670 ->/root/kinginx/nginx
25671 ->/root/kinginx/nginx
25673 ->/root/kinginx/nginx
25674 ->/root/kinginx/nginx
25676 ->/root/kinginx/nginx
25678 ->/root/kinginx/nginx
25681 ->/root/kinginx/nginx
25682 ->/root/kinginx/nginx
25683 ->/root/kinginx/nginx
25685 ->/root/kinginx/nginx
25686 ->/root/kinginx/nginx
25687 ->/root/kinginx/nginx
25688 ->/root/kinginx/nginx
25689 ->/root/kinginx/nginx
25690 ->/root/kinginx/nginx
25691 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8846 ->/root/kinginx/nginx
8847 ->/root/kinginx/nginx
8848 ->/root/kinginx/nginx
8849 ->/root/kinginx/nginx
8851 ->/root/kinginx/nginx
8852 ->/root/kinginx/nginx
8853 ->/root/kinginx/nginx
8855 ->/root/kinginx/nginx
8858 ->/root/kinginx/nginx
8859 ->/root/kinginx/nginx
8861 ->/root/kinginx/nginx
8862 ->/root/kinginx/nginx
8863 ->/root/kinginx/nginx
8864 ->/root/kinginx/nginx
8865 ->/root/kinginx/nginx
8866 ->/root/kinginx/nginx
8867 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11527 ->/root/kinginx/nginx
11528 ->/root/kinginx/nginx
11529 ->/root/kinginx/nginx
11530 ->/root/kinginx/nginx
11532 ->/root/kinginx/nginx
11533 ->/root/kinginx/nginx
11536 ->/root/kinginx/nginx
11539 ->/root/kinginx/nginx
11540 ->/root/kinginx/nginx
11541 ->/root/kinginx/nginx
11543 ->/root/kinginx/nginx
11544 ->/root/kinginx/nginx
11545 ->/root/kinginx/nginx
11546 ->/root/kinginx/nginx
11547 ->/root/kinginx/nginx
11548 ->/root/kinginx/nginx
11550 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13129 ->/root/kinginx/nginx
13130 ->/root/kinginx/nginx
13131 ->/root/kinginx/nginx
13132 ->/root/kinginx/nginx
13134 ->/root/kinginx/nginx
13135 ->/root/kinginx/nginx
13137 ->/root/kinginx/nginx
13140 ->/root/kinginx/nginx
13142 ->/root/kinginx/nginx
13143 ->/root/kinginx/nginx
13144 ->/root/kinginx/nginx
13146 ->/root/kinginx/nginx
13147 ->/root/kinginx/nginx
13148 ->/root/kinginx/nginx
13149 ->/root/kinginx/nginx
13150 ->/root/kinginx/nginx
13151 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
24520 ->/root/kinginx/nginx
24521 ->/root/kinginx/nginx
24522 ->/root/kinginx/nginx
24523 ->/root/kinginx/nginx
24524 ->/root/kinginx/nginx
24526 ->/root/kinginx/nginx
24527 ->/root/kinginx/nginx
24528 ->/root/kinginx/nginx
24530 ->/root/kinginx/nginx
24532 ->/root/kinginx/nginx
24535 ->/root/kinginx/nginx
24536 ->/root/kinginx/nginx
24538 ->/root/kinginx/nginx
24539 ->/root/kinginx/nginx
24540 ->/root/kinginx/nginx
24541 ->/root/kinginx/nginx
24542 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1126 ->/root/kinginx/nginx
1127 ->/root/kinginx/nginx
1128 ->/root/kinginx/nginx
1129 ->/root/kinginx/nginx
1130 ->/root/kinginx/nginx
1131 ->/root/kinginx/nginx
1132 ->/root/kinginx/nginx
1133 ->/root/kinginx/nginx
1134 ->/root/kinginx/nginx
1135 ->/root/kinginx/nginx
1136 ->/root/kinginx/nginx
1137 ->/root/kinginx/nginx
1138 ->/root/kinginx/nginx
1143 ->/root/kinginx/nginx
1145 ->/root/kinginx/nginx
1147 ->/root/kinginx/nginx
1148 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
5566 ->/root/kinginx/nginx
5567 ->/root/kinginx/nginx
5568 ->/root/kinginx/nginx
5569 ->/root/kinginx/nginx
5571 ->/root/kinginx/nginx
5572 ->/root/kinginx/nginx
5574 ->/root/kinginx/nginx
5576 ->/root/kinginx/nginx
5579 ->/root/kinginx/nginx
5580 ->/root/kinginx/nginx
5582 ->/root/kinginx/nginx
5583 ->/root/kinginx/nginx
5584 ->/root/kinginx/nginx
5585 ->/root/kinginx/nginx
5586 ->/root/kinginx/nginx
5588 ->/root/kinginx/nginx
5589 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22611 ->/root/kinginx/nginx
22612 ->/root/kinginx/nginx
22613 ->/root/kinginx/nginx
22614 ->/root/kinginx/nginx
22616 ->/root/kinginx/nginx
22618 ->/root/kinginx/nginx
22621 ->/root/kinginx/nginx
22622 ->/root/kinginx/nginx
22624 ->/root/kinginx/nginx
22625 ->/root/kinginx/nginx
22627 ->/root/kinginx/nginx
22628 ->/root/kinginx/nginx
22629 ->/root/kinginx/nginx
22630 ->/root/kinginx/nginx
22631 ->/root/kinginx/nginx
22633 ->/root/kinginx/nginx
22634 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6805 ->/root/kinginx/nginx
6806 ->/root/kinginx/nginx
6807 ->/root/kinginx/nginx
6808 ->/root/kinginx/nginx
6810 ->/root/kinginx/nginx
6812 ->/root/kinginx/nginx
6814 ->/root/kinginx/nginx
6815 ->/root/kinginx/nginx
6816 ->/root/kinginx/nginx
6818 ->/root/kinginx/nginx
6820 ->/root/kinginx/nginx
6821 ->/root/kinginx/nginx
6823 ->/root/kinginx/nginx
6824 ->/root/kinginx/nginx
6825 ->/root/kinginx/nginx
6826 ->/root/kinginx/nginx
6827 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27417 ->/root/kinginx/nginx
27418 ->/root/kinginx/nginx
27419 ->/root/kinginx/nginx
27420 ->/root/kinginx/nginx
27422 ->/root/kinginx/nginx
27423 ->/root/kinginx/nginx
27425 ->/root/kinginx/nginx
27427 ->/root/kinginx/nginx
27429 ->/root/kinginx/nginx
27431 ->/root/kinginx/nginx
27432 ->/root/kinginx/nginx
27434 ->/root/kinginx/nginx
27435 ->/root/kinginx/nginx
27441 ->/root/kinginx/nginx
27443 ->/root/kinginx/nginx
27444 ->/root/kinginx/nginx
27445 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2681 ->/root/kinginx/nginx
2682 ->/root/kinginx/nginx
2683 ->/root/kinginx/nginx
2684 ->/root/kinginx/nginx
2685 ->/root/kinginx/nginx
2687 ->/root/kinginx/nginx
2688 ->/root/kinginx/nginx
2690 ->/root/kinginx/nginx
2692 ->/root/kinginx/nginx
2695 ->/root/kinginx/nginx
2697 ->/root/kinginx/nginx
2698 ->/root/kinginx/nginx
2700 ->/root/kinginx/nginx
2701 ->/root/kinginx/nginx
2702 ->/root/kinginx/nginx
2703 ->/root/kinginx/nginx
2704 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
1400 ->/root/kinginx/nginx
1401 ->/root/kinginx/nginx
1402 ->/root/kinginx/nginx
1404 ->/root/kinginx/nginx
1405 ->/root/kinginx/nginx
1407 ->/root/kinginx/nginx
1409 ->/root/kinginx/nginx
1412 ->/root/kinginx/nginx
1413 ->/root/kinginx/nginx
1415 ->/root/kinginx/nginx
1416 ->/root/kinginx/nginx
1417 ->/root/kinginx/nginx
1418 ->/root/kinginx/nginx
1419 ->/root/kinginx/nginx
1420 ->/root/kinginx/nginx
1422 ->/root/kinginx/nginx
1424 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22081 ->/root/kinginx/nginx
22082 ->/root/kinginx/nginx
22083 ->/root/kinginx/nginx
22085 ->/root/kinginx/nginx
22086 ->/root/kinginx/nginx
22087 ->/root/kinginx/nginx
22089 ->/root/kinginx/nginx
22091 ->/root/kinginx/nginx
22093 ->/root/kinginx/nginx
22095 ->/root/kinginx/nginx
22096 ->/root/kinginx/nginx
22098 ->/root/kinginx/nginx
22099 ->/root/kinginx/nginx
22100 ->/root/kinginx/nginx
22101 ->/root/kinginx/nginx
22102 ->/root/kinginx/nginx
22103 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8171 ->/root/kinginx/nginx
8172 ->/root/kinginx/nginx
8173 ->/root/kinginx/nginx
8175 ->/root/kinginx/nginx
8176 ->/root/kinginx/nginx
8178 ->/root/kinginx/nginx
8180 ->/root/kinginx/nginx
8183 ->/root/kinginx/nginx
8184 ->/root/kinginx/nginx
8185 ->/root/kinginx/nginx
8187 ->/root/kinginx/nginx
8188 ->/root/kinginx/nginx
8189 ->/root/kinginx/nginx
8190 ->/root/kinginx/nginx
8191 ->/root/kinginx/nginx
8192 ->/root/kinginx/nginx
8194 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8820 ->/root/kinginx/nginx
8821 ->/root/kinginx/nginx
8822 ->/root/kinginx/nginx
8823 ->/root/kinginx/nginx
8825 ->/root/kinginx/nginx
8826 ->/root/kinginx/nginx
8828 ->/root/kinginx/nginx
8830 ->/root/kinginx/nginx
8833 ->/root/kinginx/nginx
8834 ->/root/kinginx/nginx
8835 ->/root/kinginx/nginx
8837 ->/root/kinginx/nginx
8838 ->/root/kinginx/nginx
8839 ->/root/kinginx/nginx
8840 ->/root/kinginx/nginx
8841 ->/root/kinginx/nginx
8842 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14634 ->/root/kinginx/nginx
14635 ->/root/kinginx/nginx
14636 ->/root/kinginx/nginx
14637 ->/root/kinginx/nginx
14638 ->/root/kinginx/nginx
14640 ->/root/kinginx/nginx
14641 ->/root/kinginx/nginx
14643 ->/root/kinginx/nginx
14646 ->/root/kinginx/nginx
14648 ->/root/kinginx/nginx
14649 ->/root/kinginx/nginx
14651 ->/root/kinginx/nginx
14652 ->/root/kinginx/nginx
14653 ->/root/kinginx/nginx
14654 ->/root/kinginx/nginx
14655 ->/root/kinginx/nginx
14656 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13839 ->/root/kinginx/nginx
13840 ->/root/kinginx/nginx
13841 ->/root/kinginx/nginx
13843 ->/root/kinginx/nginx
13844 ->/root/kinginx/nginx
13845 ->/root/kinginx/nginx
13848 ->/root/kinginx/nginx
13851 ->/root/kinginx/nginx
13852 ->/root/kinginx/nginx
13853 ->/root/kinginx/nginx
13855 ->/root/kinginx/nginx
13856 ->/root/kinginx/nginx
13857 ->/root/kinginx/nginx
13858 ->/root/kinginx/nginx
13859 ->/root/kinginx/nginx
13861 ->/root/kinginx/nginx
13863 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21064 ->/root/kinginx/nginx
21065 ->/root/kinginx/nginx
21066 ->/root/kinginx/nginx
21067 ->/root/kinginx/nginx
21069 ->/root/kinginx/nginx
21070 ->/root/kinginx/nginx
21073 ->/root/kinginx/nginx
21075 ->/root/kinginx/nginx
21077 ->/root/kinginx/nginx
21078 ->/root/kinginx/nginx
21080 ->/root/kinginx/nginx
21081 ->/root/kinginx/nginx
21082 ->/root/kinginx/nginx
21083 ->/root/kinginx/nginx
21085 ->/root/kinginx/nginx
21086 ->/root/kinginx/nginx
21089 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
32538 ->/root/kinginx/nginx
32539 ->/root/kinginx/nginx
32540 ->/root/kinginx/nginx
32541 ->/root/kinginx/nginx
32543 ->/root/kinginx/nginx
32544 ->/root/kinginx/nginx
32546 ->/root/kinginx/nginx
32548 ->/root/kinginx/nginx
32550 ->/root/kinginx/nginx
32552 ->/root/kinginx/nginx
32553 ->/root/kinginx/nginx
32555 ->/root/kinginx/nginx
32556 ->/root/kinginx/nginx
32557 ->/root/kinginx/nginx
32558 ->/root/kinginx/nginx
32559 ->/root/kinginx/nginx
32560 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
7117 ->/root/kinginx/nginx
7118 ->/root/kinginx/nginx
7120 ->/root/kinginx/nginx
7121 ->/root/kinginx/nginx
7122 ->/root/kinginx/nginx
7123 ->/root/kinginx/nginx
7126 ->/root/kinginx/nginx
7129 ->/root/kinginx/nginx
7130 ->/root/kinginx/nginx
7131 ->/root/kinginx/nginx
7133 ->/root/kinginx/nginx
7134 ->/root/kinginx/nginx
7135 ->/root/kinginx/nginx
7136 ->/root/kinginx/nginx
7137 ->/root/kinginx/nginx
7139 ->/root/kinginx/nginx
7140 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21755 ->/root/kinginx/nginx
21756 ->/root/kinginx/nginx
21757 ->/root/kinginx/nginx
21759 ->/root/kinginx/nginx
21760 ->/root/kinginx/nginx
21762 ->/root/kinginx/nginx
21764 ->/root/kinginx/nginx
21767 ->/root/kinginx/nginx
21768 ->/root/kinginx/nginx
21769 ->/root/kinginx/nginx
21771 ->/root/kinginx/nginx
21772 ->/root/kinginx/nginx
21773 ->/root/kinginx/nginx
21774 ->/root/kinginx/nginx
21775 ->/root/kinginx/nginx
21776 ->/root/kinginx/nginx
21778 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
15389 ->/root/kinginx/nginx
15390 ->/root/kinginx/nginx
15391 ->/root/kinginx/nginx
15393 ->/root/kinginx/nginx
15394 ->/root/kinginx/nginx
15396 ->/root/kinginx/nginx
15398 ->/root/kinginx/nginx
15401 ->/root/kinginx/nginx
15402 ->/root/kinginx/nginx
15404 ->/root/kinginx/nginx
15405 ->/root/kinginx/nginx
15406 ->/root/kinginx/nginx
15407 ->/root/kinginx/nginx
15408 ->/root/kinginx/nginx
15409 ->/root/kinginx/nginx
15410 ->/root/kinginx/nginx
15412 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
31743 ->/root/kinginx/nginx
31744 ->/root/kinginx/nginx
31745 ->/root/kinginx/nginx
31747 ->/root/kinginx/nginx
31748 ->/root/kinginx/nginx
31749 ->/root/kinginx/nginx
31752 ->/root/kinginx/nginx
31754 ->/root/kinginx/nginx
31756 ->/root/kinginx/nginx
31757 ->/root/kinginx/nginx
31759 ->/root/kinginx/nginx
31760 ->/root/kinginx/nginx
31761 ->/root/kinginx/nginx
31762 ->/root/kinginx/nginx
31763 ->/root/kinginx/nginx
31764 ->/root/kinginx/nginx
31766 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2621 ->/root/kinginx/nginx
2622 ->/root/kinginx/nginx
2623 ->/root/kinginx/nginx
2625 ->/root/kinginx/nginx
2626 ->/root/kinginx/nginx
2627 ->/root/kinginx/nginx
2629 ->/root/kinginx/nginx
2631 ->/root/kinginx/nginx
2634 ->/root/kinginx/nginx
2635 ->/root/kinginx/nginx
2636 ->/root/kinginx/nginx
2638 ->/root/kinginx/nginx
2639 ->/root/kinginx/nginx
2640 ->/root/kinginx/nginx
2641 ->/root/kinginx/nginx
2642 ->/root/kinginx/nginx
2644 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
12682 ->/root/kinginx/nginx
12683 ->/root/kinginx/nginx
12684 ->/root/kinginx/nginx
12686 ->/root/kinginx/nginx
12687 ->/root/kinginx/nginx
12689 ->/root/kinginx/nginx
12691 ->/root/kinginx/nginx
12694 ->/root/kinginx/nginx
12695 ->/root/kinginx/nginx
12696 ->/root/kinginx/nginx
12698 ->/root/kinginx/nginx
12699 ->/root/kinginx/nginx
12700 ->/root/kinginx/nginx
12701 ->/root/kinginx/nginx
12702 ->/root/kinginx/nginx
12703 ->/root/kinginx/nginx
12704 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
14117 ->/root/kinginx/nginx
14118 ->/root/kinginx/nginx
14119 ->/root/kinginx/nginx
14121 ->/root/kinginx/nginx
14122 ->/root/kinginx/nginx
14124 ->/root/kinginx/nginx
14126 ->/root/kinginx/nginx
14129 ->/root/kinginx/nginx
14130 ->/root/kinginx/nginx
14131 ->/root/kinginx/nginx
14133 ->/root/kinginx/nginx
14134 ->/root/kinginx/nginx
14135 ->/root/kinginx/nginx
14136 ->/root/kinginx/nginx
14137 ->/root/kinginx/nginx
14138 ->/root/kinginx/nginx
14140 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
18433 ->/root/kinginx/nginx
18434 ->/root/kinginx/nginx
18435 ->/root/kinginx/nginx
18437 ->/root/kinginx/nginx
18438 ->/root/kinginx/nginx
18440 ->/root/kinginx/nginx
18442 ->/root/kinginx/nginx
18445 ->/root/kinginx/nginx
18446 ->/root/kinginx/nginx
18447 ->/root/kinginx/nginx
18449 ->/root/kinginx/nginx
18450 ->/root/kinginx/nginx
18451 ->/root/kinginx/nginx
18452 ->/root/kinginx/nginx
18453 ->/root/kinginx/nginx
18455 ->/root/kinginx/nginx
18456 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11950 ->/root/kinginx/nginx
11956 ->/root/kinginx/nginx
11957 ->/root/kinginx/nginx
11959 ->/root/kinginx/nginx
11960 ->/root/kinginx/nginx
11961 ->/root/kinginx/nginx
11962 ->/root/kinginx/nginx
11963 ->/root/kinginx/nginx
11964 ->/root/kinginx/nginx
11965 ->/root/kinginx/nginx
11967 ->/root/kinginx/nginx
11969 ->/root/kinginx/nginx
11972 ->/root/kinginx/nginx
11975 ->/root/kinginx/nginx
11976 ->/root/kinginx/nginx
11977 ->/root/kinginx/nginx
11978 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
29241 ->/root/kinginx/nginx
29242 ->/root/kinginx/nginx
29243 ->/root/kinginx/nginx
29245 ->/root/kinginx/nginx
29246 ->/root/kinginx/nginx
29248 ->/root/kinginx/nginx
29251 ->/root/kinginx/nginx
29253 ->/root/kinginx/nginx
29254 ->/root/kinginx/nginx
29256 ->/root/kinginx/nginx
29257 ->/root/kinginx/nginx
29258 ->/root/kinginx/nginx
29259 ->/root/kinginx/nginx
29260 ->/root/kinginx/nginx
29261 ->/root/kinginx/nginx
29263 ->/root/kinginx/nginx
29264 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
7427 ->/root/kinginx/nginx
7428 ->/root/kinginx/nginx
7429 ->/root/kinginx/nginx
7431 ->/root/kinginx/nginx
7432 ->/root/kinginx/nginx
7434 ->/root/kinginx/nginx
7436 ->/root/kinginx/nginx
7439 ->/root/kinginx/nginx
7440 ->/root/kinginx/nginx
7441 ->/root/kinginx/nginx
7443 ->/root/kinginx/nginx
7444 ->/root/kinginx/nginx
7445 ->/root/kinginx/nginx
7446 ->/root/kinginx/nginx
7447 ->/root/kinginx/nginx
7449 ->/root/kinginx/nginx
7450 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13313 ->/root/kinginx/nginx
13314 ->/root/kinginx/nginx
13315 ->/root/kinginx/nginx
13317 ->/root/kinginx/nginx
13318 ->/root/kinginx/nginx
13320 ->/root/kinginx/nginx
13322 ->/root/kinginx/nginx
13324 ->/root/kinginx/nginx
13326 ->/root/kinginx/nginx
13327 ->/root/kinginx/nginx
13329 ->/root/kinginx/nginx
13330 ->/root/kinginx/nginx
13331 ->/root/kinginx/nginx
13332 ->/root/kinginx/nginx
13333 ->/root/kinginx/nginx
13334 ->/root/kinginx/nginx
13336 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
20784 ->/root/kinginx/nginx
20785 ->/root/kinginx/nginx
20786 ->/root/kinginx/nginx
20788 ->/root/kinginx/nginx
20789 ->/root/kinginx/nginx
20791 ->/root/kinginx/nginx
20793 ->/root/kinginx/nginx
20796 ->/root/kinginx/nginx
20797 ->/root/kinginx/nginx
20798 ->/root/kinginx/nginx
20800 ->/root/kinginx/nginx
20801 ->/root/kinginx/nginx
20802 ->/root/kinginx/nginx
20803 ->/root/kinginx/nginx
20804 ->/root/kinginx/nginx
20805 ->/root/kinginx/nginx
20806 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21801 ->/root/kinginx/nginx
21802 ->/root/kinginx/nginx
21803 ->/root/kinginx/nginx
21805 ->/root/kinginx/nginx
21806 ->/root/kinginx/nginx
21808 ->/root/kinginx/nginx
21810 ->/root/kinginx/nginx
21812 ->/root/kinginx/nginx
21814 ->/root/kinginx/nginx
21815 ->/root/kinginx/nginx
21817 ->/root/kinginx/nginx
21818 ->/root/kinginx/nginx
21819 ->/root/kinginx/nginx
21820 ->/root/kinginx/nginx
21821 ->/root/kinginx/nginx
21822 ->/root/kinginx/nginx
21824 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
17697 ->/root/kinginx/nginx
17698 ->/root/kinginx/nginx
17699 ->/root/kinginx/nginx
17701 ->/root/kinginx/nginx
17702 ->/root/kinginx/nginx
17704 ->/root/kinginx/nginx
17707 ->/root/kinginx/nginx
17709 ->/root/kinginx/nginx
17710 ->/root/kinginx/nginx
17712 ->/root/kinginx/nginx
17713 ->/root/kinginx/nginx
17714 ->/root/kinginx/nginx
17715 ->/root/kinginx/nginx
17716 ->/root/kinginx/nginx
17717 ->/root/kinginx/nginx
17718 ->/root/kinginx/nginx
17720 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
652 ->/root/kinginx/nginx
653 ->/root/kinginx/nginx
654 ->/root/kinginx/nginx
656 ->/root/kinginx/nginx
657 ->/root/kinginx/nginx
659 ->/root/kinginx/nginx
662 ->/root/kinginx/nginx
666 ->/root/kinginx/nginx
667 ->/root/kinginx/nginx
668 ->/root/kinginx/nginx
670 ->/root/kinginx/nginx
671 ->/root/kinginx/nginx
672 ->/root/kinginx/nginx
673 ->/root/kinginx/nginx
674 ->/root/kinginx/nginx
675 ->/root/kinginx/nginx
676 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6759 ->/root/kinginx/nginx
6760 ->/root/kinginx/nginx
6761 ->/root/kinginx/nginx
6763 ->/root/kinginx/nginx
6764 ->/root/kinginx/nginx
6766 ->/root/kinginx/nginx
6768 ->/root/kinginx/nginx
6771 ->/root/kinginx/nginx
6772 ->/root/kinginx/nginx
6774 ->/root/kinginx/nginx
6775 ->/root/kinginx/nginx
6776 ->/root/kinginx/nginx
6777 ->/root/kinginx/nginx
6778 ->/root/kinginx/nginx
6780 ->/root/kinginx/nginx
6781 ->/root/kinginx/nginx
6782 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
11526 ->/root/kinginx/nginx
11527 ->/root/kinginx/nginx
11528 ->/root/kinginx/nginx
11530 ->/root/kinginx/nginx
11531 ->/root/kinginx/nginx
11533 ->/root/kinginx/nginx
11535 ->/root/kinginx/nginx
11537 ->/root/kinginx/nginx
11539 ->/root/kinginx/nginx
11540 ->/root/kinginx/nginx
11542 ->/root/kinginx/nginx
11543 ->/root/kinginx/nginx
11544 ->/root/kinginx/nginx
11545 ->/root/kinginx/nginx
11546 ->/root/kinginx/nginx
11547 ->/root/kinginx/nginx
11549 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19051 ->/root/kinginx/nginx
19052 ->/root/kinginx/nginx
19053 ->/root/kinginx/nginx
19055 ->/root/kinginx/nginx
19056 ->/root/kinginx/nginx
19058 ->/root/kinginx/nginx
19060 ->/root/kinginx/nginx
19062 ->/root/kinginx/nginx
19064 ->/root/kinginx/nginx
19065 ->/root/kinginx/nginx
19067 ->/root/kinginx/nginx
19068 ->/root/kinginx/nginx
19069 ->/root/kinginx/nginx
19070 ->/root/kinginx/nginx
19071 ->/root/kinginx/nginx
19073 ->/root/kinginx/nginx
19075 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
29262 ->/root/kinginx/nginx
29263 ->/root/kinginx/nginx
29264 ->/root/kinginx/nginx
29266 ->/root/kinginx/nginx
29267 ->/root/kinginx/nginx
29269 ->/root/kinginx/nginx
29271 ->/root/kinginx/nginx
29273 ->/root/kinginx/nginx
29275 ->/root/kinginx/nginx
29276 ->/root/kinginx/nginx
29278 ->/root/kinginx/nginx
29279 ->/root/kinginx/nginx
29280 ->/root/kinginx/nginx
29281 ->/root/kinginx/nginx
29282 ->/root/kinginx/nginx
29283 ->/root/kinginx/nginx
29284 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
19029 ->/root/kinginx/nginx
19030 ->/root/kinginx/nginx
19031 ->/root/kinginx/nginx
19032 ->/root/kinginx/nginx
19034 ->/root/kinginx/nginx
19035 ->/root/kinginx/nginx
19037 ->/root/kinginx/nginx
19040 ->/root/kinginx/nginx
19042 ->/root/kinginx/nginx
19043 ->/root/kinginx/nginx
19045 ->/root/kinginx/nginx
19046 ->/root/kinginx/nginx
19047 ->/root/kinginx/nginx
19048 ->/root/kinginx/nginx
19049 ->/root/kinginx/nginx
19050 ->/root/kinginx/nginx
19052 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
21046 ->/root/kinginx/nginx
21047 ->/root/kinginx/nginx
21048 ->/root/kinginx/nginx
21050 ->/root/kinginx/nginx
21051 ->/root/kinginx/nginx
21053 ->/root/kinginx/nginx
21055 ->/root/kinginx/nginx
21058 ->/root/kinginx/nginx
21059 ->/root/kinginx/nginx
21061 ->/root/kinginx/nginx
21062 ->/root/kinginx/nginx
21063 ->/root/kinginx/nginx
21064 ->/root/kinginx/nginx
21065 ->/root/kinginx/nginx
21067 ->/root/kinginx/nginx
21068 ->/root/kinginx/nginx
21069 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
28372 ->/root/kinginx/nginx
28373 ->/root/kinginx/nginx
28374 ->/root/kinginx/nginx
28376 ->/root/kinginx/nginx
28377 ->/root/kinginx/nginx
28378 ->/root/kinginx/nginx
28380 ->/root/kinginx/nginx
28382 ->/root/kinginx/nginx
28385 ->/root/kinginx/nginx
28386 ->/root/kinginx/nginx
28387 ->/root/kinginx/nginx
28389 ->/root/kinginx/nginx
28390 ->/root/kinginx/nginx
28391 ->/root/kinginx/nginx
28392 ->/root/kinginx/nginx
28393 ->/root/kinginx/nginx
28394 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
28839 ->/root/kinginx/nginx
28840 ->/root/kinginx/nginx
28841 ->/root/kinginx/nginx
28843 ->/root/kinginx/nginx
28844 ->/root/kinginx/nginx
28845 ->/root/kinginx/nginx
28847 ->/root/kinginx/nginx
28849 ->/root/kinginx/nginx
28852 ->/root/kinginx/nginx
28853 ->/root/kinginx/nginx
28855 ->/root/kinginx/nginx
28856 ->/root/kinginx/nginx
28857 ->/root/kinginx/nginx
28858 ->/root/kinginx/nginx
28859 ->/root/kinginx/nginx
28861 ->/root/kinginx/nginx
28862 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
17698 ->/root/kinginx/nginx
17699 ->/root/kinginx/nginx
17700 ->/root/kinginx/nginx
17701 ->/root/kinginx/nginx
17703 ->/root/kinginx/nginx
17704 ->/root/kinginx/nginx
17705 ->/root/kinginx/nginx
17706 ->/root/kinginx/nginx
17707 ->/root/kinginx/nginx
17709 ->/root/kinginx/nginx
17711 ->/root/kinginx/nginx
17713 ->/root/kinginx/nginx
17715 ->/root/kinginx/nginx
17716 ->/root/kinginx/nginx
17718 ->/root/kinginx/nginx
17719 ->/root/kinginx/nginx
17720 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
2485 ->/root/kinginx/nginx
2486 ->/root/kinginx/nginx
2487 ->/root/kinginx/nginx
2489 ->/root/kinginx/nginx
2493 ->/root/kinginx/nginx
2495 ->/root/kinginx/nginx
2497 ->/root/kinginx/nginx
2500 ->/root/kinginx/nginx
2501 ->/root/kinginx/nginx
2502 ->/root/kinginx/nginx
2504 ->/root/kinginx/nginx
2505 ->/root/kinginx/nginx
2506 ->/root/kinginx/nginx
2507 ->/root/kinginx/nginx
2508 ->/root/kinginx/nginx
2509 ->/root/kinginx/nginx
2510 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
26446 ->/root/kinginx/nginx
26447 ->/root/kinginx/nginx
26448 ->/root/kinginx/nginx
26450 ->/root/kinginx/nginx
26451 ->/root/kinginx/nginx
26453 ->/root/kinginx/nginx
26455 ->/root/kinginx/nginx
26458 ->/root/kinginx/nginx
26459 ->/root/kinginx/nginx
26460 ->/root/kinginx/nginx
26462 ->/root/kinginx/nginx
26463 ->/root/kinginx/nginx
26464 ->/root/kinginx/nginx
26465 ->/root/kinginx/nginx
26466 ->/root/kinginx/nginx
26468 ->/root/kinginx/nginx
26469 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
15257 ->/root/kinginx/nginx
15258 ->/root/kinginx/nginx
15259 ->/root/kinginx/nginx
15261 ->/root/kinginx/nginx
15262 ->/root/kinginx/nginx
15264 ->/root/kinginx/nginx
15266 ->/root/kinginx/nginx
15269 ->/root/kinginx/nginx
15270 ->/root/kinginx/nginx
15271 ->/root/kinginx/nginx
15272 ->/root/kinginx/nginx
15274 ->/root/kinginx/nginx
15275 ->/root/kinginx/nginx
15276 ->/root/kinginx/nginx
15277 ->/root/kinginx/nginx
15278 ->/root/kinginx/nginx
15279 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16068 ->/root/kinginx/nginx
16069 ->/root/kinginx/nginx
16070 ->/root/kinginx/nginx
16072 ->/root/kinginx/nginx
16073 ->/root/kinginx/nginx
16075 ->/root/kinginx/nginx
16077 ->/root/kinginx/nginx
16080 ->/root/kinginx/nginx
16081 ->/root/kinginx/nginx
16083 ->/root/kinginx/nginx
16084 ->/root/kinginx/nginx
16085 ->/root/kinginx/nginx
16086 ->/root/kinginx/nginx
16087 ->/root/kinginx/nginx
16089 ->/root/kinginx/nginx
16091 ->/root/kinginx/nginx
16092 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
16838 ->/root/kinginx/nginx
16839 ->/root/kinginx/nginx
16840 ->/root/kinginx/nginx
16841 ->/root/kinginx/nginx
16843 ->/root/kinginx/nginx
16844 ->/root/kinginx/nginx
16846 ->/root/kinginx/nginx
16848 ->/root/kinginx/nginx
16851 ->/root/kinginx/nginx
16852 ->/root/kinginx/nginx
16853 ->/root/kinginx/nginx
16855 ->/root/kinginx/nginx
16856 ->/root/kinginx/nginx
16857 ->/root/kinginx/nginx
16858 ->/root/kinginx/nginx
16859 ->/root/kinginx/nginx
16860 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
23668 ->/root/kinginx/nginx
23669 ->/root/kinginx/nginx
23670 ->/root/kinginx/nginx
23672 ->/root/kinginx/nginx
23673 ->/root/kinginx/nginx
23675 ->/root/kinginx/nginx
23677 ->/root/kinginx/nginx
23680 ->/root/kinginx/nginx
23681 ->/root/kinginx/nginx
23683 ->/root/kinginx/nginx
23684 ->/root/kinginx/nginx
23685 ->/root/kinginx/nginx
23686 ->/root/kinginx/nginx
23687 ->/root/kinginx/nginx
23689 ->/root/kinginx/nginx
23690 ->/root/kinginx/nginx
23692 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6010 ->/root/kinginx/nginx
6011 ->/root/kinginx/nginx
6012 ->/root/kinginx/nginx
6014 ->/root/kinginx/nginx
6015 ->/root/kinginx/nginx
6017 ->/root/kinginx/nginx
6019 ->/root/kinginx/nginx
6021 ->/root/kinginx/nginx
6023 ->/root/kinginx/nginx
6024 ->/root/kinginx/nginx
6026 ->/root/kinginx/nginx
6027 ->/root/kinginx/nginx
6028 ->/root/kinginx/nginx
6029 ->/root/kinginx/nginx
6030 ->/root/kinginx/nginx
6031 ->/root/kinginx/nginx
6033 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
6272 ->/root/kinginx/nginx
6273 ->/root/kinginx/nginx
6274 ->/root/kinginx/nginx
6275 ->/root/kinginx/nginx
6277 ->/root/kinginx/nginx
6279 ->/root/kinginx/nginx
6281 ->/root/kinginx/nginx
6283 ->/root/kinginx/nginx
6285 ->/root/kinginx/nginx
6286 ->/root/kinginx/nginx
6287 ->/root/kinginx/nginx
6289 ->/root/kinginx/nginx
6290 ->/root/kinginx/nginx
6291 ->/root/kinginx/nginx
6292 ->/root/kinginx/nginx
6293 ->/root/kinginx/nginx
6294 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
29874 ->/root/kinginx/nginx
29875 ->/root/kinginx/nginx
29876 ->/root/kinginx/nginx
29877 ->/root/kinginx/nginx
29879 ->/root/kinginx/nginx
29880 ->/root/kinginx/nginx
29882 ->/root/kinginx/nginx
29884 ->/root/kinginx/nginx
29887 ->/root/kinginx/nginx
29888 ->/root/kinginx/nginx
29889 ->/root/kinginx/nginx
29891 ->/root/kinginx/nginx
29892 ->/root/kinginx/nginx
29893 ->/root/kinginx/nginx
29894 ->/root/kinginx/nginx
29895 ->/root/kinginx/nginx
29896 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
22293 ->/root/kinginx/nginx
22294 ->/root/kinginx/nginx
22295 ->/root/kinginx/nginx
22297 ->/root/kinginx/nginx
22298 ->/root/kinginx/nginx
22299 ->/root/kinginx/nginx
22301 ->/root/kinginx/nginx
22303 ->/root/kinginx/nginx
22306 ->/root/kinginx/nginx
22307 ->/root/kinginx/nginx
22308 ->/root/kinginx/nginx
22309 ->/root/kinginx/nginx
22311 ->/root/kinginx/nginx
22312 ->/root/kinginx/nginx
22313 ->/root/kinginx/nginx
22314 ->/root/kinginx/nginx
22315 ->/root/kinginx/nginx
remove service_status.txt successfully
remove fifo_file successfully
Nginx Stopped successfully.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
23146 ->/root/kinginx/nginx
23147 ->/root/kinginx/nginx
23148 ->/root/kinginx/nginx
23150 ->/root/kinginx/nginx
23151 ->/root/kinginx/nginx
23152 ->/root/kinginx/nginx
23154 ->/root/kinginx/nginx
23157 ->/root/kinginx/nginx
23159 ->/root/kinginx/nginx
23160 ->/root/kinginx/nginx
23161 ->/root/kinginx/nginx
23163 ->/root/kinginx/nginx
23164 ->/root/kinginx/nginx
23165 ->/root/kinginx/nginx
23166 ->/root/kinginx/nginx
23167 ->/root/kinginx/nginx
23168 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
27179 ->/root/kinginx/nginx
27180 ->/root/kinginx/nginx
27181 ->/root/kinginx/nginx
27182 ->/root/kinginx/nginx
27184 ->/root/kinginx/nginx
27185 ->/root/kinginx/nginx
27188 ->/root/kinginx/nginx
27191 ->/root/kinginx/nginx
27192 ->/root/kinginx/nginx
27193 ->/root/kinginx/nginx
27195 ->/root/kinginx/nginx
27196 ->/root/kinginx/nginx
27197 ->/root/kinginx/nginx
27198 ->/root/kinginx/nginx
27199 ->/root/kinginx/nginx
27200 ->/root/kinginx/nginx
27202 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
13437 ->/root/kinginx/nginx
13438 ->/root/kinginx/nginx
13439 ->/root/kinginx/nginx
13440 ->/root/kinginx/nginx
13442 ->/root/kinginx/nginx
13443 ->/root/kinginx/nginx
13445 ->/root/kinginx/nginx
13446 ->/root/kinginx/nginx
13448 ->/root/kinginx/nginx
13451 ->/root/kinginx/nginx
13452 ->/root/kinginx/nginx
13453 ->/root/kinginx/nginx
13455 ->/root/kinginx/nginx
13456 ->/root/kinginx/nginx
13457 ->/root/kinginx/nginx
13458 ->/root/kinginx/nginx
13459 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
5917 ->/root/kinginx/nginx
5918 ->/root/kinginx/nginx
5919 ->/root/kinginx/nginx
5920 ->/root/kinginx/nginx
5922 ->/root/kinginx/nginx
5923 ->/root/kinginx/nginx
5925 ->/root/kinginx/nginx
5928 ->/root/kinginx/nginx
5930 ->/root/kinginx/nginx
5931 ->/root/kinginx/nginx
5932 ->/root/kinginx/nginx
5934 ->/root/kinginx/nginx
5935 ->/root/kinginx/nginx
5936 ->/root/kinginx/nginx
5937 ->/root/kinginx/nginx
5938 ->/root/kinginx/nginx
5940 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
9868 ->/root/kinginx/nginx
9869 ->/root/kinginx/nginx
9870 ->/root/kinginx/nginx
9871 ->/root/kinginx/nginx
9873 ->/root/kinginx/nginx
9874 ->/root/kinginx/nginx
9876 ->/root/kinginx/nginx
9878 ->/root/kinginx/nginx
9880 ->/root/kinginx/nginx
9882 ->/root/kinginx/nginx
9883 ->/root/kinginx/nginx
9884 ->/root/kinginx/nginx
9885 ->/root/kinginx/nginx
9887 ->/root/kinginx/nginx
9888 ->/root/kinginx/nginx
9889 ->/root/kinginx/nginx
9890 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
17058 ->/root/kinginx/nginx
17059 ->/root/kinginx/nginx
17060 ->/root/kinginx/nginx
17061 ->/root/kinginx/nginx
17062 ->/root/kinginx/nginx
17064 ->/root/kinginx/nginx
17065 ->/root/kinginx/nginx
17067 ->/root/kinginx/nginx
17069 ->/root/kinginx/nginx
17072 ->/root/kinginx/nginx
17073 ->/root/kinginx/nginx
17075 ->/root/kinginx/nginx
17076 ->/root/kinginx/nginx
17077 ->/root/kinginx/nginx
17078 ->/root/kinginx/nginx
17080 ->/root/kinginx/nginx
17083 ->/root/kinginx/nginx
Nginx Already stoped.
nginx started successfully.
nginx version: nginx/1.12.2
built by gcc 4.1.2 20080704 (Red Hat 4.1.2-55)
built with OpenSSL 1.0.2l  25 May 2017
TLS SNI support enabled
configure arguments: --with-http_ssl_module --with-http_stub_status_module --add-module=../ngx_devel_kit --add-module=../set-misc-nginx-module --add-module=../ngx_http_call_kesb_module --with-openssl=../third_party_library/openssl-1.0.2l --add-module=../ngx_http_third_party_proxy_module --add-module=../nginx-upload-module

nginx workers num:17
8180 ->/root/kinginx/nginx
8181 ->/root/kinginx/nginx
8182 ->/root/kinginx/nginx
8183 ->/root/kinginx/nginx
8185 ->/root/kinginx/nginx
8186 ->/root/kinginx/nginx
8188 ->/root/kinginx/nginx
8189 ->/root/kinginx/nginx
8192 ->/root/kinginx/nginx
8194 ->/root/kinginx/nginx
8196 ->/root/kinginx/nginx
8197 ->/root/kinginx/nginx
8198 ->/root/kinginx/nginx
8199 ->/root/kinginx/nginx
8201 ->/root/kinginx/nginx
8202 ->/root/kinginx/nginx
8204 ->/root/kinginx/nginx
