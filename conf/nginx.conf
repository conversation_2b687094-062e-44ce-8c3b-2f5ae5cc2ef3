

user  root;
worker_processes  16;
worker_rlimit_nofile 100000;
master_process on;

#daemon off;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
#pid        logs/nginx.pid;

events {
    worker_connections  10000;
    multi_accept on;
    use epoll;
   accept_mutex off;
}



http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '$upstream_response_time $request_time '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;
    client_max_body_size 100m;

    sendfile        on;
    tcp_nopush     on;
    tcp_nodelay on; 

    #keepalive_timeout  0;
    keepalive_timeout  65;
    access_log off;
	error_log off;
    #gzip  on;
	upstream wkhservice_inner {
        server **************:8080 weight=1;
        server **************:8080 weight=1;
        server **************:8080 weight=1;
		server **************:8080 weight=1;
		keepalive 64;
   	}
   	upstream hualin_gateway{
     	server **********:8182 weight=2;
        #server **********:8182 weight=2;
		server ***********:8182 weight=1;
        server ***********:8182 weight=1;
        #server ***********:8182 weight=1;
		server ***********:8182 weight=5;
		server ***********:8182 weight=5;
		#server ***********:8182 weight=3;
        #server ***********:8182 weight=3;
		
		keepalive 64;
    }
   	upstream tencent_gateway{
        server ***********:34111;
		keepalive 64;
    }
 upstream tencent_ws{
	server ***********:34111;
	keepalive 64;
	} 
	upstream police_check {
        server **************:8080 weight=1;
        server **************:8080 weight=1;
   	}
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
	
	
    server {
               listen     9090;
               server_name  localhost;
               location / {
                    proxy_set_header Host  $host;
                    proxy_http_version 1.1;
					proxy_set_header connection "";
        
                    proxy_set_header X-Real-IP  $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    client_max_body_size 20m;
                    open_file_cache max=204800 inactive=30s;
                  # open_file_cache_valid 30s;
                  # open_file_cache_min_uses 1;
                    proxy_pass   http://wkhservice_inner;
                }
                error_page   500 502 503 504  /50x.html;
                    location = /50x.html {
                     root   html;
                }

    }
	server {
               listen     1010;
               server_name  localhost;
               location / {
                    proxy_set_header Host  $host;
                    proxy_http_version 1.1;
                    proxy_set_header X-Real-IP  $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    client_max_body_size 20m;
                    open_file_cache max=204800 inactive=30s;
                  # open_file_cache_valid 30s;
                  # open_file_cache_min_uses 1;
                    proxy_pass   http://police_check;
                }
                error_page   500 502 503 504  /50x.html;
                    location = /50x.html {
                     root   html;
                }

    }
    server {
        listen       80;
        listen			 443 ssl;
        
        server_name  localhost;

        #charset utf-8;

        #access_log  logs/host.access.log  main;
		ssl_certificate              server.pem;
    ssl_certificate_key          server.key;
    ssl_session_timeout          20m;
    ssl_protocols                TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers                 ALL:!ADH:!EXPORT56:RC4+RSA:+HIGH:+MEDIUM:+LOW:!SSLv2:+EXP;
    ssl_prefer_server_ciphers   on;  

	location /ws/broker {
            keepalive_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://hualin_gateway; 
        }

	location ~* /ngx_status {
	    stub_status on;
	    access_log off;
            allow 127.0.0.1;
	    allow *************/24;
            deny all;
	}

	
        location ~* /(kjdp_login_hl){
				proxy_http_version 1.1;
				proxy_set_header connection "";
        
                proxy_pass http://**************:8080;
                proxy_set_header X-Real-IP $remote_addr;
                client_max_body_size 100m;
        }
#        location ~* /(kjdp_login_szt){
#                proxy_pass http://************:8080;
#                proxy_set_header X-Real-IP $remote_addr;
#                client_max_body_size 100m;
#        }
 location ~* /(cgi-bin-hl)
         {
                proxy_http_version 1.1;
				proxy_set_header connection "";
        
				proxy_redirect off;
				proxy_set_header Host $host;
            		proxy_set_header X-Real-IP $remote_addr;
            		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            		proxy_pass http://hualin_gateway; 

          }
	
	location /cgi-bin/gateway.cgi {
			proxy_http_version 1.1;
			proxy_set_header connection "";
        
          proxy_redirect off;
          proxy_set_header Host $host;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             if ( $query_string !~* ^(.*)(reqtype=1|reqtype=2|reqtype=3|reqtype=4|reqtype=5|reqtype=6|reqtype=9|reqtype=12)(.*)$ )  { 
                 proxy_pass  http://tencent_gateway;
             }   
             proxy_pass http://hualin_gateway/cgi-bin-hl/gateway.fcgi;
         }   
  location /cgi-bin
         {   
         #*********** 34111 
		 proxy_http_version 1.1;
		proxy_set_header connection "";
        
                proxy_pass http://tencent_gateway;
                proxy_set_header X-Real-IP $remote_addr;
                client_max_body_size 100m;

          }   
 location /ws/ {
            keepalive_timeout 300;
            proxy_http_version 1.1;    
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_pass http://tencent_ws;
        }		  
	location ~* /(kesb_oldreq_legacy){
	    kesb_request;
	}
    location ~* /(kesb_req){
            python_cache on;
            python_set_param  node nginx36;
	    python_enter_point kesb_req_bpagent::kesb_req;
	}
#	location ~* /(����){

        location / {
            root /html;
            index index.html;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443;
    #    server_name  localhost;

    #    ssl                  on;
    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_timeout  5m;

    #    ssl_protocols  SSLv2 SSLv3 TLSv1;
    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers   on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
