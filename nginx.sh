#!/bin/sh

sAction=$1


#set Envirment Variables
scriptRealDir=$(cd `dirname $0`;pwd)
#echo RealDir:$scriptRealDir
#echo Setting Enviroment Variables.

export LD_LIBRARY_PATH=$scriptRealDir:$LD_LIBRARY_PATH
if [ -d $scriptRealDir/lib ]
then
   export PATH=$scriptRealDir/lib:$PATH
   export LD_LIBRARY_PATH=$scriptRealDir/lib:$LD_LIBRARY_PATH
   #echo PATH=$PATH
   #echo LD_LIBRARY_PATH=$LD_LIBRARY_PATH
fi


function ps_ex()
{
	#for pid in `ps -e | grep $1 | awk '{print $1}'` ;
  selfFileName=`basename $0`

  #ps -ef | grep nginx| grep -v $selfFileName|grep -v 'grep'| awk '{print $1,$2,$9}'
  for pid in `ps -ef | grep nginx| grep -v $selfFileName|grep -v 'grep'| awk '{print $2}'` ;
    do
       ls -l /proc/${pid}/exe &>/dev/null
       if [ "$?" == "0" ]
       then
          echo -n "${pid} "
          ls -l /proc/${pid}/exe | awk '{print $10 $11}'
       fi
    done
}

function nginxPSNum()
{
   #V_PS_NGINX_NUM=`ps -ef|grep -i nginx|grep -v 'grep'|grep -v 'nginx.sh'|wc -l`
   V_PS_NGINX_NUM=`ps_ex nginx|grep $scriptRealDir/|wc -l`
   return $V_PS_NGINX_NUM
}

function statusNginx()
{
  $scriptRealDir/nginx -V
  echo
  nginxPSNum
  echo nginx workers num:$?
  ps_ex nginx|grep $scriptRealDir/
}

function startNginx()
{ 
  nginxPSNum
  if [ "$?" == "0" ];then
     if [ ! -d "./logs" ]; then
        mkdir logs 
     fi 

     cd $scriptRealDir
     #$scriptRealDir/nginx -p $scriptRealDir -c $scriptRealDir/conf/nginx.conf
     $scriptRealDir/nginx -p $scriptRealDir
     nginxPSNum
     if [ "$?" == "0" ];then
        echo nginx starting failed,please check the log.
     else
        echo nginx started successfully.
        statusNginx
     fi
  else
     echo Nginx Already Started.
     statusNginx
  fi
  
}

function stopNginx()
{
  nginxPSNum
  if [ "$?" == "0" ];then
     echo Nginx Already stoped.
  else
     #$scriptRealDir/nginx -p $scriptRealDir -c $scriptRealDir/conf/nginx.conf -s stop
     $scriptRealDir/nginx -p $scriptRealDir -s stop
     sleep 5
     nginxPSNum
     if [ "$?" == "0" ];then
        rm -f service_status.txt
        echo remove service_status.txt successfully
        rm -f fifo_file_*
        echo remove fifo_file successfully
        echo Nginx Stopped successfully.
     else
        echo Nginx Stopping failed, There are nginx processes still running.
        statusNginx
     fi
  fi
}

if [ "$sAction" == "start" ];then
    startNginx
elif [ "$sAction" == "stop" ];then
    stopNginx
elif [ "$sAction" == "status" ];then
    statusNginx
elif [ "$sAction" == "restart" ];then
    stopNginx
    startNginx
else
    statusNginx
fi
