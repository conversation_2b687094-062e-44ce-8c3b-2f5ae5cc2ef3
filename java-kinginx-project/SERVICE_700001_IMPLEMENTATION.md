# 700001功能实现说明 - session换国金网厅token

## 功能概述

700001是一个session换国金网厅token的接口，对应Python项目中的`sessionExchangeToken`
函数。该功能将用户的session转换为国金网厅系统可用的token，实现单点登录功能。

## 业务流程

### 1. 完整业务流程

```
用户请求 → 参数验证 → session解密 → Redis票据验证 → 调用国金网厅API → 返回token
```

### 2. 详细步骤说明

#### Step 1: 参数验证

- 检查`session_id`参数是否存在且不为空
- 返回错误码`-7001`如果参数无效

#### Step 2: session解密

- 使用DES解密`session_id`
- 解密密钥为`se410301`
- 返回错误码`-7002`如果解密失败

#### Step 3: 解析session数据

- 将解密后的数据解析为JSON格式
- 提取`user_id`等关键信息
- 返回错误码`-7003`如果JSON格式错误

#### Step 4: Redis票据验证

- 构建Redis key: `USER_ID_CLS + COMPANY_ID + USER_ID`
- 从Redis中获取存储的票据信息
- 验证session是否匹配
- 返回错误码`-177`或`-178`如果票据无效

#### Step 5: 调用国金网厅API

- **开发环境**: 返回Mock数据
- **生产环境**: 调用真实的国金网厅API

#### Step 6: 处理op_station信息

- 从Redis中获取op_station信息
- 使用AES加密op_station
- 清理临时存储的数据

## 技术实现

### 1. 核心服务类

#### TokenExchangeService

```java

@Service
public class TokenExchangeService {
    /**
     * 700001 session换国金网厅token
     */
    public CompletableFuture<TokenExchangeResult> exchangeToken(String refRequest, Map<String, Object> reqData)
}
```

#### 主要方法

- `exchangeToken()`: 主要业务逻辑
- `decryptSession()`: session解密
- `extractPassword()`: 提取密码
- `callGJTokenService()`: 调用国金网厅API
- `generateMockTokenResponse()`: 生成Mock响应

### 2. 配置参数

#### application.yml配置

```yaml
kinginx:
  external:
    gj-token:
      global-url: "http://172.16.25.30:30000/yjbapi/sas/session/register"
      simple-url: "http://172.16.25.30:30000/yjbapi/sas/instant_token/apply"
      app-id: "qqstack"
      account-type: "1"
      mock-enabled: true  # 开发环境启用Mock数据
```

#### 配置说明

| 配置项            | 说明                   | 默认值     |
|----------------|----------------------|---------|
| `global-url`   | 获取durableToken的API地址 | 国金网厅地址  |
| `simple-url`   | 获取最终token的API地址      | 国金网厅地址  |
| `app-id`       | 应用ID                 | qqstack |
| `account-type` | 账户类型                 | 1       |
| `mock-enabled` | 是否启用Mock数据           | true    |

### 3. 加密解密功能

#### CryptoUtil增强

```java
/**
 * Session解密
 */
public String sessionDecrypt(String sessionId, String key)

/**
 * MD5加密
 */
public String md5Encrypt(String input)

/**
 * AES加密/解密
 */
public String aesEncrypt(String plainText, String key)

public String aesDecrypt(String encryptedData, String key)
```

### 4. 国金网厅API调用

#### 两步调用流程

```java
// Step 1: 获取durableToken
Map<String, Object> globalRequest = new HashMap<>();
globalRequest.put("app_id", appId);
globalRequest.put("account_type", accountType);
globalRequest.put("expired_time", 28800);
globalRequest.put("password", password);

String globalResponse = sendHttpRequest(globalUrl, globalRequest);

// Step 2: 获取最终token
Map<String, Object> simpleRequest = new HashMap<>();
simpleRequest.put("durable_token", durableToken);
simpleRequest.put("expired_time", 28800);

String simpleResponse = sendHttpRequest(simpleUrl, simpleRequest);
```

## Mock数据实现

### 1. Mock响应格式

```java
private TokenExchangeResult generateMockTokenResponse(String sessionId, String userId) {
    Map<String, Object> result = new HashMap<>();
    result.put("token", "mock_token_" + System.currentTimeMillis());
    result.put("expired_time", 28800);
    result.put("user_id", userId);
    result.put("app_id", appId);
    result.put("account_type", accountType);
    result.put("op_station", "mock_op_station_encrypted");

    return new TokenExchangeResult("0", "success", result);
}
```

### 2. Mock数据特点

- **真实性**: 包含所有必要的token信息
- **时效性**: 动态生成token值
- **完整性**: 包含op_station等扩展信息

## 错误码定义

| 错误码     | 错误信息                | 说明           |
|---------|---------------------|--------------|
| `0`     | success             | 成功           |
| `-7001` | [session_id]必传/不能为空 | 参数错误         |
| `-7002` | [session_id]解析失败    | session解密失败  |
| `-7003` | session数据格式错误       | JSON解析失败     |
| `-7004` | session中缺少user_id   | session数据不完整 |
| `-177`  | 票据已过期               | Redis中无票据    |
| `-178`  | 票据已过期               | 票据不匹配        |
| `-7100` | 系统异常                | 系统级异常        |
| `-7200` | 调用token服务失败         | 国金网厅API调用失败  |

## 请求响应示例

### 1. 请求格式

```json
{
  "REQUESTS": [
    {
      "REQ_MSG_HDR": {
        "SERVICE_ID": "700001",
        "TIME_STAMP": "1575335973",
        "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"
      },
      "REQ_COMM_DATA": {
        "session_id": "encrypted_session_data"
      }
    }
  ]
}
```

### 2. 成功响应

```json
{
  "ANSWERS": [
    {
      "ANS_MSG_HDR": {
        "MSG_CODE": "0",
        "MSG_LEVEL": "0",
        "MSG_TEXT": "success",
        "DEBUG_MSG": "",
        "SESSION": ""
      },
      "ANS_COMM_DATA": [
        {
          "token": "mock_token_1234567890",
          "expired_time": 28800,
          "user_id": "test_user",
          "app_id": "qqstack",
          "account_type": "1",
          "op_station": "encrypted_op_station_data"
        }
      ]
    }
  ]
}
```

### 3. 错误响应

```json
{
  "ANSWERS": [
    {
      "ANS_MSG_HDR": {
        "MSG_CODE": "-7001",
        "MSG_LEVEL": "0",
        "MSG_TEXT": "[session_id]必传",
        "DEBUG_MSG": "",
        "SESSION": ""
      },
      "ANS_COMM_DATA": null
    }
  ]
}
```

## 测试验证

### 1. 单元测试

```java
@Test
public void testTokenExchange700001() throws Exception {
    KinginxRequest request = createTokenExchangeRequest();
    String requestJson = objectMapper.writeValueAsString(request);
    
    mockMvc.perform(post("/api/trading/request")
            .contentType(MediaType.APPLICATION_JSON)
            .content(requestJson))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.ANSWERS").exists())
            .andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR").exists());
}
```

### 2. curl测试

```bash
curl -X POST http://localhost:8080/kinginx/api/trading/request \
  -H 'Content-Type: application/json' \
  -d '{
    "REQUESTS":[{
      "REQ_MSG_HDR":{
        "SERVICE_ID":"700001",
        "TIME_STAMP":"1575335973",
        "SIGN":"80D5A19FE849C9B0C99251C91ACB4A2D"
      },
      "REQ_COMM_DATA":{
        "session_id":"test_session_id_12345"
      }
    }]
  }'
```

### 3. 预期结果

- **开发环境**: 返回Mock token数据
- **生产环境**: 返回真实的国金网厅token

## 安全考虑

### 1. 数据加密

- **session解密**: 使用DES算法解密session
- **op_station加密**: 使用AES算法加密敏感信息
- **密钥管理**: 密钥通过配置文件管理

### 2. 票据验证

- **Redis验证**: 验证session在Redis中的有效性
- **时效性检查**: 检查票据是否过期
- **一致性验证**: 确保session与存储的一致

### 3. 日志安全

- **敏感信息屏蔽**: 日志中不记录完整的session和token
- **操作审计**: 记录关键操作的审计日志

## 扩展性

### 1. 支持多种token类型

```java
// 可以根据account_type返回不同类型的token
switch (accountType) {
    case "1": return getGeneralToken();
    case "2": return getVipToken();
    default: return getDefaultToken();
}
```

### 2. 支持token刷新

```java
// 可以添加token刷新功能
public TokenExchangeResult refreshToken(String oldToken) {
    // 实现token刷新逻辑
}
```

### 3. 支持多环境配置

```yaml
# 不同环境使用不同的国金网厅地址
spring:
  profiles:
    active: dev
---
spring:
  profiles: prod
kinginx:
  external:
    gj-token:
      mock-enabled: false
      global-url: "http://prod.gj.com/api/session/register"
```

## 总结

700001功能的实现特点：

✅ **完整的业务流程**: 从session验证到token生成的完整链路  
✅ **安全的加密机制**: DES session解密 + AES数据加密  
✅ **可靠的票据验证**: Redis票据存储和验证机制  
✅ **灵活的Mock支持**: 开发环境Mock + 生产环境真实调用  
✅ **完善的错误处理**: 详细的错误码和错误信息  
✅ **异步处理能力**: 使用CompletableFuture提升性能

该功能为国金网厅系统提供了安全可靠的单点登录能力！
