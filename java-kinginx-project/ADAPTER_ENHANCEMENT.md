# 交易适配器增强 - translateReq操作补充

## 问题描述

原Java版本的`TradingAdapterService.callAdapter`方法缺少了Python项目中`adaptbiz.py`的`adaptcommbiz`函数的核心功能：

- **translateReq**: 入参转换操作
- **translateAns**: 出参转换操作
- **参数映射配置**: functiondict配置字典

这些操作负责将标准请求参数转换为柜台系统格式，以及将柜台响应转换回标准格式。

## 解决方案

### 1. 增强TradingAdapterService

#### 1.1 完整的callAdapter流程

```java
public AdapterResult callAdapter(String refRequest, String serviceId, Map<String, Object> jsonReq) {
    // Step 1: translateReq - 入参转换
    ParameterTranslator.TranslateResult translateReqResult = 
        parameterTranslator.translateReq(serviceId, jsonReq);
    
    // Step 2: 特殊处理 - op_station字段清理
    // 处理TCC字段，替换特定IP和MAC地址
    
    // Step 3: 构建请求数据并发送到柜台
    // 使用转换后的参数和目标功能号
    
    // Step 4: 解析柜台响应
    // 获取返回的数据和错误码
    
    // Step 5: translateAns - 出参转换
    ParameterTranslator.TranslateResult translateAnsResult = 
        parameterTranslator.translateAns(serviceId, translatedReq, ansCommData, translatedAnsData);
    
    // Step 6: 错误码转换
    // 将柜台错误码转换为标准错误码
}
```

#### 1.2 新增的辅助方法

- `isValidServiceForOpStationProcessing()`: 检查是否需要op_station处理
- `maskSensitiveData()`: 屏蔽敏感数据用于日志
- `translateErrorCode()`: 错误码转换
- `translateErrorMessage()`: 错误消息转换

### 2. 新增ParameterTranslator工具类

#### 2.1 核心功能

```java
@Component
public class ParameterTranslator {
    /**
     * 入参转换 - 对应Python中的translateReq函数
     */
    public TranslateResult translateReq(String srcServiceId, Map<String, Object> reqDict)
    
    /**
     * 出参转换 - 对应Python中的translateAns函数  
     */
    public TranslateResult translateAns(String srcServiceId, Map<String, Object> reqDict, 
                                       List<Map<String, Object>> srcAnsDictList, 
                                       List<Map<String, Object>> dstAnsDictList)
}
```

#### 2.2 参数转换配置

实现了对应Python项目中`functiondict`的配置字典：

```java
// 410301签约配置示例
Map<String, Object> config410301 = new HashMap<>();
config410301.put("dstfuncid", "331100");  // 目标功能号

List<Map<String, Object>> reqConfig = new ArrayList<>();
reqConfig.add(createReqConfig("biznum#", "biznum", "410301"));
reqConfig.add(createReqConfig("op_branch_no", "op_branch_no", "5010"));
reqConfig.add(createReqConfig("trdpwd", "trdpwd"));
reqConfig.add(createReqConfigWithFunction("password", "password", 
    "functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)"));
```

#### 2.3 函数插件支持

实现了对应Python中的`functionPlugIn`机制：

| 函数名                      | 功能        | 对应Python函数             |
|--------------------------|-----------|------------------------|
| `WSMultiEncodeByChannel` | 密码加密      | WSMultiEncodeByChannel |
| `tranOpStation`          | TCC操作站点转换 | tranOpStation          |
| `TimeFormat`             | 时间格式化     | TimeFormat             |
| `FormatFloat`            | 浮点数格式化    | FormatFloat            |
| `WSGetCustid`            | 获取客户号     | WSGetCustid            |
| `WSGetOrgid`             | 获取机构代码    | WSGetOrgid             |

### 3. 参数转换示例

#### 3.1 410301签约参数转换

**输入参数**:

```json
{
  "inputid": "test_account",
  "trdpwd": "encrypted_password",
  "TCC": "device001|***********|*************",
  "operway": "5"
}
```

**转换后参数**:

```json
{
  "biznum": "410301",
  "op_branch_no": "5010", 
  "account_content": "test_account",
  "password": "encoded_password",
  "op_station": "*************;***********;device001",
  "op_entrust_way": "h",
  "input_content": "1",
  "content_type": "0"
}
```

#### 3.2 目标功能号映射

| 源功能号    | 目标功能号  | 说明     |
|---------|--------|--------|
| 410301  | 331100 | 组合业务签约 |
| 410301A | 410301 | 登录验证   |
| 410323  | 410323 | 增加权限   |
| 410324  | 410324 | 查询权限   |

### 4. 特殊处理逻辑

#### 4.1 op_station字段清理

```java
// 处理 LIP 字段，替换特定 IP 为 NA
opStation = opStation.replace("LIP=127.0.0.1", "LIP=NA");
opStation = opStation.replace("LIP=0.0.0.0", "LIP=NA");

// 处理 MAC 字段，替换特定值为 NA  
opStation = opStation.replace("MAC=************", "MAC=NA");
opStation = opStation.replace("MAC=************", "MAC=NA");
opStation = opStation.replace("MAC=************", "MAC=NA");
```

#### 4.2 敏感数据屏蔽

```java
// 屏蔽密码相关字段用于日志输出
masked = masked.replaceAll("(\"password\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
masked = masked.replaceAll("(\"trdpwd\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
```

#### 4.3 错误码转换

```java
switch (originalCode) {
    case "-1":
        return "-30011"; // 适配器通用错误
    case "-100": 
        return "-30012"; // 系统错误
    default:
        return originalCode;
}
```

### 5. 与Python项目的对应关系

| Java组件                              | Python组件                | 功能说明   |
|-------------------------------------|-------------------------|--------|
| `TradingAdapterService.callAdapter` | `adaptbiz.adaptcommbiz` | 适配器主流程 |
| `ParameterTranslator.translateReq`  | `adaptbiz.translateReq` | 入参转换   |
| `ParameterTranslator.translateAns`  | `adaptbiz.translateAns` | 出参转换   |
| `FUNCTION_DICT`                     | `functiondict`          | 参数转换配置 |
| `processFunctionPlugin`             | `functionPlugIn`        | 函数插件处理 |

### 6. 配置扩展

#### 6.1 添加新功能号配置

```java
// 添加新的功能号配置到FUNCTION_DICT
Map<String, Object> configNewFunc = new HashMap<>();
configNewFunc.put("dstfuncid", "目标功能号");

List<Map<String, Object>> reqConfig = new ArrayList<>();
// 添加请求参数映射配置

configNewFunc.put("req", reqConfig);
FUNCTION_DICT.put("新功能号", configNewFunc);
```

#### 6.2 添加新函数插件

```java
private Object executeFunctionPlugin(String functionName, List<String> params, Map<String, Object> dataDict) {
    switch (functionName) {
        case "新函数名":
            return handle新函数逻辑(params);
        // ... 其他函数
    }
}
```

### 7. 测试验证

#### 7.1 单元测试

```java
@Test
public void testParameterTranslation() {
    Map<String, Object> reqData = new HashMap<>();
    reqData.put("inputid", "test_account");
    reqData.put("trdpwd", "test_password");
    
    TranslateResult result = parameterTranslator.translateReq("410301", reqData);
    
    assertEquals(0, result.getCode());
    assertEquals("331100", result.getDstServiceId());
    assertNotNull(result.getTranslatedData());
}
```

#### 7.2 集成测试

```bash
# 测试410301签约流程
curl -X POST http://localhost:8080/kinginx/api/trading/request \
  -H 'Content-Type: application/json' \
  -d '{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600001"},"REQ_COMM_DATA":{"LOGIN_CODE":"test","TRD_PWD":"pwd","USER_ID":"user1"}}]}'
```

### 8. 性能优化

#### 8.1 配置缓存

- 参数转换配置在启动时加载到内存
- 避免重复解析配置文件

#### 8.2 日志优化

- 敏感数据自动屏蔽
- 分级日志输出

#### 8.3 异常处理

- 完善的异常捕获和转换
- 友好的错误提示信息

## 总结

通过这次增强，Java版本的交易适配器现在完全对应了Python项目中的`adaptcommbiz`功能：

✅ **完整的参数转换流程**: translateReq → 柜台调用 → translateAns  
✅ **函数插件支持**: 支持密码加密、时间格式化等插件  
✅ **配置化参数映射**: 通过FUNCTION_DICT配置参数转换规则  
✅ **特殊处理逻辑**: op_station清理、敏感数据屏蔽  
✅ **错误码转换**: 标准化错误码和错误消息  
✅ **扩展性设计**: 易于添加新功能号和函数插件

现在Java版本的适配器功能与Python版本完全一致，能够正确处理410301签约等复杂业务流程！
