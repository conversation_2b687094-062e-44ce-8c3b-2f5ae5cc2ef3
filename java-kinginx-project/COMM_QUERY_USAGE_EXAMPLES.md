# handleCommQuery 使用示例

## 概述

`handleCommQuery` 方法是通用查询处理器，对应Python项目中的 `pyreqbpagentCommquery` 和 `adaptcommbiz` 函数。它负责处理各种查询类业务，包括资金查询、持仓查询、委托查询等。

## 核心流程

1. **参数验证**：检查SERVICE_ID是否存在
2. **数据提取**：获取REQ_COMM_DATA业务数据
3. **适配器调用**：通过TradingAdapterService调用适配器
4. **参数转换**：执行translateReq入参转换
5. **柜台通信**：发送请求到交易柜台
6. **响应处理**：解析柜台响应并执行translateAns出参转换
7. **结果返回**：构建标准响应格式

## 支持的查询类型

### 1. 资金查询 (600120)

```json
{
  "SERVICE_ID": "600120",
  "REQ_COMM_DATA": {
    "ACCOUNT": "********",
    "USER_ID": "testuser",
    "USER_ID_CLS": "1",
    "COMPANY_ID": "15900"
  }
}
```

**响应示例**：
```json
{
  "code": "0",
  "message": "查询成功",
  "body": "",
  "session": "",
  "data": [
    {
      "FUND_BALANCE": "10000.00",
      "AVAILABLE_BALANCE": "8000.00",
      "FROZEN_BALANCE": "2000.00",
      "CURRENCY": "CNY"
    }
  ]
}
```

### 2. 持仓查询 (600200)

```json
{
  "SERVICE_ID": "600200",
  "REQ_COMM_DATA": {
    "ACCOUNT": "********",
    "USER_ID": "testuser",
    "STOCK_CODE": "",
    "MARKET": ""
  }
}
```

**响应示例**：
```json
{
  "code": "0",
  "message": "查询成功",
  "body": "",
  "session": "",
  "data": [
    {
      "STOCK_CODE": "000001",
      "STOCK_NAME": "平安银行",
      "CURRENT_AMOUNT": "1000",
      "AVAILABLE_AMOUNT": "1000",
      "COST_PRICE": "12.50",
      "MARKET_VALUE": "13500.00"
    },
    {
      "STOCK_CODE": "600036",
      "STOCK_NAME": "招商银行",
      "CURRENT_AMOUNT": "500",
      "AVAILABLE_AMOUNT": "500",
      "COST_PRICE": "45.20",
      "MARKET_VALUE": "23600.00"
    }
  ]
}
```

### 3. 委托查询 (600160)

```json
{
  "SERVICE_ID": "600160",
  "REQ_COMM_DATA": {
    "ACCOUNT": "********",
    "USER_ID": "testuser",
    "START_DATE": "********",
    "END_DATE": "********"
  }
}
```

### 4. 历史成交查询 (600180)

```json
{
  "SERVICE_ID": "600180",
  "REQ_COMM_DATA": {
    "ACCOUNT": "********",
    "USER_ID": "testuser",
    "START_DATE": "********",
    "END_DATE": "********",
    "STOCK_CODE": ""
  }
}
```

### 5. 新股查询 (600050)

```json
{
  "SERVICE_ID": "600050",
  "REQ_COMM_DATA": {
    "ACCOUNT": "********",
    "USER_ID": "testuser",
    "QUERY_TYPE": "1"
  }
}
```

## 错误处理

### 1. 参数错误

```json
{
  "code": "-1",
  "message": "请求参数错误：缺少SERVICE_ID",
  "body": "",
  "session": ""
}
```

### 2. 适配器错误

```json
{
  "code": "-30011",
  "message": "adapt功能600120入参转换失败:必要参数ACCOUNT不能为空",
  "body": "",
  "session": ""
}
```

### 3. 柜台错误

```json
{
  "code": "-1001",
  "message": "柜台连接失败",
  "body": "",
  "session": ""
}
```

### 4. 系统错误

```json
{
  "code": "-9999",
  "message": "通用查询处理异常: 网络超时",
  "body": "",
  "session": ""
}
```

## 在Controller中的使用

```java
@RestController
@RequestMapping("/api/trading")
public class TradingController {

    @Autowired
    private RequestHandlerService requestHandlerService;

    @PostMapping("/query")
    public KinginxResponse handleQuery(@RequestBody KinginxRequest request) {
        String refRequest = generateRequestId();
        
        // 构建查询请求数据
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", request.getServiceId());
        reqData.put("REQ_COMM_DATA", request.getReqCommData());
        
        // 调用通用查询处理器
        return requestHandlerService.handleCommQuery(refRequest, reqData);
    }
}
```

## 适配器配置示例

对于每个查询功能，需要在适配器配置中定义参数转换规则：

```java
// 600120 资金查询配置示例
Map<String, Object> fundQueryConfig = new HashMap<>();
fundQueryConfig.put("dstfuncid", "331300"); // 目标柜台功能号

List<Map<String, Object>> reqConfig = new ArrayList<>();
reqConfig.add(createReqConfig("ACCOUNT", "fund_account"));
reqConfig.add(createReqConfig("USER_ID", "client_id"));
reqConfig.add(createReqConfig("op_branch_no#", "op_branch_no", "5010"));
reqConfig.add(createReqConfig("op_entrust_way#", "op_entrust_way", "h"));

fundQueryConfig.put("req", reqConfig);

// 出参转换配置
List<Map<String, Object>> ansConfig = new ArrayList<>();
ansConfig.add(createAnsConfig("fund_balance", "FUND_BALANCE"));
ansConfig.add(createAnsConfig("enable_balance", "AVAILABLE_BALANCE"));
ansConfig.add(createAnsConfig("frozen_balance", "FROZEN_BALANCE"));

fundQueryConfig.put("ans", ansConfig);
```

## 性能优化建议

1. **缓存机制**：对于频繁查询的数据，可以考虑添加Redis缓存
2. **异步处理**：对于耗时较长的查询，可以考虑异步处理
3. **分页查询**：对于大量数据的查询，实现分页机制
4. **连接池**：优化柜台连接池配置，提高并发处理能力

## 监控和日志

系统会自动记录以下关键信息：

1. **请求日志**：记录查询请求的参数和refRequest
2. **适配器日志**：记录参数转换过程和柜台通信
3. **响应日志**：记录查询结果和处理时间
4. **错误日志**：记录异常信息和错误堆栈

## 扩展新的查询功能

要添加新的查询功能，需要：

1. **配置适配器**：在ParameterTranslator中添加参数转换配置
2. **更新映射**：在service-handler-mapping.yml中添加服务映射
3. **测试验证**：编写单元测试验证功能正确性

## 注意事项

1. **参数验证**：确保必要参数不为空
2. **错误处理**：妥善处理各种异常情况
3. **日志记录**：记录关键操作和错误信息
4. **性能监控**：监控查询响应时间和成功率
5. **安全考虑**：对敏感数据进行脱敏处理
