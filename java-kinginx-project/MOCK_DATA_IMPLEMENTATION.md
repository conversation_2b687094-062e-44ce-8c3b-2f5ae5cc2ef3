# Mock数据实现说明

## 背景

在开发环境中，直接调用柜台系统（t2Client）不太方便，因此实现了Mock数据功能，模拟柜台系统的响应数据，便于开发和测试。

## 实现方案

### 1. 环境判断机制

#### 1.1 配置文件控制

在`application.yml`中添加了Mock开关：

```yaml
kinginx:
  external:
    trading-gateway:
      url: "http://10.10.0.75:8182"
      timeout: 30000
      mock-enabled: true  # 开发环境启用Mock数据
```

#### 1.2 环境检查逻辑

```java
private boolean isDevEnvironment() {
    // 优先使用配置文件中的mock-enabled设置
    if (mockEnabled) {
        return true;
    }
    
    // 其次检查环境变量
    String profile = System.getProperty("spring.profiles.active", "dev");
    return "dev".equals(profile) || "test".equals(profile);
}
```

### 2. 请求路由机制

#### 2.1 统一请求入口

```java
private String sendRequestToTrading(String serviceId, String requestJson) {
    // 开发环境使用Mock数据
    if (isDevEnvironment()) {
        return generateMockResponse(serviceId, requestJson);
    }
    
    // 生产环境调用t2Client
    return sendHttpRequestToTrading(requestJson);
}
```

#### 2.2 生产环境预留接口

```java
/**
 * 生产环境HTTP请求到交易网关 (实际应该调用t2Client)
 */
private String sendHttpRequestToTrading(String requestJson) {
    // 这里应该调用t2Client，目前使用HTTP请求作为示例
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
        HttpPost httpPost = new HttpPost(tradingGatewayUrl);
        httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
        httpPost.setEntity(new StringEntity(requestJson, StandardCharsets.UTF_8));

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        }
    } catch (IOException e) {
        logger.error("调用交易网关异常", e);
        throw new RuntimeException("调用交易网关失败: " + e.getMessage());
    }
}
```

### 3. Mock数据生成

#### 3.1 功能号路由

```java
private String generateMockResponse(String serviceId, String requestJson) {
    switch (serviceId) {
        case "331100": // 410301签约对应的目标功能号
            return generateMock331100Response(requestData);
        case "410301": // 410301A登录验证
            return generateMock410301Response(requestData);
        case "410323": // 增加权限
            return generateMock410323Response(requestData);
        case "410324": // 查询权限
            return generateMock410324Response(requestData);
        default:
            return generateDefaultMockResponse(serviceId);
    }
}
```

#### 3.2 具体Mock数据实现

##### 331100 (410301签约) Mock数据

```java
private String generateMock331100Response(Map<String, Object> requestData) {
    // 返回包含深A和沪A股东账号的签约成功响应
    Map<String, Object> response = new HashMap<>();
    List<Map<String, Object>> answers = new ArrayList<>();
    Map<String, Object> answer = new HashMap<>();
    
    // 响应头
    Map<String, Object> ansMsgHdr = new HashMap<>();
    ansMsgHdr.put("MSG_CODE", "0");
    ansMsgHdr.put("MSG_TEXT", "签约成功");
    ansMsgHdr.put("MSG_LEVEL", "0");
    answer.put("ANS_MSG_HDR", ansMsgHdr);
    
    // 响应数据 - 深A股东账号
    List<Map<String, Object>> ansCommData = new ArrayList<>();
    Map<String, Object> dataRow = new HashMap<>();
    dataRow.put("fundid", "test_fund_001");
    dataRow.put("custid", "test_cust_001");
    dataRow.put("custname", "测试客户");
    dataRow.put("orgid", "5010");
    dataRow.put("asset_prop", "0"); // 普通证券账户
    dataRow.put("market", "0"); // 深A
    dataRow.put("secuid", "0000001111");
    ansCommData.add(dataRow);
    
    // 沪A股东账号
    Map<String, Object> dataRow2 = new HashMap<>();
    dataRow2.put("fundid", "test_fund_001");
    dataRow2.put("custid", "test_cust_001");
    dataRow2.put("custname", "测试客户");
    dataRow2.put("orgid", "5010");
    dataRow2.put("asset_prop", "0");
    dataRow2.put("market", "1"); // 沪A
    dataRow2.put("secuid", "1111000000");
    ansCommData.add(dataRow2);
    
    answer.put("ANS_COMM_DATA", ansCommData);
    answers.add(answer);
    response.put("ANSWERS", answers);
    
    return JSON.toJSONString(response);
}
```

##### 410301 (登录验证) Mock数据

```java
private String generateMock410301Response(Map<String, Object> requestData) {
    // 返回登录验证成功响应
    String operway = (String) requestData.get("operway");
    
    // 构建包含客户信息和权限的响应
    Map<String, Object> dataRow = new HashMap<>();
    dataRow.put("fundid", "test_fund_001");
    dataRow.put("custid", "test_cust_001");
    dataRow.put("custname", "测试客户");
    dataRow.put("orgid", "5010");
    dataRow.put("operway", operway);
    
    // ... 构建完整响应
}
```

##### 410324 (查询权限) Mock数据

```java
private String generateMock410324Response(Map<String, Object> requestData) {
    // 返回权限查询响应，默认没有e权限
    Map<String, Object> dataRow = new HashMap<>();
    dataRow.put("name", "测试客户");
    dataRow.put("fundid", "test_fund_001");
    dataRow.put("operway", "5"); // 默认没有e权限，需要增加
    
    // ... 构建完整响应
}
```

##### 410323 (增加权限) Mock数据

```java
private String generateMock410323Response(Map<String, Object> requestData) {
    // 返回权限增加成功响应
    Map<String, Object> dataRow = new HashMap<>();
    dataRow.put("result", "success");

    // ... 构建完整响应
}
```

### 4. Mock数据特点

#### 4.1 真实性

- **数据结构**: 完全符合柜台系统的响应格式
- **字段内容**: 包含所有必要的业务字段
- **业务逻辑**: 模拟真实的业务场景

#### 4.2 一致性

- **响应格式**: 统一的ANSWERS结构
- **错误处理**: 标准的错误码和错误消息
- **数据类型**: 与真实系统保持一致

#### 4.3 可配置性

- **环境控制**: 通过配置文件控制是否启用Mock
- **数据定制**: 可以根据请求参数返回不同的Mock数据
- **扩展性**: 易于添加新功能号的Mock数据

### 5. 业务场景覆盖

#### 5.1 410301签约流程

1. **Step 1**: operway=5验证身份 → 返回客户基本信息
2. **Step 2**: 410324查询权限 → 返回当前权限状态(默认无e权限)
3. **Step 3**: 410323增加权限 → 返回权限增加成功
4. **Step 4**: operway=e确认开通 → 返回最终签约成功

#### 5.2 数据关联性

- **客户信息**: 各步骤返回的客户信息保持一致
- **权限状态**: 模拟权限从无到有的变化过程
- **股东账号**: 返回深A和沪A两个市场的股东账号

### 6. 错误场景模拟

#### 6.1 异常处理

```java
private String generateErrorMockResponse(String serviceId, String errorMessage) {
    Map<String, Object> ansMsgHdr = new HashMap<>();
    ansMsgHdr.put("MSG_CODE", "-1");
    ansMsgHdr.put("MSG_TEXT", "Mock数据生成异常: " + errorMessage);
    ansMsgHdr.put("MSG_LEVEL", "0");

    // ... 构建错误响应
}
```

#### 6.2 业务错误模拟

可以通过修改Mock数据来模拟各种业务错误场景：

- 客户不存在
- 权限不足
- 账户状态异常
- 系统繁忙等

### 7. 使用方式

#### 7.1 开发环境

```yaml
# application.yml
kinginx:
  external:
    trading-gateway:
      mock-enabled: true  # 启用Mock数据
```

#### 7.2 生产环境

```yaml
# application-prod.yml
kinginx:
  external:
    trading-gateway:
      mock-enabled: false  # 禁用Mock数据，使用真实t2Client
```

#### 7.3 测试验证

```bash
# 测试410301签约流程
curl -X POST http://localhost:8080/kinginx/api/trading/request \
  -H 'Content-Type: application/json' \
  -d '{
    "REQUESTS":[{
      "REQ_MSG_HDR":{"SERVICE_ID":"600001"},
      "REQ_COMM_DATA":{
        "LOGIN_CODE":"test_account",
        "TRD_PWD":"test_password",
        "USER_ID":"test_user",
        "USER_ID_CLS":"1",
        "COMPANY_ID":"15900"
      }
    }]
  }'
```

### 8. 扩展指南

#### 8.1 添加新功能号Mock数据

```java
// 在generateMockResponse方法中添加新的case
case "新功能号":
    return generateMock新功能号Response(requestData);

// 实现具体的Mock数据生成方法
private String generateMock新功能号Response(Map<String, Object> requestData) {
    // 实现具体的Mock逻辑
}
```

#### 8.2 定制Mock数据

```java
// 可以根据请求参数返回不同的Mock数据
String accountContent = (String) requestData.get("account_content");
if ("error_account".equals(accountContent)) {
    // 返回错误响应
    return generateErrorResponse("账户不存在");
} else {
    // 返回正常响应
    return generateSuccessResponse(accountContent);
}
```

## 总结

通过实现Mock数据功能，我们实现了：

✅ **开发便利性**: 无需依赖真实柜台系统即可进行开发测试  
✅ **数据真实性**: Mock数据完全符合真实系统的响应格式  
✅ **业务完整性**: 覆盖了410301签约的完整业务流程  
✅ **配置灵活性**: 通过配置文件控制Mock开关  
✅ **扩展性**: 易于添加新功能号的Mock数据  
✅ **生产就绪**: 预留了t2Client调用接口

这为开发和测试提供了极大的便利，同时保证了与真实系统的兼容性！
