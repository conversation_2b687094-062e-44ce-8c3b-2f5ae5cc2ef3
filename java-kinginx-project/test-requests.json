{"description": "Kinginx Trading Service 测试请求示例", "base_url": "http://localhost:8080/kinginx/api/trading", "requests": {"health_check": {"method": "GET", "url": "/health", "description": "健康检查接口"}, "sign_contract_600001": {"method": "POST", "url": "/request", "description": "410301签约功能测试", "headers": {"Content-Type": "application/json"}, "body": {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "600001", "TIME_STAMP": "**********", "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"}, "REQ_COMM_DATA": {"LOGIN_CODE": "test_account_001", "TRD_PWD": "encrypted_test_password_123", "USER_ID": "test_user_001", "USER_ID_CLS": "1", "COMPANY_ID": "15900", "LOGIN_TYPE": "2", "TCC": "test_device_001|***********|*************", "XID_SESSION": "test_xid_session_123"}}]}}, "login_600010": {"method": "POST", "url": "/request", "description": "登录功能测试", "headers": {"Content-Type": "application/json"}, "body": {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "600010", "TIME_STAMP": "**********", "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"}, "REQ_COMM_DATA": {"LOGIN_CODE": "test_account_001", "USER_PWD": "encrypted_test_password_123", "USER_ID": "test_user_001", "USER_ID_CLS": "1", "COMPANY_ID": "15900", "LOGIN_TYPE": "2", "TCC": "test_device_001|***********|*************"}}]}}, "unsign_600002": {"method": "POST", "url": "/request", "description": "华林解约功能测试", "headers": {"Content-Type": "application/json"}, "body": {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "600002", "TIME_STAMP": "**********", "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"}, "REQ_COMM_DATA": {"USER_ID": "test_user_002", "USER_ID_CLS": "1", "COMPANY_ID": "12900"}}]}}, "account_trace_600003": {"method": "POST", "url": "/request", "description": "账户留痕功能测试", "headers": {"Content-Type": "application/json"}, "body": {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "600003", "TIME_STAMP": "**********", "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"}, "REQ_COMM_DATA": {"USER_ID": "test_user_003", "USER_ID_CLS": "1", "COMPANY_ID": "15900", "ACCOUNT": "test_account_003", "TCC": "test_device_003|***********|*************"}}]}}, "token_exchange_700001": {"method": "POST", "url": "/request", "description": "session换国金网厅token功能测试", "headers": {"Content-Type": "application/json"}, "body": {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "700001", "TIME_STAMP": "**********", "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"}, "REQ_COMM_DATA": {"session_id": "test_session_id_12345"}}]}}, "invalid_service": {"method": "POST", "url": "/request", "description": "无效功能号测试", "headers": {"Content-Type": "application/json"}, "body": {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "999999", "TIME_STAMP": "**********", "SIGN": "80D5A19FE849C9B0C99251C91ACB4A2D"}, "REQ_COMM_DATA": {"TEST_PARAM": "test_value"}}]}}}, "curl_examples": {"health_check": "curl -X GET http://localhost:8080/kinginx/api/trading/health", "sign_contract": "curl -X POST http://localhost:8080/kinginx/api/trading/request -H 'Content-Type: application/json' -d '{\"REQUESTS\":[{\"REQ_MSG_HDR\":{\"SERVICE_ID\":\"600001\",\"TIME_STAMP\":\"**********\",\"SIGN\":\"80D5A19FE849C9B0C99251C91ACB4A2D\"},\"REQ_COMM_DATA\":{\"LOGIN_CODE\":\"test_account_001\",\"TRD_PWD\":\"encrypted_test_password_123\",\"USER_ID\":\"test_user_001\",\"USER_ID_CLS\":\"1\",\"COMPANY_ID\":\"15900\",\"LOGIN_TYPE\":\"2\",\"TCC\":\"test_device_001|***********|*************\"}}]}'", "login": "curl -X POST http://localhost:8080/kinginx/api/trading/request -H 'Content-Type: application/json' -d '{\"REQUESTS\":[{\"REQ_MSG_HDR\":{\"SERVICE_ID\":\"600010\",\"TIME_STAMP\":\"**********\",\"SIGN\":\"80D5A19FE849C9B0C99251C91ACB4A2D\"},\"REQ_COMM_DATA\":{\"LOGIN_CODE\":\"test_account_001\",\"USER_PWD\":\"encrypted_test_password_123\",\"USER_ID\":\"test_user_001\",\"USER_ID_CLS\":\"1\",\"COMPANY_ID\":\"15900\",\"LOGIN_TYPE\":\"2\"}}]}'", "unsign": "curl -X POST http://localhost:8080/kinginx/api/trading/request -H 'Content-Type: application/json' -d '{\"REQUESTS\":[{\"REQ_MSG_HDR\":{\"SERVICE_ID\":\"600002\",\"TIME_STAMP\":\"**********\",\"SIGN\":\"80D5A19FE849C9B0C99251C91ACB4A2D\"},\"REQ_COMM_DATA\":{\"USER_ID\":\"test_user_002\",\"USER_ID_CLS\":\"1\",\"COMPANY_ID\":\"12900\"}}]}'", "account_trace": "curl -X POST http://localhost:8080/kinginx/api/trading/request -H 'Content-Type: application/json' -d '{\"REQUESTS\":[{\"REQ_MSG_HDR\":{\"SERVICE_ID\":\"600003\",\"TIME_STAMP\":\"**********\",\"SIGN\":\"80D5A19FE849C9B0C99251C91ACB4A2D\"},\"REQ_COMM_DATA\":{\"USER_ID\":\"test_user_003\",\"USER_ID_CLS\":\"1\",\"COMPANY_ID\":\"15900\",\"ACCOUNT\":\"test_account_003\",\"TCC\":\"test_device_003|***********|*************\"}}]}'", "token_exchange": "curl -X POST http://localhost:8080/kinginx/api/trading/request -H 'Content-Type: application/json' -d '{\"REQUESTS\":[{\"REQ_MSG_HDR\":{\"SERVICE_ID\":\"700001\",\"TIME_STAMP\":\"**********\",\"SIGN\":\"80D5A19FE849C9B0C99251C91ACB4A2D\"},\"REQ_COMM_DATA\":{\"session_id\":\"test_session_id_12345\"}}]}'"}, "response_examples": {"success_response": {"ANSWERS": [{"ANS_MSG_HDR": {"MSG_CODE": "0", "MSG_LEVEL": "0", "MSG_TEXT": "签约成功", "DEBUG_MSG": "", "SESSION": "abc123def456ghi789"}, "ANS_COMM_DATA": [{"ONLINE_FLAG": "0"}]}]}, "error_response": {"ANSWERS": [{"ANS_MSG_HDR": {"MSG_CODE": "-101", "MSG_LEVEL": "0", "MSG_TEXT": "该资金账号已经绑定了另一个微信号", "DEBUG_MSG": "", "SESSION": ""}, "ANS_COMM_DATA": null}]}}, "notes": {"service_ids": {"600001": "华林微证券签约接口", "600002": "华林微证券解约接口", "600003": "账户留痕接口", "600010": "bpagent登录接口", "700001": "session换国金网厅token接口"}, "user_id_cls": {"0": "客户代码", "1": "一般渠道", "2": "自选股", "Q": "全户通渠道"}, "company_ids": {"15900": "国金证券", "12900": "华林证券"}, "login_types": {"1": "客户代码", "2": "资金账号", "3": "深A股东", "4": "深B股东", "5": "沪A股东", "6": "沪B股东"}}}