# 配置修改示例

## 如何添加新的服务映射

### 1. 修改YAML配置文件

编辑 `src/main/resources/service-handler-mapping.yml`，添加新的服务映射：

```yaml
service-handler-mapping:
  # 现有配置...
  
  # 新增服务映射
  "800999": "handleNewService"                    # 新服务示例
  "900999": "handleAnotherService"                # 另一个新服务
```

### 2. 在RequestHandlerService中实现对应方法

在 `src/main/java/com/kinginx/service/RequestHandlerService.java` 中添加对应的处理方法：

```java
/**
 * 处理800999新服务请求
 */
public KinginxResponse handleNewService(String refRequest, Map<String, Object> reqData) {
    logger.info("处理800999新服务请求, refRequest: {}", refRequest);
    
    // 参数验证
    if (reqData.get("required_param") == null) {
        throw new KinginxException("-1", "必要参数不能为空");
    }
    
    // 业务逻辑处理
    try {
        // TODO: 实现具体业务逻辑
        
        return new KinginxResponse("0", "处理成功", "", "");
    } catch (Exception e) {
        logger.error("处理800999新服务请求异常, refRequest: {}", refRequest, e);
        throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, 
            "新服务处理异常: " + e.getMessage(), e);
    }
}

/**
 * 处理900999另一个新服务请求
 */
public KinginxResponse handleAnotherService(String refRequest, Map<String, Object> reqData) {
    logger.info("处理900999另一个新服务请求, refRequest: {}", refRequest);
    // TODO: 实现另一个新服务的逻辑
    return new KinginxResponse("0", "另一个新服务功能待实现", "", "");
}
```

### 3. 重启应用

重启应用后，系统会自动：
1. 读取新的YAML配置
2. 验证新添加的方法是否存在
3. 初始化方法缓存
4. 输出验证结果和统计信息

### 4. 测试新服务

发送测试请求：

```bash
curl -X POST http://localhost:8080/kinginx/api/trading/request \
  -H "Content-Type: application/json" \
  -d '{
    "SERVICE_ID": "800999",
    "REQ_COMM_DATA": {
      "required_param": "test_value"
    }
  }'
```

## 如何修改现有服务映射

### 1. 修改YAML配置

如果需要将某个服务ID映射到不同的处理方法：

```yaml
service-handler-mapping:
  # 原来的映射
  # "600001": "handle600001Sign"
  
  # 修改后的映射
  "600001": "handleNewSignMethod"
```

### 2. 实现新的处理方法

```java
/**
 * 新的签约处理方法
 */
public KinginxResponse handleNewSignMethod(String refRequest, Map<String, Object> reqData) {
    logger.info("使用新的签约处理方法, refRequest: {}", refRequest);
    // 新的业务逻辑
    return new KinginxResponse("0", "新签约方法处理成功", "", "");
}
```

### 3. 保留旧方法（可选）

如果需要保持向后兼容，可以保留旧的处理方法，或者让新方法调用旧方法：

```java
public KinginxResponse handleNewSignMethod(String refRequest, Map<String, Object> reqData) {
    // 添加新的逻辑
    logger.info("执行新的前置处理逻辑");
    
    // 调用原有方法
    return handle600001Sign(refRequest, reqData);
}
```

## 配置验证输出示例

应用启动时会看到类似的验证输出：

```
2024-01-15 10:30:15.123 [main] INFO  c.k.u.ServiceMappingValidator - 开始验证服务映射配置...
2024-01-15 10:30:15.125 [main] INFO  c.k.u.ServiceMappingValidator - 服务映射配置验证完成，共验证 113 个服务映射
2024-01-15 10:30:15.126 [main] INFO  c.k.u.ServiceMappingValidator - 处理方法使用统计:
2024-01-15 10:30:15.127 [main] INFO  c.k.u.ServiceMappingValidator -   handleCommQuery -> 89 个服务
2024-01-15 10:30:15.128 [main] INFO  c.k.u.ServiceMappingValidator -   handle600001Sign -> 1 个服务
2024-01-15 10:30:15.129 [main] INFO  c.k.u.ServiceMappingValidator -   handle600002Unsign -> 1 个服务
...
```

## 常见错误和解决方案

### 1. 方法不存在错误

```
Handler method not found: handleNonExistentMethod for service: 999999
```

**解决方案**：在RequestHandlerService中实现对应的方法。

### 2. 方法签名错误

```
服务 999999 的处理方法 handleWrongSignature 返回类型不是 KinginxResponse
```

**解决方案**：确保方法签名为 `public KinginxResponse methodName(String refRequest, Map<String, Object> reqData)`。

### 3. YAML格式错误

```
Cannot create property=service-handler-mapping for JavaBean=...
```

**解决方案**：检查YAML文件的格式，确保缩进正确，使用空格而不是制表符。

## 最佳实践

1. **命名规范**：处理方法名应该清晰地表达其功能，建议使用 `handle + 服务ID + 功能描述` 的格式
2. **文档更新**：添加新服务时，同时更新相关文档
3. **测试覆盖**：为新添加的处理方法编写单元测试
4. **日志记录**：在处理方法中添加适当的日志记录
5. **异常处理**：统一的异常处理和错误码返回
6. **参数验证**：对输入参数进行必要的验证
