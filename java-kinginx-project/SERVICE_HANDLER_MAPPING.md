# 服务处理器映射配置说明

## 概述

本文档说明了Java版本的Kinginx交易系统中的服务处理器映射配置，该配置对应Python项目中的`bpagentspd.py`文件中的`g_reqBpagentFunctionDict`字典。

## 配置文件

### 1. service-handler-mapping.yml

位置：`src/main/resources/service-handler-mapping.yml`

这是主要的配置文件，以YAML格式定义服务ID到处理方法的映射关系。便于后续修改和维护。

### 2. ServiceHandlerMappingProperties.java

位置：`src/main/java/com/kinginx/config/ServiceHandlerMappingProperties.java`

配置属性类，负责从YAML文件中读取映射配置。

### 3. ServiceHandler.java

位置：`src/main/java/com/kinginx/service/ServiceHandler.java`

函数式接口，定义统一的服务处理方法签名。

### 4. ServiceHandlerConfig.java

位置：`src/main/java/com/kinginx/config/ServiceHandlerConfig.java`

该配置类负责：
1. 从YAML文件读取服务ID到处理方法的映射关系
2. 在应用启动时初始化方法引用缓存（使用函数式方法引用替代反射）
3. 提供处理器查找和服务支持检查功能

## 映射规则

### 1. 签约解约相关
- `600001` → `handle600001Sign` - 签约接口
- `600002` → `handle600002Unsign` - 解约接口  
- `600003` → `handle600003AccountTrace` - 账户留痕接口
- `600010` → `handle600010Login` - 登录接口
- `900010` → `handle900010ResetPassword` - 重置/修改密码接口
- `600011` → `handle600011FundPasswordLogin` - 资金密码登录接口

### 2. 通用查询接口
大部分查询类接口都映射到 `handleCommQuery` 方法，对应Python中的 `pyreqbpagentCommquery` 函数：

- `600140` → `handleCommQuery` - 委托下单
- `600141` → `handleCommQuery` - 委托撤单
- `600120` → `handleCommQuery` - 资金查询
- `600200` → `handleCommQuery` - 持仓查询
- `600160` → `handleCommQuery` - 当日委托查询
- `600170` → `handleCommQuery` - 历史委托查询
- 等等...

### 3. 特殊处理接口
- `600040` → `handle600040UserInfo` - 用户信息查询接口（对应Python中的pyreqbpagent600040）
- `600401` → `handle600401ExtendSession` - 延长会话

### 4. 公告相关
- `600090` → `handle600090InsertNotice` - 插入券商公告
- `600091` → `handle600091SelectNotice` - 查询公告
- `600400` → `handle600400SelectNoticeTx` - 腾讯查询公告
- `600098` → `handle600098DeleteNotice` - 删除券商公告
- `600099` → `handle600099UpdateNotice` - 修改券商公告

### 5. Token相关
- `700001` → `handle700001TokenExchange` - session换国金网厅token

### 6. 测试接口
- `testforsession` → `handleTestForSession` - 测试会话
- `getclientip` → `handleGetClientIp` - 获取客户IP信息
- `GetNginxInfo` → `handleGetNginxInfo` - 获取应答字典残留应答
- `ClearNginxXpAns` → `handleClearNginxXpAns` - 清空残留应答

### 7. 测试环境专用接口
- `400001` → `handle400001QueryAccount` - 查询客户号
- `400002` → `handle400002QueryUserId` - 查询腾讯ID
- `400020` → `handle400020CheckSmsCode` - 查询验证码

### 8. 新客理财相关
- `800060` → `handleCommQuery` - 查询TA账户信息
- `800049` → `handleCommQuery` - 开通TA账户
- `800063` → `handleCommQuery` - 理财产品适当性匹配查询
- `800064` → `handleCommQuery` - 签署接口
- `800410` → `handleCommQuery` - 查询新客理财产品信息
- `600171` → `handleCommQuery` - 持仓记录查询
- `600173` → `handleCommQuery` - 持仓记录查询2
- `800140` → `handleCommQuery` - 新增理财委托接口
- `800061` → `handleCommQuery` - 协议留痕接口
- `800160` → `handleCommQuery` - 委托记录查询接口

## 使用方式

### 1. 在Controller中使用

```java
@Autowired
private ServiceHandlerConfig serviceHandlerConfig;

// 检查服务是否支持
if (!serviceHandlerConfig.isServiceSupported(serviceId)) {
    return new KinginxResponse("-1", "不支持的功能号: " + serviceId, "", "");
}

// 获取处理器
ServiceHandler handler = serviceHandlerConfig.getServiceHandler(serviceId);

// 直接调用处理方法（无需反射）
return handler.handle(refRequest, reqCommData);
```

### 2. 添加新的服务映射

1. 在 `service-handler-mapping.yml` 中添加新的映射关系
2. 在 `RequestHandlerService` 中实现对应的处理方法
3. 确保方法签名为：`public KinginxResponse methodName(String refRequest, Map<String, Object> reqData)`
4. 重启应用，系统会自动验证新的映射配置

## 优势

1. **外部化配置**：映射关系存储在YAML文件中，无需重新编译即可修改
2. **配置化管理**：所有服务映射集中在一个配置文件中，便于维护
3. **高性能调用**：使用函数式方法引用替代反射，显著提升调用性能
4. **编译时安全**：方法引用在编译时验证，避免运行时方法不存在的错误
5. **类型安全**：在应用启动时验证所有映射的处理器是否存在
6. **易于扩展**：添加新服务只需要在配置中添加映射关系并实现对应方法
7. **与Python版本对应**：完全对应Python项目中的功能号映射关系
8. **自动验证**：启动时自动验证配置的完整性和正确性
9. **无反射开销**：避免了反射调用的性能损耗和安全问题

## 配置验证

系统提供了 `ServiceMappingValidator` 组件，在应用启动时自动验证配置：

1. **方法存在性检查**：验证所有配置的处理方法是否存在
2. **方法签名检查**：验证方法参数和返回类型是否正确
3. **统计信息输出**：显示各处理方法的使用频率
4. **错误报告**：详细报告配置错误和警告信息

## 注意事项

1. 所有处理方法必须遵循统一的方法签名：`public KinginxResponse methodName(String refRequest, Map<String, Object> reqData)`
2. 方法名必须与YAML配置中的映射关系一致
3. 应用启动时会验证所有映射的方法是否存在，如果不存在会抛出异常
4. 新增服务时需要同时更新YAML配置和实现对应的处理方法
5. YAML文件修改后需要重启应用才能生效
6. 建议在开发环境中启用DEBUG日志级别以查看详细的验证信息
