# 测试环境配置
spring:
  # 使用H2内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

  # Redis配置 - 测试时禁用
  redis:
    host: localhost
    port: 6379
    password:
    timeout: 1000ms

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

# 日志配置
logging:
  level:
    com.kinginx: DEBUG
    org.springframework: WARN
    org.hibernate: WARN

# 自定义配置
kinginx:
  # 交易适配器配置
  trading:
    adapter:
      timeout: 5000
      retry-count: 1

  # 会话配置  
  session:
    expire-time: 300  # 5分钟

  # 公司配置
  company:
    default-id: "15900"

  # 加密配置 - 测试时禁用
  encryption:
    rsa:
      private-key-path: ""
      public-key-path: ""
    des:
      key: "test_des_key_123456"

  # 外部服务 - 测试时使用Mock
  external:
    trading-gateway:
      url: "http://mock-gateway:8182"
      timeout: 5000
