package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Method;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务处理器配置测试
 */
@SpringBootTest
public class ServiceHandlerConfigTest {

    @Autowired
    private ServiceHandlerConfig serviceHandlerConfig;

    @Autowired
    private RequestHandlerService requestHandlerService;

    @Test
    public void testAllServiceMappingsHaveCorrespondingMethods() {
        Map<String, String> mappings = serviceHandlerConfig.getAllServiceMappings();
        
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String serviceId = entry.getKey();
            String methodName = entry.getValue();
            
            // 检查方法是否存在
            Method method = serviceHandlerConfig.getHandlerMethod(serviceId);
            assertNotNull(method, "Service " + serviceId + " should have corresponding method " + methodName);
            
            // 检查方法名是否匹配
            assertEquals(methodName, method.getName(), 
                "Method name should match for service " + serviceId);
            
            // 检查方法参数
            Class<?>[] paramTypes = method.getParameterTypes();
            assertEquals(2, paramTypes.length, 
                "Handler method should have 2 parameters for service " + serviceId);
            assertEquals(String.class, paramTypes[0], 
                "First parameter should be String for service " + serviceId);
            assertEquals(Map.class, paramTypes[1], 
                "Second parameter should be Map for service " + serviceId);
        }
    }

    @Test
    public void testServiceSupported() {
        // 测试支持的服务
        assertTrue(serviceHandlerConfig.isServiceSupported("600001"));
        assertTrue(serviceHandlerConfig.isServiceSupported("600002"));
        assertTrue(serviceHandlerConfig.isServiceSupported("700001"));

        // 测试不支持的服务
        assertFalse(serviceHandlerConfig.isServiceSupported("999999"));
        assertFalse(serviceHandlerConfig.isServiceSupported("invalid"));
    }

    @Test
    public void testGetHandlerMethod() {
        // 测试获取处理方法
        Method method = serviceHandlerConfig.getHandlerMethod("600001");
        assertNotNull(method);
        assertEquals("handle600001Sign", method.getName());
        
        // 测试不存在的服务
        Method nonExistentMethod = serviceHandlerConfig.getHandlerMethod("999999");
        assertNull(nonExistentMethod);
    }
}
