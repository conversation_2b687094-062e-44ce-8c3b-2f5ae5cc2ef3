package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import com.kinginx.service.ServiceHandler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务处理器配置测试
 */
@SpringBootTest
public class ServiceHandlerConfigTest {

    @Autowired
    private ServiceHandlerConfig serviceHandlerConfig;

    @Autowired
    private RequestHandlerService requestHandlerService;

    @Test
    public void testAllServiceMappingsHaveCorrespondingHandlers() {
        Map<String, String> mappings = serviceHandlerConfig.getAllServiceMappings();

        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String serviceId = entry.getKey();
            String methodName = entry.getValue();

            // 检查处理器是否存在
            ServiceHandler handler = serviceHandlerConfig.getServiceHandler(serviceId);
            assertNotNull(handler, "Service " + serviceId + " should have corresponding handler " + methodName);
        }
    }

    @Test
    public void testServiceSupported() {
        // 测试支持的服务
        assertTrue(serviceHandlerConfig.isServiceSupported("600001"));
        assertTrue(serviceHandlerConfig.isServiceSupported("600002"));
        assertTrue(serviceHandlerConfig.isServiceSupported("700001"));

        // 测试不支持的服务
        assertFalse(serviceHandlerConfig.isServiceSupported("999999"));
        assertFalse(serviceHandlerConfig.isServiceSupported("invalid"));
    }

    @Test
    public void testGetServiceHandler() {
        // 测试获取处理器
        ServiceHandler handler = serviceHandlerConfig.getServiceHandler("600001");
        assertNotNull(handler);

        // 测试不存在的服务
        ServiceHandler nonExistentHandler = serviceHandlerConfig.getServiceHandler("999999");
        assertNull(nonExistentHandler);
    }

    @Test
    public void testHandlerExecution() {
        // 测试处理器执行
        ServiceHandler handler = serviceHandlerConfig.getServiceHandler("600001");
        assertNotNull(handler);

        // 可以进一步测试处理器的执行（需要mock数据）
        // KinginxResponse response = handler.handle("test-ref", new HashMap<>());
        // assertNotNull(response);
    }
}
