package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import com.kinginx.service.ServiceHandler;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 方法引用注册器测试
 */
@SpringBootTest
public class MethodReferenceRegistryTest {

    @Autowired
    private RequestHandlerService requestHandlerService;

    @Test
    public void testRegistryInitialization() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        // 测试注册器初始化
        assertTrue(registry.getMethodReferenceCount() > 0);
        
        // 测试基本方法引用是否存在
        assertTrue(registry.containsMethodReference("handle600001Sign"));
        assertTrue(registry.containsMethodReference("handleCommQuery"));
        assertTrue(registry.containsMethodReference("handle700001TokenExchange"));
    }

    @Test
    public void testGetMethodReference() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        // 测试获取存在的方法引用
        ServiceHandler handler = registry.getMethodReference("handle600001Sign");
        assertNotNull(handler);
        
        // 测试获取不存在的方法引用
        ServiceHandler nonExistentHandler = registry.getMethodReference("nonExistentMethod");
        assertNull(nonExistentHandler);
    }

    @Test
    public void testGetAllMethodReferences() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        Map<String, ServiceHandler> allReferences = registry.getAllMethodReferences();
        assertNotNull(allReferences);
        assertTrue(allReferences.size() > 0);
        
        // 验证返回的是副本，修改不会影响原始数据
        int originalSize = allReferences.size();
        allReferences.clear();
        assertEquals(originalSize, registry.getMethodReferenceCount());
    }

    @Test
    public void testGetAllMethodNames() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        Set<String> methodNames = registry.getAllMethodNames();
        assertNotNull(methodNames);
        assertTrue(methodNames.size() > 0);
        
        // 验证包含预期的方法名
        assertTrue(methodNames.contains("handle600001Sign"));
        assertTrue(methodNames.contains("handleCommQuery"));
    }

    @Test
    public void testDynamicAddMethodReference() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        int originalCount = registry.getMethodReferenceCount();
        
        // 动态添加方法引用
        ServiceHandler testHandler = (refRequest, reqData) -> null;
        registry.addMethodReference("testMethod", testHandler);
        
        // 验证添加成功
        assertEquals(originalCount + 1, registry.getMethodReferenceCount());
        assertTrue(registry.containsMethodReference("testMethod"));
        assertEquals(testHandler, registry.getMethodReference("testMethod"));
    }

    @Test
    public void testRemoveMethodReference() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        // 添加一个测试方法引用
        ServiceHandler testHandler = (refRequest, reqData) -> null;
        registry.addMethodReference("testMethod", testHandler);
        assertTrue(registry.containsMethodReference("testMethod"));
        
        // 移除方法引用
        boolean removed = registry.removeMethodReference("testMethod");
        assertTrue(removed);
        assertFalse(registry.containsMethodReference("testMethod"));
        
        // 尝试移除不存在的方法引用
        boolean notRemoved = registry.removeMethodReference("nonExistentMethod");
        assertFalse(notRemoved);
    }

    @Test
    public void testClearAllMethodReferences() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        assertTrue(registry.getMethodReferenceCount() > 0);
        
        // 清空所有方法引用
        registry.clearAllMethodReferences();
        
        assertEquals(0, registry.getMethodReferenceCount());
        assertFalse(registry.containsMethodReference("handle600001Sign"));
    }

    @Test
    public void testValidateAllMethodReferences() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        // 这个测试主要验证验证方法不会抛出异常
        assertDoesNotThrow(() -> registry.validateAllMethodReferences());
    }

    @Test
    public void testOverwriteMethodReference() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        // 获取原始处理器
        ServiceHandler originalHandler = registry.getMethodReference("handle600001Sign");
        assertNotNull(originalHandler);
        
        // 覆盖方法引用
        ServiceHandler newHandler = (refRequest, reqData) -> null;
        registry.addMethodReference("handle600001Sign", newHandler);
        
        // 验证覆盖成功
        ServiceHandler currentHandler = registry.getMethodReference("handle600001Sign");
        assertEquals(newHandler, currentHandler);
        assertNotEquals(originalHandler, currentHandler);
    }

    @Test
    public void testMethodReferenceConsistency() {
        MethodReferenceRegistry registry = new MethodReferenceRegistry(requestHandlerService);
        
        // 验证方法引用的一致性
        Map<String, ServiceHandler> allReferences = registry.getAllMethodReferences();
        Set<String> allMethodNames = registry.getAllMethodNames();
        
        assertEquals(allReferences.size(), allMethodNames.size());
        assertEquals(allReferences.size(), registry.getMethodReferenceCount());
        
        for (String methodName : allMethodNames) {
            assertTrue(allReferences.containsKey(methodName));
            assertNotNull(registry.getMethodReference(methodName));
        }
    }
}
