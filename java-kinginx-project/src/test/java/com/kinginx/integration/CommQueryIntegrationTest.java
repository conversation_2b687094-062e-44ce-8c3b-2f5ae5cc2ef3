package com.kinginx.integration;

import com.kinginx.common.dto.KinginxRequest;
import com.kinginx.common.dto.KinginxResponse;
import com.kinginx.controller.TradingController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 通用查询功能集成测试
 * 测试从Controller到Service的完整流程
 */
@SpringBootTest
@ActiveProfiles("test")
public class CommQueryIntegrationTest {

    @Autowired
    private TradingController tradingController;

    @Test
    public void testFundQuery_600120() {
        // 准备资金查询请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600120");
        
        Map<String, Object> reqCommData = new HashMap<>();
        reqCommData.put("ACCOUNT", "********");
        reqCommData.put("USER_ID", "testuser");
        reqCommData.put("USER_ID_CLS", "1");
        reqCommData.put("COMPANY_ID", "15900");
        request.setReqCommData(reqCommData);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        assertNotNull(response.getCode());
        assertNotNull(response.getMessage());
        
        // 在测试环境下，应该返回Mock数据或者适当的错误信息
        System.out.println("资金查询响应: " + response);
    }

    @Test
    public void testPositionQuery_600200() {
        // 准备持仓查询请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600200");
        
        Map<String, Object> reqCommData = new HashMap<>();
        reqCommData.put("ACCOUNT", "********");
        reqCommData.put("USER_ID", "testuser");
        reqCommData.put("USER_ID_CLS", "1");
        reqCommData.put("COMPANY_ID", "15900");
        reqCommData.put("STOCK_CODE", "");
        reqCommData.put("MARKET", "");
        request.setReqCommData(reqCommData);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        assertNotNull(response.getCode());
        assertNotNull(response.getMessage());
        
        System.out.println("持仓查询响应: " + response);
    }

    @Test
    public void testOrderQuery_600160() {
        // 准备委托查询请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600160");
        
        Map<String, Object> reqCommData = new HashMap<>();
        reqCommData.put("ACCOUNT", "********");
        reqCommData.put("USER_ID", "testuser");
        reqCommData.put("USER_ID_CLS", "1");
        reqCommData.put("COMPANY_ID", "15900");
        reqCommData.put("START_DATE", "********");
        reqCommData.put("END_DATE", "********");
        request.setReqCommData(reqCommData);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        assertNotNull(response.getCode());
        assertNotNull(response.getMessage());
        
        System.out.println("委托查询响应: " + response);
    }

    @Test
    public void testInvalidServiceId() {
        // 准备无效的服务ID请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("999999"); // 不存在的服务ID
        
        Map<String, Object> reqCommData = new HashMap<>();
        reqCommData.put("ACCOUNT", "********");
        request.setReqCommData(reqCommData);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        assertEquals("-1", response.getCode());
        assertTrue(response.getMessage().contains("不支持的功能号"));
        
        System.out.println("无效服务ID响应: " + response);
    }

    @Test
    public void testMissingRequiredParameters() {
        // 准备缺少必要参数的请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600120");
        
        Map<String, Object> reqCommData = new HashMap<>();
        // 故意不设置ACCOUNT等必要参数
        reqCommData.put("USER_ID", "testuser");
        request.setReqCommData(reqCommData);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        // 应该返回参数错误
        assertNotEquals("0", response.getCode());
        
        System.out.println("缺少参数响应: " + response);
    }

    @Test
    public void testEmptyReqCommData() {
        // 准备空的业务数据请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600120");
        request.setReqCommData(new HashMap<>());

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        // 应该返回参数错误或者适配器错误
        assertNotEquals("0", response.getCode());
        
        System.out.println("空业务数据响应: " + response);
    }

    @Test
    public void testNullReqCommData() {
        // 准备null业务数据请求
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600120");
        request.setReqCommData(null);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应
        assertNotNull(response);
        // handleCommQuery应该能处理null的REQ_COMM_DATA
        assertNotNull(response.getCode());
        
        System.out.println("null业务数据响应: " + response);
    }

    @Test
    public void testMultipleQueries() {
        // 测试多个查询请求的处理
        String[] serviceIds = {"600120", "600200", "600160"};
        
        for (String serviceId : serviceIds) {
            KinginxRequest request = new KinginxRequest();
            request.setServiceId(serviceId);
            
            Map<String, Object> reqCommData = new HashMap<>();
            reqCommData.put("ACCOUNT", "********");
            reqCommData.put("USER_ID", "testuser");
            reqCommData.put("USER_ID_CLS", "1");
            reqCommData.put("COMPANY_ID", "15900");
            
            if ("600160".equals(serviceId)) {
                reqCommData.put("START_DATE", "********");
                reqCommData.put("END_DATE", "********");
            }
            
            request.setReqCommData(reqCommData);

            // 执行请求
            KinginxResponse response = tradingController.handleRequest(request, null);

            // 验证响应
            assertNotNull(response, "服务" + serviceId + "响应不能为null");
            assertNotNull(response.getCode(), "服务" + serviceId + "响应码不能为null");
            assertNotNull(response.getMessage(), "服务" + serviceId + "响应消息不能为null");
            
            System.out.println("服务" + serviceId + "响应: " + response);
        }
    }

    @Test
    public void testResponseFormat() {
        // 测试响应格式的一致性
        KinginxRequest request = new KinginxRequest();
        request.setServiceId("600120");
        
        Map<String, Object> reqCommData = new HashMap<>();
        reqCommData.put("ACCOUNT", "********");
        reqCommData.put("USER_ID", "testuser");
        request.setReqCommData(reqCommData);

        // 执行请求
        KinginxResponse response = tradingController.handleRequest(request, null);

        // 验证响应格式
        assertNotNull(response);
        assertNotNull(response.getCode());
        assertNotNull(response.getMessage());
        assertNotNull(response.getBody()); // body字段应该存在，即使为空
        assertNotNull(response.getSession()); // session字段应该存在，即使为空
        
        // data字段可能为null（无数据时）或者List（有数据时）
        if (response.getData() != null) {
            assertTrue(response.getData() instanceof java.util.List, 
                "data字段应该是List类型");
        }
        
        System.out.println("响应格式验证通过: " + response);
    }
}
