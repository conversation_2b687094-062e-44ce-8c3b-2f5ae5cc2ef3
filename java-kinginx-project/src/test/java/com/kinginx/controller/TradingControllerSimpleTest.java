package com.kinginx.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 交易控制器简单集成测试类
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(
        properties = {"spring.datasource.url=jdbc:h2:mem:testdb", "spring.datasource.driver-class-name=org.h2.Driver",
                "spring.jpa.hibernate.ddl-auto=create-drop",
                "kinginx.external.trading-gateway.url=http://mock-gateway:8182", "spring.redis.host=localhost",
                "spring.redis.port=6379", "spring.redis.password=", "kinginx.encryption.rsa.private-key-path=",
                "kinginx.encryption.rsa.public-key-path="})
public class TradingControllerSimpleTest {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testHealthCheck() {
        String url = "http://localhost:" + port + "/kinginx/api/trading/health";

        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("UP", response.getBody().get("status"));
        assertEquals("kinginx-trading-service", response.getBody().get("service"));
    }

    @Test
    public void testInvalidServiceId() throws Exception {
        String url = "http://localhost:" + port + "/kinginx/api/trading/request";

        // 构建无效功能号请求
        KinginxRequest request = createInvalidServiceRequest();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<KinginxRequest> entity = new HttpEntity<>(request, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());

        // 检查响应格式
        assertTrue(response.getBody().containsKey("ANSWERS"));
        List<Map> answers = (List<Map>)response.getBody().get("ANSWERS");
        assertFalse(answers.isEmpty());

        Map<String, Object> answerHeader = (Map<String, Object>)answers.get(0).get("ANS_MSG_HDR");
        assertEquals("-1", answerHeader.get("MSG_CODE"));
    }

    @Test
    public void testRequestValidation() throws Exception {
        String url = "http://localhost:" + port + "/kinginx/api/trading/request";

        // 构建空请求
        Map<String, Object> emptyRequest = new HashMap<>();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map> entity = new HttpEntity<>(emptyRequest, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());

        // 应该返回参数验证错误
        List<Map> answers = (List<Map>)response.getBody().get("ANSWERS");
        assertFalse(answers.isEmpty());

        Map<String, Object> answerHeader = (Map<String, Object>)answers.get(0).get("ANS_MSG_HDR");
        assertEquals(ServiceConstants.ERROR_CODE_SYSTEM, answerHeader.get("MSG_CODE"));
    }

    @Test
    public void testSignContractRequestFormat() throws Exception {
        String url = "http://localhost:" + port + "/kinginx/api/trading/request";

        // 构建签约请求（会因为缺少外部依赖而失败，但可以测试请求格式）
        KinginxRequest request = createSignContractRequest();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<KinginxRequest> entity = new HttpEntity<>(request, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());

        // 检查响应格式正确
        assertTrue(response.getBody().containsKey("ANSWERS"));
        List<Map> answers = (List<Map>)response.getBody().get("ANSWERS");
        assertFalse(answers.isEmpty());

        Map<String, Object> answerHeader = (Map<String, Object>)answers.get(0).get("ANS_MSG_HDR");
        assertNotNull(answerHeader.get("MSG_CODE"));
        assertNotNull(answerHeader.get("MSG_TEXT"));
    }

    private KinginxRequest createSignContractRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId(ServiceConstants.SERVICE_600001);
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("LOGIN_CODE", "test_account");
        reqData.put("TRD_PWD", "encrypted_password");
        reqData.put("USER_ID", "test_user_id");
        reqData.put("USER_ID_CLS", ServiceConstants.USER_ID_CLS_GENERAL);
        reqData.put("COMPANY_ID", ServiceConstants.COMPANY_ID_GUOJIN);
        reqData.put("LOGIN_TYPE", ServiceConstants.LOGIN_TYPE_FUND_ACCOUNT);
        reqData.put("TCC", "test_tcc_info");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }

    private KinginxRequest createInvalidServiceRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId("999999"); // 无效功能号
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("TEST_PARAM", "test_value");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }
}
