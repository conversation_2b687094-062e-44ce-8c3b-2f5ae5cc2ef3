package com.kinginx.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxRequest;
import com.kinginx.service.SignContractService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 交易控制器测试类
 */
@WebMvcTest(TradingController.class)
public class TradingControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private SignContractService signContractService;

    @Test
    public void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/trading/health")).andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.service").value("kinginx-trading-service"));
    }

    @Test
    public void testSignContract600001() throws Exception {
        // 构建签约请求
        KinginxRequest request = createSignContractRequest();

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/trading/request").contentType(MediaType.APPLICATION_JSON).content(requestJson))
                .andExpect(status().isOk()).andExpect(jsonPath("$.ANSWERS").exists())
                .andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR").exists());
    }

    @Test
    public void testLogin600010() throws Exception {
        // 构建登录请求
        KinginxRequest request = createLoginRequest();

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/trading/request").contentType(MediaType.APPLICATION_JSON).content(requestJson))
                .andExpect(status().isOk()).andExpect(jsonPath("$.ANSWERS").exists())
                .andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR").exists());
    }

    @Test
    public void testUnsign600002() throws Exception {
        // 构建解约请求
        KinginxRequest request = createUnsignRequest();

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/trading/request").contentType(MediaType.APPLICATION_JSON).content(requestJson))
                .andExpect(status().isOk()).andExpect(jsonPath("$.ANSWERS").exists())
                .andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR").exists());
    }

    @Test
    public void testAccountTrace600003() throws Exception {
        // 构建账户留痕请求
        KinginxRequest request = createAccountTraceRequest();

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/trading/request").contentType(MediaType.APPLICATION_JSON).content(requestJson))
                .andExpect(status().isOk()).andExpect(jsonPath("$.ANSWERS").exists())
                .andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR").exists());
    }

    @Test
    public void testTokenExchange700001() throws Exception {
        // 构建token交换请求
        KinginxRequest request = createTokenExchangeRequest();

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/trading/request").contentType(MediaType.APPLICATION_JSON).content(requestJson))
                .andExpect(status().isOk()).andExpect(jsonPath("$.ANSWERS").exists())
                .andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR").exists());
    }

    @Test
    public void testInvalidServiceId() throws Exception {
        // 构建无效功能号请求
        KinginxRequest request = createInvalidServiceRequest();

        String requestJson = objectMapper.writeValueAsString(request);

        mockMvc.perform(post("/api/trading/request").contentType(MediaType.APPLICATION_JSON).content(requestJson))
                .andExpect(status().isOk()).andExpect(jsonPath("$.ANSWERS[0].ANS_MSG_HDR.MSG_CODE").value("-1"));
    }

    private KinginxRequest createInvalidServiceRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId("999999"); // 无效功能号
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("TEST_PARAM", "test_value");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }

    private KinginxRequest createSignContractRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId(ServiceConstants.SERVICE_600001);
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("LOGIN_CODE", "test_account");
        reqData.put("TRD_PWD", "encrypted_password");
        reqData.put("USER_ID", "test_user_id");
        reqData.put("USER_ID_CLS", ServiceConstants.USER_ID_CLS_GENERAL);
        reqData.put("COMPANY_ID", ServiceConstants.COMPANY_ID_GUOJIN);
        reqData.put("LOGIN_TYPE", ServiceConstants.LOGIN_TYPE_FUND_ACCOUNT);
        reqData.put("TCC", "test_tcc_info");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }

    private KinginxRequest createLoginRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId(ServiceConstants.SERVICE_600010);
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("LOGIN_CODE", "test_account");
        reqData.put("USER_PWD", "encrypted_password");
        reqData.put("USER_ID", "test_user_id");
        reqData.put("USER_ID_CLS", ServiceConstants.USER_ID_CLS_GENERAL);
        reqData.put("COMPANY_ID", ServiceConstants.COMPANY_ID_GUOJIN);
        reqData.put("LOGIN_TYPE", ServiceConstants.LOGIN_TYPE_FUND_ACCOUNT);

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }

    private KinginxRequest createUnsignRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId(ServiceConstants.SERVICE_600002);
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("USER_ID", "test_user_id");
        reqData.put("USER_ID_CLS", ServiceConstants.USER_ID_CLS_GENERAL);
        reqData.put("COMPANY_ID", ServiceConstants.COMPANY_ID_GUOJIN);

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }

    private KinginxRequest createAccountTraceRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId(ServiceConstants.SERVICE_600003);
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("USER_ID", "test_user_id");
        reqData.put("USER_ID_CLS", ServiceConstants.USER_ID_CLS_GENERAL);
        reqData.put("COMPANY_ID", ServiceConstants.COMPANY_ID_GUOJIN);
        reqData.put("ACCOUNT", "test_account");
        reqData.put("TCC", "test_tcc_info");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }

    private KinginxRequest createTokenExchangeRequest() {
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId(ServiceConstants.SERVICE_700001);
        header.setTimeStamp(String.valueOf(System.currentTimeMillis()));
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("session_id", "test_session_id_12345");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        return request;
    }
}
