package com.kinginx.common.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Kinginx请求DTO测试类
 */
public class KinginxRequestTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testKinginxRequestSerialization() throws Exception {
        // 创建请求对象
        KinginxRequest request = new KinginxRequest();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId("600001");
        header.setTimeStamp("**********");
        header.setSign("test_sign");

        Map<String, Object> reqData = new HashMap<>();
        reqData.put("LOGIN_CODE", "test_account");
        reqData.put("USER_ID", "test_user");

        KinginxRequest.RequestItem requestItem = new KinginxRequest.RequestItem();
        requestItem.setReqMsgHdr(header);
        requestItem.setReqCommData(reqData);

        request.setRequests(Collections.singletonList(requestItem));

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(request);
        assertNotNull(json);
        assertTrue(json.contains("REQUESTS"));
        assertTrue(json.contains("REQ_MSG_HDR"));
        assertTrue(json.contains("SERVICE_ID"));

        // 反序列化
        KinginxRequest deserializedRequest = objectMapper.readValue(json, KinginxRequest.class);
        assertNotNull(deserializedRequest);
        assertNotNull(deserializedRequest.getRequests());
        assertEquals(1, deserializedRequest.getRequests().size());

        KinginxRequest.RequestItem item = deserializedRequest.getRequests().get(0);
        assertEquals("600001", item.getReqMsgHdr().getServiceId());
        assertEquals("test_account", item.getReqCommData().get("LOGIN_CODE"));
    }

    @Test
    public void testKinginxResponseSerialization() throws Exception {
        // 创建响应对象
        KinginxResponse response = new KinginxResponse("0", "成功", "", "session123");

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(response);
        assertNotNull(json);
        assertTrue(json.contains("ANSWERS"));
        assertTrue(json.contains("ANS_MSG_HDR"));
        assertTrue(json.contains("MSG_CODE"));

        // 反序列化
        KinginxResponse deserializedResponse = objectMapper.readValue(json, KinginxResponse.class);
        assertNotNull(deserializedResponse);
        assertNotNull(deserializedResponse.getAnswers());
        assertEquals(1, deserializedResponse.getAnswers().size());

        KinginxResponse.AnswerItem item = deserializedResponse.getAnswers().get(0);
        assertEquals("0", item.getAnsMsgHdr().getMsgCode());
        assertEquals("成功", item.getAnsMsgHdr().getMsgText());
        assertEquals("session123", item.getAnsMsgHdr().getSession());
    }

    @Test
    public void testRequestHeaderValidation() {
        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();

        // 测试getter/setter
        header.setServiceId("600001");
        assertEquals("600001", header.getServiceId());

        header.setTimeStamp("**********");
        assertEquals("**********", header.getTimeStamp());

        header.setSign("test_sign");
        assertEquals("test_sign", header.getSign());
    }

    @Test
    public void testResponseHeaderValidation() {
        KinginxResponse.AnswerHeader header = new KinginxResponse.AnswerHeader();

        // 测试getter/setter
        header.setMsgCode("0");
        assertEquals("0", header.getMsgCode());

        header.setMsgText("成功");
        assertEquals("成功", header.getMsgText());

        header.setSession("session123");
        assertEquals("session123", header.getSession());

        header.setDebugMsg("debug info");
        assertEquals("debug info", header.getDebugMsg());

        // 测试默认值
        assertEquals("0", header.getMsgLevel());
    }

    @Test
    public void testRequestItemValidation() {
        KinginxRequest.RequestItem item = new KinginxRequest.RequestItem();

        KinginxRequest.RequestHeader header = new KinginxRequest.RequestHeader();
        header.setServiceId("600001");
        item.setReqMsgHdr(header);

        Map<String, Object> data = new HashMap<>();
        data.put("test", "value");
        item.setReqCommData(data);

        assertEquals(header, item.getReqMsgHdr());
        assertEquals(data, item.getReqCommData());
        assertEquals("value", item.getReqCommData().get("test"));
    }

    @Test
    public void testAnswerItemValidation() {
        KinginxResponse.AnswerItem item = new KinginxResponse.AnswerItem();

        KinginxResponse.AnswerHeader header = new KinginxResponse.AnswerHeader();
        header.setMsgCode("0");
        item.setAnsMsgHdr(header);

        Map<String, Object> data = new HashMap<>();
        data.put("result", "success");
        item.setAnsCommData(Collections.singletonList(data));

        assertEquals(header, item.getAnsMsgHdr());
        assertEquals(1, item.getAnsCommData().size());
        assertEquals("success", item.getAnsCommData().get(0).get("result"));
    }
}
