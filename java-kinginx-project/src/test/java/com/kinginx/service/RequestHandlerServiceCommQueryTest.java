package com.kinginx.service;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxResponse;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RequestHandlerService中handleCommQuery方法的测试
 */
@SpringBootTest
public class RequestHandlerServiceCommQueryTest {

    @Mock
    private TradingAdapterService tradingAdapterService;

    @InjectMocks
    private RequestHandlerService requestHandlerService;

    @Test
    public void testHandleCommQuery_Success() {
        // 准备测试数据
        String refRequest = "test-ref-001";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "600120");
        
        Map<String, Object> reqCommData = new HashMap<>();
        reqCommData.put("ACCOUNT", "********");
        reqCommData.put("USER_ID", "testuser");
        reqData.put("REQ_COMM_DATA", reqCommData);

        // 准备Mock响应数据
        List<Map<String, Object>> mockData = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("FUND_BALANCE", "10000.00");
        dataRow.put("AVAILABLE_BALANCE", "8000.00");
        mockData.add(dataRow);

        TradingAdapterService.AdapterResult mockResult = 
            new TradingAdapterService.AdapterResult("0", "查询成功", mockData);

        // 设置Mock行为
        when(tradingAdapterService.callAdapter(eq(refRequest), eq("600120"), any(Map.class)))
            .thenReturn(mockResult);

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals("0", response.getCode());
        assertEquals("查询成功", response.getMessage());
        assertNotNull(response.getData());
        assertEquals(1, response.getData().size());
        
        Map<String, Object> responseData = (Map<String, Object>) response.getData().get(0);
        assertEquals("10000.00", responseData.get("FUND_BALANCE"));
        assertEquals("8000.00", responseData.get("AVAILABLE_BALANCE"));

        // 验证Mock调用
        verify(tradingAdapterService, times(1))
            .callAdapter(eq(refRequest), eq("600120"), any(Map.class));
    }

    @Test
    public void testHandleCommQuery_MissingServiceId() {
        // 准备测试数据 - 缺少SERVICE_ID
        String refRequest = "test-ref-002";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("REQ_COMM_DATA", new HashMap<>());

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals("-1", response.getCode());
        assertTrue(response.getMessage().contains("缺少SERVICE_ID"));

        // 验证没有调用适配器
        verify(tradingAdapterService, never()).callAdapter(anyString(), anyString(), any(Map.class));
    }

    @Test
    public void testHandleCommQuery_EmptyServiceId() {
        // 准备测试数据 - SERVICE_ID为空
        String refRequest = "test-ref-003";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "");
        reqData.put("REQ_COMM_DATA", new HashMap<>());

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals("-1", response.getCode());
        assertTrue(response.getMessage().contains("缺少SERVICE_ID"));

        // 验证没有调用适配器
        verify(tradingAdapterService, never()).callAdapter(anyString(), anyString(), any(Map.class));
    }

    @Test
    public void testHandleCommQuery_AdapterError() {
        // 准备测试数据
        String refRequest = "test-ref-004";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "600120");
        reqData.put("REQ_COMM_DATA", new HashMap<>());

        // 准备Mock错误响应
        TradingAdapterService.AdapterResult mockResult = 
            new TradingAdapterService.AdapterResult("-1001", "柜台连接失败", null);

        // 设置Mock行为
        when(tradingAdapterService.callAdapter(eq(refRequest), eq("600120"), any(Map.class)))
            .thenReturn(mockResult);

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals("-1001", response.getCode());
        assertEquals("柜台连接失败", response.getMessage());
        assertNull(response.getData());

        // 验证Mock调用
        verify(tradingAdapterService, times(1))
            .callAdapter(eq(refRequest), eq("600120"), any(Map.class));
    }

    @Test
    public void testHandleCommQuery_AdapterReturnsNull() {
        // 准备测试数据
        String refRequest = "test-ref-005";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "600120");
        reqData.put("REQ_COMM_DATA", new HashMap<>());

        // 设置Mock行为 - 返回null
        when(tradingAdapterService.callAdapter(eq(refRequest), eq("600120"), any(Map.class)))
            .thenReturn(null);

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals(ServiceConstants.ERROR_CODE_SYSTEM, response.getCode());
        assertTrue(response.getMessage().contains("适配器调用失败"));

        // 验证Mock调用
        verify(tradingAdapterService, times(1))
            .callAdapter(eq(refRequest), eq("600120"), any(Map.class));
    }

    @Test
    public void testHandleCommQuery_NoReqCommData() {
        // 准备测试数据 - 没有REQ_COMM_DATA
        String refRequest = "test-ref-006";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "600120");

        // 准备Mock响应
        TradingAdapterService.AdapterResult mockResult = 
            new TradingAdapterService.AdapterResult("0", "查询成功", new ArrayList<>());

        // 设置Mock行为
        when(tradingAdapterService.callAdapter(eq(refRequest), eq("600120"), any(Map.class)))
            .thenReturn(mockResult);

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals("0", response.getCode());
        assertEquals("查询成功", response.getMessage());

        // 验证传递给适配器的是空的HashMap
        verify(tradingAdapterService, times(1))
            .callAdapter(eq(refRequest), eq("600120"), argThat(map -> 
                map != null && map.isEmpty()));
    }

    @Test
    public void testHandleCommQuery_EmptyDataResponse() {
        // 准备测试数据
        String refRequest = "test-ref-007";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "600120");
        reqData.put("REQ_COMM_DATA", new HashMap<>());

        // 准备Mock响应 - 空数据
        TradingAdapterService.AdapterResult mockResult = 
            new TradingAdapterService.AdapterResult("0", "查询成功，无数据", new ArrayList<>());

        // 设置Mock行为
        when(tradingAdapterService.callAdapter(eq(refRequest), eq("600120"), any(Map.class)))
            .thenReturn(mockResult);

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals("0", response.getCode());
        assertEquals("查询成功，无数据", response.getMessage());
        // 空数据时应该返回没有data字段的响应
        assertNull(response.getData());
    }

    @Test
    public void testHandleCommQuery_AdapterException() {
        // 准备测试数据
        String refRequest = "test-ref-008";
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("SERVICE_ID", "600120");
        reqData.put("REQ_COMM_DATA", new HashMap<>());

        // 设置Mock行为 - 抛出异常
        when(tradingAdapterService.callAdapter(eq(refRequest), eq("600120"), any(Map.class)))
            .thenThrow(new RuntimeException("适配器内部错误"));

        // 执行测试
        KinginxResponse response = requestHandlerService.handleCommQuery(refRequest, reqData);

        // 验证结果
        assertNotNull(response);
        assertEquals(ServiceConstants.ERROR_CODE_SYSTEM, response.getCode());
        assertTrue(response.getMessage().contains("通用查询处理异常"));
        assertTrue(response.getMessage().contains("适配器内部错误"));

        // 验证Mock调用
        verify(tradingAdapterService, times(1))
            .callAdapter(eq(refRequest), eq("600120"), any(Map.class));
    }
}
