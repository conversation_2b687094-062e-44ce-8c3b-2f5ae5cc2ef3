package com.kinginx.service.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 加密工具类测试
 */
@SpringBootTest
@TestPropertySource(properties = {"kinginx.encryption.rsa.private-key-path=", "kinginx.encryption.rsa.public-key-path=",
        "kinginx.encryption.des.key=test_des_key_123456"})
public class CryptoUtilTest {

    @Test
    public void testMd5Hash() {
        CryptoUtil cryptoUtil = new CryptoUtil();

        String input = "test_string";
        String hash = cryptoUtil.md5Hash(input);

        assertNotNull(hash);
        assertEquals(32, hash.length()); // MD5 hash is 32 characters

        // Test consistency
        String hash2 = cryptoUtil.md5Hash(input);
        assertEquals(hash, hash2);
    }

    @Test
    public void testSha256Hash() {
        CryptoUtil cryptoUtil = new CryptoUtil();

        String input = "test_string";
        String hash = cryptoUtil.sha256Hash(input);

        assertNotNull(hash);
        assertEquals(64, hash.length()); // SHA256 hash is 64 characters

        // Test consistency
        String hash2 = cryptoUtil.sha256Hash(input);
        assertEquals(hash, hash2);
    }

    @Test
    public void testDesEncryptDecrypt() {
        CryptoUtil cryptoUtil = new CryptoUtil();

        String plainText = "test_password";
        String key = "test_key";

        // Test encryption
        String encrypted = cryptoUtil.desEncrypt(plainText, key);
        assertNotNull(encrypted);
        assertNotEquals(plainText, encrypted);

        // Test decryption
        String decrypted = cryptoUtil.desDecrypt(encrypted, key);
        assertEquals(plainText, decrypted);
    }

    @Test
    public void testDesEncryptWithDifferentKeys() {
        CryptoUtil cryptoUtil = new CryptoUtil();

        String plainText = "test_password";
        String key1 = "test_key1";
        String key2 = "test_key2";

        String encrypted1 = cryptoUtil.desEncrypt(plainText, key1);
        String encrypted2 = cryptoUtil.desEncrypt(plainText, key2);

        // Different keys should produce different encrypted results
        assertNotEquals(encrypted1, encrypted2);
    }

    @Test
    public void testHashFunctions() {
        CryptoUtil cryptoUtil = new CryptoUtil();

        String input1 = "password123";
        String input2 = "password124";

        // MD5 hashes should be different for different inputs
        String md5Hash1 = cryptoUtil.md5Hash(input1);
        String md5Hash2 = cryptoUtil.md5Hash(input2);
        assertNotEquals(md5Hash1, md5Hash2);

        // SHA256 hashes should be different for different inputs
        String sha256Hash1 = cryptoUtil.sha256Hash(input1);
        String sha256Hash2 = cryptoUtil.sha256Hash(input2);
        assertNotEquals(sha256Hash1, sha256Hash2);
    }
}
