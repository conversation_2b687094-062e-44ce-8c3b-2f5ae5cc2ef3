package com.kinginx.controller;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxRequest;
import com.kinginx.common.dto.KinginxResponse;
import com.kinginx.config.ServiceHandlerConfig;
import com.kinginx.service.RequestHandlerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 交易控制器
 * 对应Python项目中的kesb_req_bpagent.py入口
 */
@RestController
@RequestMapping("/api/trading")
public class TradingController {

    private static final Logger logger = LoggerFactory.getLogger(TradingController.class);

    @Autowired
    private RequestHandlerService requestHandlerService;

    /**
     * 统一请求入口
     * 对应Python中的kesb_req函数
     */
    @PostMapping("/request")
    public KinginxResponse handleRequest(@Valid @RequestBody KinginxRequest request, HttpServletRequest httpRequest) {

        // 生成请求ID
        String refRequest = generateRequestId();

        try {
            logger.info("收到交易请求, refRequest: {}, request: {}", refRequest, request);

            // 获取请求数据
            KinginxRequest.RequestItem requestItem = request.getRequests().get(0);
            String serviceId = requestItem.getReqMsgHdr().getServiceId();
            Map<String, Object> reqCommData = requestItem.getReqCommData();

            logger.info("处理功能号: {}, refRequest: {}", serviceId, refRequest);

            // 根据功能号路由到不同的处理方法
            switch (serviceId) {
                case ServiceConstants.SERVICE_600001:
                    return requestHandlerService.handle600001Sign(refRequest, reqCommData);
                case ServiceConstants.SERVICE_600002:
                    return requestHandlerService.handle600002Unsign(refRequest, reqCommData);
                case ServiceConstants.SERVICE_600003:
                    return requestHandlerService.handle600003AccountTrace(refRequest, reqCommData);
                case ServiceConstants.SERVICE_600010:
                    return requestHandlerService.handle600010Login(refRequest, reqCommData);
                case ServiceConstants.SERVICE_700001:
                    return requestHandlerService.handle700001TokenExchange(refRequest, reqCommData);
                default:
                    logger.warn("不支持的功能号: {}, refRequest: {}", serviceId, refRequest);
                    return new KinginxResponse("-1", "不支持的功能号: " + serviceId, "", "");
            }

        } catch (Exception e) {
            logger.error("处理交易请求异常, refRequest: {}", refRequest, e);
            return new KinginxResponse(ServiceConstants.ERROR_CODE_SYSTEM, "系统异常: " + e.getMessage(), "", "");
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", "UP");
        map.put("timestamp", System.currentTimeMillis());
        map.put("service", "kinginx-trading-service");
        return map;
    }
}
