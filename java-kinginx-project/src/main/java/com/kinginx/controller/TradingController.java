package com.kinginx.controller;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxRequest;
import com.kinginx.common.dto.KinginxResponse;
import com.kinginx.common.exception.KinginxException;
import com.kinginx.service.SignContractService;
import com.kinginx.service.SignContractService.SignResult;
import com.kinginx.service.TokenExchangeService;
import com.kinginx.service.TokenExchangeService.TokenExchangeResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 交易控制器
 * 对应Python项目中的kesb_req_bpagent.py入口
 */
@RestController
@RequestMapping("/api/trading")
public class TradingController {

    private static final Logger logger = LoggerFactory.getLogger(TradingController.class);

    @Autowired
    private SignContractService signContractService;

    @Autowired
    private TokenExchangeService tokenExchangeService;

    /**
     * 统一请求入口
     * 对应Python中的kesb_req函数
     */
    @PostMapping("/request")
    public KinginxResponse handleRequest(@Valid @RequestBody KinginxRequest request, HttpServletRequest httpRequest) {

        // 生成请求ID
        String refRequest = generateRequestId();

        try {
            logger.info("收到交易请求, refRequest: {}, request: {}", refRequest, request);

            // 获取请求数据
            KinginxRequest.RequestItem requestItem = request.getRequests().get(0);
            String serviceId = requestItem.getReqMsgHdr().getServiceId();
            Map<String, Object> reqCommData = requestItem.getReqCommData();

            logger.info("处理功能号: {}, refRequest: {}", serviceId, refRequest);

            // 根据功能号路由到不同的处理方法
            switch (serviceId) {
                case ServiceConstants.SERVICE_600001:
                    return handle600001Sign(refRequest, reqCommData);
                case ServiceConstants.SERVICE_600002:
                    return handle600002Unsign(refRequest, reqCommData);
                case ServiceConstants.SERVICE_600003:
                    return handle600003AccountTrace(refRequest, reqCommData);
                case ServiceConstants.SERVICE_600010:
                    return handle600010Login(refRequest, reqCommData);
                case ServiceConstants.SERVICE_700001:
                    return handle700001TokenExchange(refRequest, reqCommData);
                default:
                    logger.warn("不支持的功能号: {}, refRequest: {}", serviceId, refRequest);
                    return new KinginxResponse("-1", "不支持的功能号: " + serviceId, "", "");
            }

        } catch (Exception e) {
            logger.error("处理交易请求异常, refRequest: {}", refRequest, e);
            return new KinginxResponse(ServiceConstants.ERROR_CODE_SYSTEM, "系统异常: " + e.getMessage(), "", "");
        }
    }

    /**
     * 处理600001签约请求
     */
    private KinginxResponse handle600001Sign(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600001签约请求, refRequest: {}", refRequest);

            // 参数验证
            validateSignRequest(reqData);

            // 调用签约服务
            SignResult signResult = signContractService.signContract410301(refRequest, reqData);

            if (signResult.isSuccess()) {
                logger.info("600001签约成功, refRequest: {}, session: {}", refRequest, signResult.getSession());
                return new KinginxResponse(signResult.getCode(), signResult.getMessage(), "", signResult.getSession(),
                        signResult.getData());
            } else {
                logger.warn("600001签约失败, refRequest: {}, code: {}, message: {}", refRequest, signResult.getCode(),
                        signResult.getMessage());
                return new KinginxResponse(signResult.getCode(), signResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600001签约请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "签约处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理600002解约请求
     */
    private KinginxResponse handle600002Unsign(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600002解约请求, refRequest: {}", refRequest);

            // 参数验证
            validateUnsignRequest(reqData);

            // 调用解约服务
            SignResult unsignResult = signContractService.unsignContract600002(refRequest, reqData);

            if (unsignResult.isSuccess()) {
                logger.info("600002解约成功, refRequest: {}", refRequest);
                return new KinginxResponse(unsignResult.getCode(), unsignResult.getMessage(), "", "");
            } else {
                logger.warn("600002解约失败, refRequest: {}, code: {}, message: {}", refRequest, unsignResult.getCode(),
                        unsignResult.getMessage());
                return new KinginxResponse(unsignResult.getCode(), unsignResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600002解约请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "解约处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理600003账户留痕请求
     */
    private KinginxResponse handle600003AccountTrace(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600003账户留痕请求, refRequest: {}", refRequest);

            // 参数验证
            validateAccountTraceRequest(reqData);

            // 调用账户留痕服务
            SignResult traceResult = signContractService.accountTrace600003(refRequest, reqData);

            if (traceResult.isSuccess()) {
                logger.info("600003账户留痕成功, refRequest: {}", refRequest);
                return new KinginxResponse(traceResult.getCode(), traceResult.getMessage(), "", "");
            } else {
                logger.warn("600003账户留痕失败, refRequest: {}, code: {}, message: {}", refRequest,
                        traceResult.getCode(), traceResult.getMessage());
                return new KinginxResponse(traceResult.getCode(), traceResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600003账户留痕请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "账户留痕处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理700001 session换token请求
     */
    private KinginxResponse handle700001TokenExchange(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理700001 session换token请求, refRequest: {}", refRequest);

            // 参数验证
            validateTokenExchangeRequest(reqData);

            // 调用token交换服务
            CompletableFuture<TokenExchangeResult> tokenFuture =
                    tokenExchangeService.exchangeToken(refRequest, reqData);
            TokenExchangeResult tokenResult = tokenFuture.get();

            if (tokenResult.isSuccess()) {
                logger.info("700001 session换token成功, refRequest: {}", refRequest);

                // 构建响应数据
                Map<String, Object> resultData = new HashMap<>();
                if (tokenResult.getData() != null) {
                    resultData.putAll((Map<String, Object>)tokenResult.getData());
                }

                return new KinginxResponse(tokenResult.getCode(), tokenResult.getMessage(), "", "",
                        Collections.singletonList(resultData));
            } else {
                logger.warn("700001 session换token失败, refRequest: {}, code: {}, message: {}", refRequest,
                        tokenResult.getCode(), tokenResult.getMessage());
                return new KinginxResponse(tokenResult.getCode(), tokenResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理700001 session换token请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "token交换处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理600010登录请求
     */
    private KinginxResponse handle600010Login(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600010登录请求, refRequest: {}", refRequest);

            // 参数验证
            validateLoginRequest(reqData);

            // 调用登录服务
            CompletableFuture<SignResult> loginFuture = signContractService.login(refRequest, reqData);
            SignResult loginResult = loginFuture.get();

            if (loginResult.isSuccess()) {
                logger.info("600010登录成功, refRequest: {}, session: {}", refRequest, loginResult.getSession());
                return new KinginxResponse(loginResult.getCode(), loginResult.getMessage(), "",
                        loginResult.getSession());
            } else {
                logger.warn("600010登录失败, refRequest: {}, code: {}, message: {}", refRequest, loginResult.getCode(),
                        loginResult.getMessage());
                return new KinginxResponse(loginResult.getCode(), loginResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600010登录请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "登录处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 验证签约请求参数
     */
    private void validateSignRequest(Map<String, Object> reqData) {
        if (reqData.get("LOGIN_CODE") == null) {
            throw new KinginxException("-1", "登录代码不能为空");
        }
        if (reqData.get("TRD_PWD") == null) {
            throw new KinginxException("-1", "交易密码不能为空");
        }
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-1", "用户ID不能为空");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
    }

    /**
     * 验证登录请求参数
     */
    private void validateLoginRequest(Map<String, Object> reqData) {
        if (reqData.get("LOGIN_CODE") == null) {
            throw new KinginxException("-1", "登录代码不能为空");
        }
        if (reqData.get("USER_PWD") == null) {
            throw new KinginxException("-1", "用户密码不能为空");
        }
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-1", "用户ID不能为空");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
    }

    /**
     * 验证解约请求参数
     */
    private void validateUnsignRequest(Map<String, Object> reqData) {
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-100", "入参有误：必要的入参[USER_ID]不存在");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
    }

    /**
     * 验证账户留痕请求参数
     */
    private void validateAccountTraceRequest(Map<String, Object> reqData) {
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-100", "入参有误：必要的入参[USER_ID]不存在");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
        if (reqData.get("ACCOUNT") == null) {
            throw new KinginxException("-1", "资金账号不能为空");
        }
    }

    /**
     * 验证token交换请求参数
     */
    private void validateTokenExchangeRequest(Map<String, Object> reqData) {
        if (reqData.get("session_id") == null) {
            throw new KinginxException("-7001", "[session_id]必传");
        }
        String sessionId = (String)reqData.get("session_id");
        if (sessionId == null || sessionId.trim().isEmpty()) {
            throw new KinginxException("-7001", "[session_id]不能为空");
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", "UP");
        map.put("timestamp", System.currentTimeMillis());
        map.put("service", "kinginx-trading-service");
        return map;
    }
}
