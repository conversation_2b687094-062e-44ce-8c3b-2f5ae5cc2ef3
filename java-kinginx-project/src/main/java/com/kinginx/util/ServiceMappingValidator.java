package com.kinginx.util;

import com.kinginx.config.ServiceHandlerConfig;
import com.kinginx.config.ServiceHandlerMappingProperties;
import com.kinginx.service.ServiceHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 服务映射配置验证器
 * 在应用启动时验证所有配置的服务映射是否有对应的处理器
 */
@Component
public class ServiceMappingValidator implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ServiceMappingValidator.class);

    @Autowired
    private ServiceHandlerMappingProperties mappingProperties;

    @Autowired
    private ServiceHandlerConfig serviceHandlerConfig;

    @Override
    public void run(String... args) throws Exception {
        logger.info("开始验证服务映射配置...");

        Map<String, String> mappings = mappingProperties.getServiceHandlerMapping();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String serviceId = entry.getKey();
            String methodName = entry.getValue();

            try {
                // 检查处理器是否存在
                ServiceHandler handler = serviceHandlerConfig.getServiceHandler(serviceId);

                if (handler == null) {
                    errors.add(String.format("服务 %s 的处理器 %s 不存在", serviceId, methodName));
                } else {
                    logger.debug("服务 {} -> 处理器 {} 验证通过", serviceId, methodName);
                }

            } catch (Exception e) {
                errors.add(String.format("服务 %s 的处理器 %s 验证异常: %s", serviceId, methodName, e.getMessage()));
            }
        }

        // 输出验证结果
        if (!errors.isEmpty()) {
            logger.error("服务映射配置验证失败，发现 {} 个错误:", errors.size());
            for (String error : errors) {
                logger.error("  - {}", error);
            }
            throw new RuntimeException("服务映射配置验证失败，请检查配置文件和处理方法");
        }

        if (!warnings.isEmpty()) {
            logger.warn("服务映射配置验证发现 {} 个警告:", warnings.size());
            for (String warning : warnings) {
                logger.warn("  - {}", warning);
            }
        }

        logger.info("服务映射配置验证完成，共验证 {} 个服务映射", mappings.size());

        // 输出统计信息
        logMappingStatistics(mappings);
    }

    /**
     * 输出映射统计信息
     */
    private void logMappingStatistics(Map<String, String> mappings) {
        Map<String, Long> methodCounts = mappings.values().stream().collect(
                java.util.stream.Collectors.groupingBy(java.util.function.Function.identity(),
                        java.util.stream.Collectors.counting()));

        logger.info("处理方法使用统计:");
        methodCounts.entrySet().stream().sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .forEach(entry -> {
                    logger.info("  {} -> {} 个服务", entry.getKey(), entry.getValue());
                });
    }
}
