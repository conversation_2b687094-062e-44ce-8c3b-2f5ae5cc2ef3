package com.kinginx.common.constants;

/**
 * 服务常量定义
 * 对应Python项目中的功能号定义
 */
public class ServiceConstants {

    /**
     * 签约相关功能号
     */
    public static final String SERVICE_600001 = "600001";  // 华林微证券签约接口
    public static final String SERVICE_600002 = "600002";  // 华林微证券解约接口
    public static final String SERVICE_600003 = "600003";  // 账户留痕接口
    public static final String SERVICE_600010 = "600010";  // bpagent登录接口

    /**
     * 网厅相关功能号
     */
    public static final String SERVICE_700001 = "700001";  // session换国金网厅token

    /**
     * 交易适配器功能号
     */
    public static final String ADAPTER_410301 = "410301";   // 组合业务签约
    public static final String ADAPTER_410301A = "410301A"; // 登录验证
    public static final String ADAPTER_410323 = "410323";   // 增加权限
    public static final String ADAPTER_410324 = "410324";   // 查询权限

    /**
     * 统一认证功能号
     */
    public static final String TYRZ_T0005001 = "T0005001";  // 生成票据
    public static final String TYRZ_T0005005 = "T0005005";  // 签约
    public static final String TYRZ_T0005006 = "T0005006";  // 插入扩展信息
    public static final String TYRZ_T0005009 = "T0005009";  // 解约
    public static final String TYRZ_T0005010 = "T0005010";  // 查询签约信息

    /**
     * 登录类型定义
     */
    public static final int LOGIN_TYPE_CUSTOMER_CODE = 1;   // 客户代码
    public static final int LOGIN_TYPE_FUND_ACCOUNT = 2;    // 资金账号
    public static final int LOGIN_TYPE_SZ_A_ACCOUNT = 3;    // 深A股东
    public static final int LOGIN_TYPE_SZ_B_ACCOUNT = 4;    // 深B股东
    public static final int LOGIN_TYPE_SH_A_ACCOUNT = 5;    // 沪A股东
    public static final int LOGIN_TYPE_SH_B_ACCOUNT = 6;    // 沪B股东

    /**
     * 市场代码定义
     */
    public static final String MARKET_SZ_A = "0";  // 深A
    public static final String MARKET_SH_A = "1";  // 沪A
    public static final String MARKET_SZ_B = "2";  // 深B
    public static final String MARKET_SH_B = "3";  // 沪B

    /**
     * 用户ID类型定义
     */
    public static final String USER_ID_CLS_CUSTOMER = "0";  // 客户代码
    public static final String USER_ID_CLS_QQ = "Q";        // 全户通渠道
    public static final String USER_ID_CLS_WECHAT = "2";    // 自选股
    public static final String USER_ID_CLS_GENERAL = "1";   // 一般渠道

    /**
     * 公司ID定义
     */
    public static final String COMPANY_ID_GUOJIN = "15900"; // 国金证券
    public static final String COMPANY_ID_HUALIN = "12900"; // 华林证券

    /**
     * 操作方式定义
     */
    public static final String OPER_WAY_VERIFY = "5";   // 验证方式
    public static final String OPER_WAY_LOGIN = "e";    // 登录方式

    /**
     * 响应码定义
     */
    public static final String SUCCESS_CODE = "0";
    public static final String ERROR_CODE_SYSTEM = "-100";
    public static final String ERROR_CODE_ALREADY_SIGNED = "-101";
    public static final String ERROR_CODE_INVALID_ACCOUNT = "-102";
    public static final String ERROR_CODE_ADAPTER_FAIL = "-30011";
    public static final String ERROR_CODE_TYRZ_FAIL = "-2000";

    /**
     * 会话相关常量
     */
    public static final int DEFAULT_SESSION_EXPIRE_TIME = 28800; // 8小时
    public static final String SESSION_TYPE_LOGIN = "Login";
    public static final String SESSION_TYPE_SIGN = "Sign";

    /**
     * 加密相关常量
     */
    public static final String DES_KEY_410301 = "410301_DesPwd";

    /**
     * 资产属性定义
     */
    public static final String ASSET_PROP_NORMAL = "0";  // 普通证券账户
}
