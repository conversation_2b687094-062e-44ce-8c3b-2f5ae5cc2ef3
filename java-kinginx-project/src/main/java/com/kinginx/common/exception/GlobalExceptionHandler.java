package com.kinginx.common.exception;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理Kinginx业务异常
     */
    @ExceptionHandler(KinginxException.class)
    public ResponseEntity<KinginxResponse> handleKinginxException(KinginxException e, HttpServletRequest request) {
        logger.error("Kinginx业务异常: code={}, message={}, debug={}", e.getErrorCode(), e.getErrorMessage(),
                e.getDebugMessage(), e);

        KinginxResponse response = new KinginxResponse(e.getErrorCode(), e.getErrorMessage(), e.getDebugMessage(), "");

        return ResponseEntity.ok(response);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public ResponseEntity<KinginxResponse> handleValidationException(Exception e, HttpServletRequest request) {
        logger.error("参数验证异常: {}", e.getMessage(), e);

        String errorMessage = "请求参数验证失败";
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validException = (MethodArgumentNotValidException)e;
            if (validException.getBindingResult().hasFieldErrors()) {
                errorMessage = validException.getBindingResult().getFieldError().getDefaultMessage();
            }
        } else if (e instanceof BindException) {
            BindException bindException = (BindException)e;
            if (bindException.getBindingResult().hasFieldErrors()) {
                errorMessage = bindException.getBindingResult().getFieldError().getDefaultMessage();
            }
        }

        KinginxResponse response =
                new KinginxResponse(ServiceConstants.ERROR_CODE_SYSTEM, errorMessage, e.getMessage(), "");

        return ResponseEntity.ok(response);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<KinginxResponse> handleSystemException(Exception e, HttpServletRequest request) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        logger.error("系统异常: {}", e.getMessage(), e);

        KinginxResponse response =
                new KinginxResponse(ServiceConstants.ERROR_CODE_SYSTEM, "系统内部异常: " + e.getMessage(),
                        timestamp + ": " + e.getClass().getSimpleName(), "");

        return ResponseEntity.ok(response);
    }
}
