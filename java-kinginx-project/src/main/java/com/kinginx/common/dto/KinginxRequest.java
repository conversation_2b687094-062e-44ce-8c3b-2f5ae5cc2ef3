package com.kinginx.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Kinginx统一请求格式
 * 对应Python项目中的JSON请求结构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KinginxRequest {

    @JsonProperty("REQUESTS")
    @NotNull
    @NotEmpty
    @Valid
    private List<RequestItem> requests;

    /**
     * 请求项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequestItem {

        @JsonProperty("REQ_MSG_HDR")
        @NotNull
        @Valid
        private RequestHeader reqMsgHdr;

        @JsonProperty("REQ_COMM_DATA")
        @NotNull
        private Map<String, Object> reqCommData;
    }

    /**
     * 请求头
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequestHeader {

        @JsonProperty("SERVICE_ID")
        @NotNull
        private String serviceId;

        @JsonProperty("TIME_STAMP")
        private String timeStamp;

        @JsonProperty("SIGN")
        private String sign;
    }
}
