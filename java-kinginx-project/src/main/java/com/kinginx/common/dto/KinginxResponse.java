package com.kinginx.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Kinginx统一响应格式
 * 对应Python项目中的JSON响应结构
 */
@Data
@NoArgsConstructor
public class KinginxResponse {

    @JsonProperty("ANSWERS")
    private List<AnswerItem> answers;

    public KinginxResponse(String code, String message, String debug, String session) {
        this.answers = Collections.singletonList(new AnswerItem(code, message, debug, session, null));
    }

    public KinginxResponse(String code, String message, String debug, String session, List<Map<String, Object>> data) {
        this.answers = Collections.singletonList(new AnswerItem(code, message, debug, session, data));
    }

    /**
     * 响应项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnswerItem {

        @JsonProperty("ANS_MSG_HDR")
        private AnswerHeader ansMsgHdr;

        @JsonProperty("ANS_COMM_DATA")
        private List<Map<String, Object>> ansCommData;

        public AnswerItem(String code, String message, String debug, String session, List<Map<String, Object>> data) {
            this.ansMsgHdr = new AnswerHeader(code, message, debug, session);
            this.ansCommData = data;
        }
    }

    /**
     * 响应头
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnswerHeader {

        @JsonProperty("MSG_CODE")
        private String msgCode;

        @JsonProperty("MSG_LEVEL")
        private String msgLevel = "0";

        @JsonProperty("MSG_TEXT")
        private String msgText;

        @JsonProperty("DEBUG_MSG")
        private String debugMsg;

        @JsonProperty("SESSION")
        private String session;

        public AnswerHeader(String msgCode, String msgText, String debugMsg, String session) {
            this.msgCode = msgCode;
            this.msgLevel = "0";
            this.msgText = msgText;
            this.debugMsg = debugMsg;
            this.session = session;
        }
    }
}
