package com.kinginx.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户签约解约记录实体
 * 对应Python项目中的user_sign_and_unsign表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_sign_and_unsign")
public class UserSignAndUnsign {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_code", length = 50)
    private String userCode;

    @Column(name = "user_id", length = 100, nullable = false)
    private String userId;

    @Column(name = "user_id_cls", length = 10, nullable = false)
    private String userIdCls;

    @Column(name = "account", length = 50, nullable = false)
    private String account;

    @Column(name = "company_id", length = 20, nullable = false)
    private String companyId;

    @Column(name = "time", nullable = false)
    private LocalDateTime time;

    @Column(name = "type", nullable = false)
    private Integer type; // 0-签约, 1-解约

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        if (time == null) {
            time = LocalDateTime.now();
        }
    }
}
