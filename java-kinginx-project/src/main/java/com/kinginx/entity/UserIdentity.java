package com.kinginx.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户身份信息实体
 * 对应Python项目中的user_identity表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user_identity")
public class UserIdentity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id_info", length = 100)
    private String userIdInfo;

    @Column(name = "user_id_type", length = 10)
    private String userIdType;

    @Column(name = "user_id_cls", length = 10)
    private String userIdCls;

    @Column(name = "company_id", length = 20)
    private String companyId;

    @Column(name = "account", length = 50)
    private String account;

    @Column(name = "custid", length = 50)
    private String custId;

    @Column(name = "user_name", length = 100)
    private String userName;

    @Column(name = "org_code", length = 20)
    private String orgCode;

    @Column(name = "secusza", length = 20)
    private String secuSzA;

    @Column(name = "secuszb", length = 20)
    private String secuSzB;

    @Column(name = "secusha", length = 20)
    private String secuShA;

    @Column(name = "secushb", length = 20)
    private String secuShB;

    @Column(name = "bankinfo", length = 500)
    private String bankInfo;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
}
