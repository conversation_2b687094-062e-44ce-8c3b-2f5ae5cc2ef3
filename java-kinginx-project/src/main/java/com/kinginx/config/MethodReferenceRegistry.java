package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import com.kinginx.service.ServiceHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 方法引用注册器
 * 提供统一的方法引用注册和管理功能
 */
public class MethodReferenceRegistry {

    private static final Logger logger = LoggerFactory.getLogger(MethodReferenceRegistry.class);

    private final RequestHandlerService requestHandlerService;
    private final Map<String, ServiceHandler> methodReferenceMap;

    public MethodReferenceRegistry(RequestHandlerService requestHandlerService) {
        this.requestHandlerService = requestHandlerService;
        this.methodReferenceMap = new HashMap<>();
        registerAllMethodReferences();
    }

    /**
     * 注册所有方法引用
     */
    private void registerAllMethodReferences() {
        logger.info("开始注册方法引用...");

        // 签约解约相关
        registerMethodReference("handle600001Sign", requestHandlerService::handle600001Sign);
        registerMethodReference("handle600002Unsign", requestHandlerService::handle600002Unsign);
        registerMethodReference("handle600003AccountTrace", requestHandlerService::handle600003AccountTrace);
        registerMethodReference("handle600010Login", requestHandlerService::handle600010Login);
        registerMethodReference("handle900010ResetPassword", requestHandlerService::handle900010ResetPassword);
        registerMethodReference("handle600011FundPasswordLogin", requestHandlerService::handle600011FundPasswordLogin);

        // 通用查询接口
        registerMethodReference("handleCommQuery", requestHandlerService::handleCommQuery);

        // 特殊处理接口
        registerMethodReference("handle600040UserInfo", requestHandlerService::handle600040UserInfo);

        // 会话相关
        registerMethodReference("handle600401ExtendSession", requestHandlerService::handle600401ExtendSession);

        // 公告相关
        registerMethodReference("handle600090InsertNotice", requestHandlerService::handle600090InsertNotice);
        registerMethodReference("handle600091SelectNotice", requestHandlerService::handle600091SelectNotice);
        registerMethodReference("handle600400SelectNoticeTx", requestHandlerService::handle600400SelectNoticeTx);
        registerMethodReference("handle600098DeleteNotice", requestHandlerService::handle600098DeleteNotice);
        registerMethodReference("handle600099UpdateNotice", requestHandlerService::handle600099UpdateNotice);

        // Token相关
        registerMethodReference("handle700001TokenExchange", requestHandlerService::handle700001TokenExchange);

        // 测试接口
        registerMethodReference("handleTestForSession", requestHandlerService::handleTestForSession);
        registerMethodReference("handleGetClientIp", requestHandlerService::handleGetClientIp);
        registerMethodReference("handleGetNginxInfo", requestHandlerService::handleGetNginxInfo);
        registerMethodReference("handleClearNginxXpAns", requestHandlerService::handleClearNginxXpAns);

        // 测试环境专用接口
        registerMethodReference("handle400001QueryAccount", requestHandlerService::handle400001QueryAccount);
        registerMethodReference("handle400002QueryUserId", requestHandlerService::handle400002QueryUserId);
        registerMethodReference("handle400020CheckSmsCode", requestHandlerService::handle400020CheckSmsCode);

        logger.info("方法引用注册完成，共注册 {} 个方法引用", methodReferenceMap.size());
    }

    /**
     * 注册单个方法引用
     */
    private void registerMethodReference(String methodName, ServiceHandler handler) {
        if (methodReferenceMap.containsKey(methodName)) {
            logger.warn("方法引用已存在，将被覆盖: {}", methodName);
        }
        methodReferenceMap.put(methodName, handler);
        logger.debug("注册方法引用: {}", methodName);
    }

    /**
     * 获取方法引用
     */
    public ServiceHandler getMethodReference(String methodName) {
        return methodReferenceMap.get(methodName);
    }

    /**
     * 检查方法引用是否存在
     */
    public boolean containsMethodReference(String methodName) {
        return methodReferenceMap.containsKey(methodName);
    }

    /**
     * 获取所有方法引用
     */
    public Map<String, ServiceHandler> getAllMethodReferences() {
        return new HashMap<>(methodReferenceMap);
    }

    /**
     * 获取所有方法名
     */
    public Set<String> getAllMethodNames() {
        return methodReferenceMap.keySet();
    }

    /**
     * 获取方法引用数量
     */
    public int getMethodReferenceCount() {
        return methodReferenceMap.size();
    }

    /**
     * 动态添加方法引用（用于扩展）
     */
    public void addMethodReference(String methodName, ServiceHandler handler) {
        if (methodReferenceMap.containsKey(methodName)) {
            logger.warn("方法引用已存在，将被覆盖: {}", methodName);
        }
        methodReferenceMap.put(methodName, handler);
        logger.info("动态添加方法引用: {}", methodName);
    }

    /**
     * 移除方法引用
     */
    public boolean removeMethodReference(String methodName) {
        ServiceHandler removed = methodReferenceMap.remove(methodName);
        if (removed != null) {
            logger.info("移除方法引用: {}", methodName);
            return true;
        }
        return false;
    }

    /**
     * 清空所有方法引用
     */
    public void clearAllMethodReferences() {
        int count = methodReferenceMap.size();
        methodReferenceMap.clear();
        logger.info("清空所有方法引用，共清空 {} 个", count);
    }

    /**
     * 验证所有方法引用
     */
    public void validateAllMethodReferences() {
        logger.info("开始验证所有方法引用...");
        
        for (Map.Entry<String, ServiceHandler> entry : methodReferenceMap.entrySet()) {
            String methodName = entry.getKey();
            ServiceHandler handler = entry.getValue();
            
            if (handler == null) {
                logger.error("方法引用为null: {}", methodName);
            } else {
                logger.debug("方法引用验证通过: {}", methodName);
            }
        }
        
        logger.info("方法引用验证完成");
    }
}
