package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import com.kinginx.service.ServiceHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务处理器配置
 * 对应Python项目中的bpagentspd.py中的g_reqBpagentFunctionDict
 * 从YAML配置文件中读取映射关系
 */
@Configuration
public class ServiceHandlerConfig {

    @Autowired
    private RequestHandlerService requestHandlerService;

    @Autowired
    private ServiceHandlerMappingProperties mappingProperties;



    /**
     * 运行时方法引用缓存
     */
    private Map<String, ServiceHandler> handlerCache = new HashMap<>();

    /**
     * 方法名到方法引用的映射
     */
    private Map<String, ServiceHandler> methodReferenceMap;

    @PostConstruct
    public void initHandlerCache() {
        // 初始化方法引用映射
        initMethodReferenceMap();

        Map<String, String> mappings = mappingProperties.getServiceHandlerMapping();

        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String serviceId = entry.getKey();
            String methodName = entry.getValue();

            ServiceHandler handler = methodReferenceMap.get(methodName);
            if (handler == null) {
                throw new RuntimeException("Handler method not found: " + methodName + " for service: " + serviceId);
            }

            handlerCache.put(serviceId, handler);
        }
    }

    /**
     * 初始化方法引用映射
     */
    private void initMethodReferenceMap() {
        methodReferenceMap = new HashMap<>();

        // 签约解约相关
        methodReferenceMap.put("handle600001Sign", requestHandlerService::handle600001Sign);
        methodReferenceMap.put("handle600002Unsign", requestHandlerService::handle600002Unsign);
        methodReferenceMap.put("handle600003AccountTrace", requestHandlerService::handle600003AccountTrace);
        methodReferenceMap.put("handle600010Login", requestHandlerService::handle600010Login);
        methodReferenceMap.put("handle900010ResetPassword", requestHandlerService::handle900010ResetPassword);
        methodReferenceMap.put("handle600011FundPasswordLogin", requestHandlerService::handle600011FundPasswordLogin);

        // 通用查询接口
        methodReferenceMap.put("handleCommQuery", requestHandlerService::handleCommQuery);

        // 特殊处理接口
        methodReferenceMap.put("handle600040UserInfo", requestHandlerService::handle600040UserInfo);

        // 会话相关
        methodReferenceMap.put("handle600401ExtendSession", requestHandlerService::handle600401ExtendSession);

        // 公告相关
        methodReferenceMap.put("handle600090InsertNotice", requestHandlerService::handle600090InsertNotice);
        methodReferenceMap.put("handle600091SelectNotice", requestHandlerService::handle600091SelectNotice);
        methodReferenceMap.put("handle600400SelectNoticeTx", requestHandlerService::handle600400SelectNoticeTx);
        methodReferenceMap.put("handle600098DeleteNotice", requestHandlerService::handle600098DeleteNotice);
        methodReferenceMap.put("handle600099UpdateNotice", requestHandlerService::handle600099UpdateNotice);

        // Token相关
        methodReferenceMap.put("handle700001TokenExchange", requestHandlerService::handle700001TokenExchange);

        // 测试接口
        methodReferenceMap.put("handleTestForSession", requestHandlerService::handleTestForSession);
        methodReferenceMap.put("handleGetClientIp", requestHandlerService::handleGetClientIp);
        methodReferenceMap.put("handleGetNginxInfo", requestHandlerService::handleGetNginxInfo);
        methodReferenceMap.put("handleClearNginxXpAns", requestHandlerService::handleClearNginxXpAns);

        // 测试环境专用接口
        methodReferenceMap.put("handle400001QueryAccount", requestHandlerService::handle400001QueryAccount);
        methodReferenceMap.put("handle400002QueryUserId", requestHandlerService::handle400002QueryUserId);
        methodReferenceMap.put("handle400020CheckSmsCode", requestHandlerService::handle400020CheckSmsCode);
    }

    /**
     * 获取服务处理器
     */
    public ServiceHandler getServiceHandler(String serviceId) {
        return handlerCache.get(serviceId);
    }

    /**
     * 获取所有支持的服务ID
     */
    public Map<String, String> getAllServiceMappings() {
        return mappingProperties.getAllMappings();
    }

    /**
     * 检查服务ID是否支持
     */
    public boolean isServiceSupported(String serviceId) {
        return mappingProperties.isServiceSupported(serviceId);
    }
}
