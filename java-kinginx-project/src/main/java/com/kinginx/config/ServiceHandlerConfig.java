package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import com.kinginx.service.ServiceHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务处理器配置
 * 对应Python项目中的bpagentspd.py中的g_reqBpagentFunctionDict
 * 从YAML配置文件中读取映射关系
 */
@Configuration
public class ServiceHandlerConfig {

    @Autowired
    private RequestHandlerService requestHandlerService;

    @Autowired
    private ServiceHandlerMappingProperties mappingProperties;



    /**
     * 运行时方法映射缓存
     */
    private Map<String, Method> methodCache = new HashMap<>();

    @PostConstruct
    public void initMethodCache() {
        Class<?> serviceClass = requestHandlerService.getClass();
        Map<String, String> mappings = mappingProperties.getServiceHandlerMapping();

        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String serviceId = entry.getKey();
            String methodName = entry.getValue();
            try {
                Method method = serviceClass.getMethod(methodName, String.class, Map.class);
                methodCache.put(serviceId, method);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException("Handler method not found: " + methodName + " for service: " + serviceId, e);
            }
        }
    }

    /**
     * 获取服务处理方法
     */
    public Method getHandlerMethod(String serviceId) {
        return methodCache.get(serviceId);
    }

    /**
     * 获取所有支持的服务ID
     */
    public Map<String, String> getAllServiceMappings() {
        return mappingProperties.getAllMappings();
    }

    /**
     * 检查服务ID是否支持
     */
    public boolean isServiceSupported(String serviceId) {
        return mappingProperties.isServiceSupported(serviceId);
    }
}
