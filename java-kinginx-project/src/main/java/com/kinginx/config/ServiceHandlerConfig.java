package com.kinginx.config;

import com.kinginx.service.RequestHandlerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务处理器配置
 * 对应Python项目中的bpagentspd.py中的g_reqBpagentFunctionDict
 */
@Configuration
public class ServiceHandlerConfig {

    @Autowired
    private RequestHandlerService requestHandlerService;

    /**
     * 功能号对应的处理方法映射
     * 对应Python中的g_reqBpagentFunctionDict
     */
    private static final Map<String, String> SERVICE_HANDLER_MAPPING = new HashMap<String, String>() {{
        // 签约解约相关
        put("600001", "handle600001Sign");                    // 签约接口
        put("600002", "handle600002Unsign");                  // 解约接口  
        put("600003", "handle600003AccountTrace");            // 账户留痕接口
        put("600010", "handle600010Login");                   // 登录接口
        put("900010", "handle900010ResetPassword");           // 重置/修改密码接口
        put("600011", "handle600011FundPasswordLogin");       // 资金密码登录接口
        
        // 通用查询接口 - 对应pyreqbpagentCommquery
        put("600140", "handleCommQuery");                     // 委托下单
        put("600141", "handleCommQuery");                     // 委托撤单
        put("600410", "handleCommQuery");                     // 产品查询
        put("600143", "handleCommQuery");                     // 委托修改
        put("600150", "handleCommQuery");                     // 委托查询
        put("600043", "handleCommQuery");                     // 证券信息查询
        put("600045", "handleCommQuery");                     // 创业板权限查询
        put("600046", "handleCommQuery");                     // 创业板开通
        put("600020", "handleCommQuery");                     // 证券代码查询
        put("600049", "handleCommQuery");                     // 新股申购
        put("600050", "handleCommQuery");                     // 新股查询
        put("600060", "handleCommQuery");                     // 股东账户查询
        put("600070", "handleCommQuery");                     // 适当性查询
        put("600080", "handleCommQuery");                     // 根据证件号查询资金账号
        put("600200", "handleCommQuery");                     // 持仓查询
        put("600120", "handleCommQuery");                     // 资金查询
        put("600123", "handleCommQuery");                     // 资产查询
        put("600124", "handleCommQuery");                     // 新股缴款查询
        put("600160", "handleCommQuery");                     // 当日委托查询
        put("600161", "handleCommQuery");                     // 当日成交查询
        put("600170", "handleCommQuery");                     // 历史委托查询
        put("600172", "handleCommQuery");                     // 历史委托查询2
        put("600174", "handleCommQuery");                     // 委托撤单2
        put("600180", "handleCommQuery");                     // 历史成交查询
        put("600041", "handleCommQuery");                     // 风险测评查询
        put("600042", "handleCommQuery");                     // ST股票权限查询
        put("600230", "handleCommQuery");                     // 银证转账
        put("600030", "handleCommQuery");                     // 营业部信息查询
        put("600270", "handleCommQuery");                     // 银证转账查询
        put("600320", "handleCommQuery");                     // 新股信息查询
        put("600340", "handleCommQuery");                     // 配股查询
        put("600380", "handleCommQuery");                     // 新股额度查询
        put("600390", "handleCommQuery");                     // 资金流水查询
        put("600391", "handleCommQuery");                     // 证券流水查询
        put("600190", "handleCommQuery");                     // 交割单查询
        put("600055", "handleCommQuery");                     // 证券信息查询2
        put("600061", "handleCommQuery");                     // 股东账户开户查询
        put("600063", "handleCommQuery");                     // 证券信息查询3
        put("600064", "handleCommQuery");                     // 证券信息查询4
        put("600330", "handleCommQuery");                     // 新股配号查询
        put("600360", "handleCommQuery");                     // 银行账户查询
        put("700713", "handleCommQuery");                     // 冻结资金查询
        put("700714", "handleCommQuery");                     // 冻结资金操作
        put("601160", "handleCommQuery");                     // 港股通委托查询
        put("601170", "handleCommQuery");                     // 港股通历史委托
        put("601180", "handleCommQuery");                     // 港股通历史成交
        put("601190", "handleCommQuery");                     // 港股通交割单
        put("500401", "handleCommQuery");                     // 港股通额度查询
        put("500402", "handleCommQuery");                     // 港股通汇率查询
        put("600121", "handleCommQuery");                     // 新股申购额度查询
        put("500403", "handleCommQuery");                     // 港股通标的查询
        put("500062", "handleCommQuery");                     // 科创板权限开通
        put("500060", "handleCommQuery");                     // 科创板权限查询
        put("500061", "handleCommQuery");                     // 科创板签署协议
        put("500063", "handleCommQuery");                     // 科创板基础知识测试
        put("800011", "handleCommQuery");                     // 短信验证码发送
        put("800012", "handleCommQuery");                     // 短信验证码校验
        put("800013", "handleCommQuery");                     // 客户资料更新
        put("800014", "handleCommQuery");                     // 客户信息查询
        put("800015", "handleCommQuery");                     // 客户信息查询2
        put("600044", "handleCommQuery");                     // 客户资料更新2
        put("600047", "handleCommQuery");                     // 客户资料更新3
        put("601140", "handleCommQuery");                     // 港股通委托下单
        put("601150", "handleCommQuery");                     // 港股通委托撤单
        put("600210", "handleCommQuery");                     // 银证转账2
        put("600220", "handleCommQuery");                     // 银证转账3
        put("600052", "handleCommQuery");                     // 证券信息查询5
        put("600026", "handleCommQuery");                     // 证券信息查询6
        
        // 特殊处理接口 - 对应pyreqbpagent600040
        put("600040", "handle600040UserInfo");                // 用户信息查询接口
        
        // 会话相关
        put("600401", "handle600401ExtendSession");           // 延长会话
        
        // 公告相关
        put("600090", "handle600090InsertNotice");            // 插入券商公告
        put("600091", "handle600091SelectNotice");            // 查询公告
        put("600400", "handle600400SelectNoticeTx");          // 腾讯查询公告
        put("600098", "handle600098DeleteNotice");            // 删除券商公告
        put("600099", "handle600099UpdateNotice");            // 修改券商公告
        
        // Token相关
        put("700001", "handle700001TokenExchange");           // session换国金网厅token
        
        // 测试接口
        put("testforsession", "handleTestForSession");        // 测试会话
        put("getclientip", "handleGetClientIp");              // 获取客户IP信息
        put("GetNginxInfo", "handleGetNginxInfo");            // 获取应答字典残留应答
        put("ClearNginxXpAns", "handleClearNginxXpAns");      // 清空残留应答
        
        // 测试环境专用接口
        put("400001", "handle400001QueryAccount");            // 查询客户号
        put("400002", "handle400002QueryUserId");             // 查询腾讯ID
        put("400020", "handle400020CheckSmsCode");            // 查询验证码
        
        // 新客理财相关
        put("800060", "handleCommQuery");                     // 查询TA账户信息
        put("800049", "handleCommQuery");                     // 开通TA账户
        put("800063", "handleCommQuery");                     // 理财产品适当性匹配查询
        put("800064", "handleCommQuery");                     // 签署接口
        put("800410", "handleCommQuery");                     // 查询新客理财产品信息
        put("600171", "handleCommQuery");                     // 持仓记录查询
        put("600173", "handleCommQuery");                     // 持仓记录查询2
        put("800140", "handleCommQuery");                     // 新增理财委托接口
        put("800061", "handleCommQuery");                     // 协议留痕接口
        put("800160", "handleCommQuery");                     // 委托记录查询接口
        
        // 风险相关
        put("600034", "handleCommQuery");                     // 查询风险股票提示信息
    }};

    /**
     * 运行时方法映射缓存
     */
    private Map<String, Method> methodCache = new HashMap<>();

    @PostConstruct
    public void initMethodCache() {
        Class<?> serviceClass = requestHandlerService.getClass();
        for (Map.Entry<String, String> entry : SERVICE_HANDLER_MAPPING.entrySet()) {
            String serviceId = entry.getKey();
            String methodName = entry.getValue();
            try {
                Method method = serviceClass.getMethod(methodName, String.class, Map.class);
                methodCache.put(serviceId, method);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException("Handler method not found: " + methodName + " for service: " + serviceId, e);
            }
        }
    }

    /**
     * 获取服务处理方法
     */
    public Method getHandlerMethod(String serviceId) {
        return methodCache.get(serviceId);
    }

    /**
     * 获取所有支持的服务ID
     */
    public static Map<String, String> getAllServiceMappings() {
        return new HashMap<>(SERVICE_HANDLER_MAPPING);
    }

    /**
     * 检查服务ID是否支持
     */
    public static boolean isServiceSupported(String serviceId) {
        return SERVICE_HANDLER_MAPPING.containsKey(serviceId);
    }
}
