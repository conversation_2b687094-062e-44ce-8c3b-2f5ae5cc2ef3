package com.kinginx.config;

import com.google.common.collect.ImmutableMap;
import com.kinginx.service.RequestHandlerService;
import com.kinginx.service.ServiceHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * 服务处理器配置
 * 对应Python项目中的bpagentspd.py中的g_reqBpagentFunctionDict
 * 从YAML配置文件中读取映射关系
 */
@Slf4j
@Configuration
public class ServiceHandlerConfig {

    /**
     * 方法名到方法引用的映射
     */
    private Map<String, ServiceHandler> handlerCache;
    @Autowired
    private RequestHandlerService requestHandlerService;

    @Autowired
    private ServiceHandlerMappingProperties mappingProperties;

    @PostConstruct
    public void initHandlerCache() {
        log.info("初始化服务处理器配置...");
        log.info("mappingProperties: {}", mappingProperties);
        // 初始化方法引用映射
        initMethodReferenceMap();
        // log handler cache
        log.info("handler cache: {}", handlerCache);
    }

    /**
     * 初始化方法引用映射
     */
    private void initMethodReferenceMap() {
        // 签约解约相关
        handlerCache = ImmutableMap.<String, ServiceHandler>builder()
                .put("handle600001Sign", requestHandlerService::handle600001Sign)
                .put("handle600002Unsign", requestHandlerService::handle600002Unsign)
                .put("handle600003AccountTrace", requestHandlerService::handle600003AccountTrace)
                .put("handle600010Login", requestHandlerService::handle600010Login)
                .put("handle900010ResetPassword", requestHandlerService::handle900010ResetPassword)
                .put("handle600011FundPasswordLogin", requestHandlerService::handle600011FundPasswordLogin)
                // 通用查询接口
                .put("handleCommQuery", requestHandlerService::handleCommQuery)
                // 特殊处理接口
                .put("handle600040UserInfo", requestHandlerService::handle600040UserInfo)
                // 会话相关
                .put("handle600401ExtendSession", requestHandlerService::handle600401ExtendSession)
                // 公告相关
                .put("handle600090InsertNotice", requestHandlerService::handle600090InsertNotice)
                .put("handle600091SelectNotice", requestHandlerService::handle600091SelectNotice)
                .put("handle600400SelectNoticeTx", requestHandlerService::handle600400SelectNoticeTx)
                .put("handle600098DeleteNotice", requestHandlerService::handle600098DeleteNotice)
                .put("handle600099UpdateNotice", requestHandlerService::handle600099UpdateNotice)
                // Token相关
                .put("handle700001TokenExchange", requestHandlerService::handle700001TokenExchange)
                // 测试接口
                .put("handleTestForSession", requestHandlerService::handleTestForSession)
                .put("handleGetClientIp", requestHandlerService::handleGetClientIp)
                .put("handleGetNginxInfo", requestHandlerService::handleGetNginxInfo)
                .put("handleClearNginxXpAns", requestHandlerService::handleClearNginxXpAns)
                // 测试环境专用接口
                .put("handle400001QueryAccount", requestHandlerService::handle400001QueryAccount)
                .put("handle400002QueryUserId", requestHandlerService::handle400002QueryUserId)
                .put("handle400020CheckSmsCode", requestHandlerService::handle400020CheckSmsCode).build();
    }

    /**
     * 获取服务处理器
     */
    public ServiceHandler getServiceHandler(String serviceId) {
        return handlerCache.get(mappingProperties.getHandlerMethodName(serviceId));
    }

    public boolean isServiceSupported(String serviceId) {
        return mappingProperties.isServiceSupported(serviceId) &&
                handlerCache.containsKey(mappingProperties.getHandlerMethodName(serviceId));
    }

    public Map<String, String> getAllServiceMappings() {
        return mappingProperties.getServiceHandlerMapping();
    }
}
