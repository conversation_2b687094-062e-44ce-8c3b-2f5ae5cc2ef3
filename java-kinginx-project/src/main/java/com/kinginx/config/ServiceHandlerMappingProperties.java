package com.kinginx.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务处理器映射配置属性
 * 从YAML文件中读取服务ID到处理方法的映射关系
 */
@Component
@ConfigurationProperties(prefix = "")
public class ServiceHandlerMappingProperties {

    /**
     * 服务处理器映射
     * key: 服务ID
     * value: 处理方法名
     */
    private Map<String, String> serviceHandlerMapping = new HashMap<>();

    public Map<String, String> getServiceHandlerMapping() {
        return serviceHandlerMapping;
    }

    public void setServiceHandlerMapping(Map<String, String> serviceHandlerMapping) {
        this.serviceHandlerMapping = serviceHandlerMapping;
    }

    /**
     * 检查服务是否支持
     */
    public boolean isServiceSupported(String serviceId) {
        return serviceHandlerMapping.containsKey(serviceId);
    }

    /**
     * 获取服务对应的处理方法名
     */
    public String getHandlerMethodName(String serviceId) {
        return serviceHandlerMapping.get(serviceId);
    }

    /**
     * 获取所有支持的服务ID
     */
    public Map<String, String> getAllMappings() {
        return new HashMap<>(serviceHandlerMapping);
    }
}
