package com.kinginx.service.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 参数转换器
 * 对应Python项目中的translateReq和translateAns函数
 * 负责将请求参数从源格式转换为目标柜台系统格式，以及将响应数据转换回标准格式
 */
@Component
public class ParameterTranslator {

    private static final Logger logger = LoggerFactory.getLogger(ParameterTranslator.class);

    // 函数插件正则表达式
    private static final Pattern FUNCTION_PLUGIN_PATTERN = Pattern.compile("functionPlugIn\\(([^)]+)\\)");
    // 功能字典配置 - 对应Python中的functiondict
    // 这里只实现410301相关的配置，其他功能号可以按需添加
    private static final Map<String, Map<String, Object>> FUNCTION_DICT = new HashMap<>();

    static {
        // 410301签约配置
        Map<String, Object> config410301 = new HashMap<>();
        config410301.put("dstfuncid", "331100");

        List<Map<String, Object>> reqConfig = new ArrayList<>();
        reqConfig.add(createReqConfig("biznum#", "biznum", "410301"));
        reqConfig.add(createReqConfig("op_branch_no", "op_branch_no", "5010"));
        reqConfig.add(createReqConfig("trdpwd", "trdpwd"));
        reqConfig.add(createReqConfigWithFunction("password", "password",
                "functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)"));
        reqConfig.add(createReqConfig("op_entrust_way#", "op_entrust_way", "h"));
        reqConfig.add(createReqConfigWithFunction("TCC", "op_station", "functionPlugIn(tranOpStation,TCC)"));
        reqConfig.add(createReqConfig("input_content", "input_content", "1"));
        reqConfig.add(createReqConfig("inputid", "account_content"));
        reqConfig.add(createReqConfig("content_type", "content_type", "0"));

        config410301.put("req", reqConfig);
        FUNCTION_DICT.put("410301", config410301);

        // 410324查询权限配置
        Map<String, Object> config410324 = new HashMap<>();
        config410324.put("dstfuncid", "410324");

        List<Map<String, Object>> req410324 = new ArrayList<>();
        req410324.add(createReqConfig("funcid", "funcid"));
        req410324.add(createReqConfig("custid", "custid"));
        req410324.add(createReqConfig("custorgid", "custorgid"));
        req410324.add(createReqConfigWithFunction("trdpwd", "trdpwd",
                "functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS,\"kdencode\",\"410301\")"));
        req410324.add(createReqConfig("orgid", "orgid"));
        req410324.add(createReqConfig("operway#", "operway", "operway"));
        req410324.add(createReqConfig("ext#-1", "ext", "0"));
        req410324.add(createReqConfig("netaddr", "netaddr"));
        req410324.add(createReqConfig("netaddr2", "netaddr2"));
        req410324.add(createReqConfig("inputid", "fundid"));
        req410324.add(createReqConfig("custcert", "custcert"));

        List<Map<String, Object>> ans410324 = new ArrayList<>();
        ans410324.add(createAnsConfig("name", "name"));
        ans410324.add(createAnsConfig("fundid", "fundid"));
        ans410324.add(createAnsConfig("operway", "operway"));

        config410324.put("req", req410324);
        config410324.put("ans", ans410324);
        FUNCTION_DICT.put("410324", config410324);
    }

    private static Map<String, Object> createReqConfig(String srcKey, String dstKey) {
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("dst", dstKey);
        config.put(srcKey, mapping);
        return config;
    }

    private static Map<String, Object> createReqConfig(String srcKey, String dstKey, String defaultValue) {
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("dst", dstKey);
        mapping.put("default", defaultValue);
        config.put(srcKey, mapping);
        return config;
    }

    private static Map<String, Object> createReqConfigWithFunction(String srcKey, String dstKey,
            String functionPlugIn) {
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("dst", dstKey);
        mapping.put("functionPlugIn", functionPlugIn);
        config.put(srcKey, mapping);
        return config;
    }

    private static Map<String, Object> createAnsConfig(String srcKey, String dstKey) {
        Map<String, Object> config = new HashMap<>();
        Map<String, Object> mapping = new HashMap<>();
        mapping.put("dst", dstKey);
        config.put(srcKey, mapping);
        return config;
    }

    /**
     * 入参转换 - 对应Python中的translateReq函数
     * #适配器json入参转换,遇到函数,默认值,会修改原本的请求字典
     * # 入参:原功能号,请求字典
     * #出参 code 0 succ
     * #出参 msg  转换请求的错误消息
     * #出参dst_serviceid目标功能号
     * #列表出参dst_reqdict  转换后的请求字典
     */
    public TranslateResult translateReq(String srcServiceId, Map<String, Object> reqDict) {
        try {
            logger.debug("开始入参转换, serviceId: {}", srcServiceId);

            // 浅拷贝请求字典
            Map<String, Object> reqDictTmp = new HashMap<>(reqDict);
            Map<String, Object> dstReqDict = new HashMap<>();

            // 检查是否需要参数转换
            if (!FUNCTION_DICT.containsKey(srcServiceId) || !FUNCTION_DICT.get(srcServiceId).containsKey("req")) {
                dstReqDict.putAll(reqDictTmp);
                return new TranslateResult(0, "无需参数转换", srcServiceId, dstReqDict);
            }

            // 获取目标功能号
            Map<String, Object> funcConfig = FUNCTION_DICT.get(srcServiceId);
            if (!funcConfig.containsKey("dstfuncid")) {
                return new TranslateResult(-3001, "入参转换未找到目标功能号，请检查" + srcServiceId + "的转换字典", null,
                        null);
            }

            String dstServiceId = (String)funcConfig.get("dstfuncid");
            List<Map<String, Object>> reqConfig = (List<Map<String, Object>>)funcConfig.get("req");

            // 执行参数转换
            for (Map<String, Object> eachReqKey : reqConfig) {
                for (Map.Entry<String, Object> entry : eachReqKey.entrySet()) {
                    String srcKey = entry.getKey();
                    Map<String, Object> config = (Map<String, Object>)entry.getValue();

                    processRequestParameter(srcKey, config, reqDictTmp, dstReqDict);
                }
            }

            logger.debug("入参转换完成, srcServiceId: {}, dstServiceId: {}", srcServiceId, dstServiceId);
            return new TranslateResult(0, "入参转换成功", dstServiceId, dstReqDict);

        } catch (Exception e) {
            logger.error("入参转换异常, serviceId: {}", srcServiceId, e);
            return new TranslateResult(-3002, "入参转换异常: " + e.getMessage(), null, null);
        }
    }

    /**
     * 出参转换 - 对应Python中的translateAns函数
     */
    public TranslateResult translateAns(String srcServiceId, Map<String, Object> reqDict,
            List<Map<String, Object>> srcAnsDictList, List<Map<String, Object>> dstAnsDictList) {
        try {
            logger.debug("开始出参转换, serviceId: {}", srcServiceId);

            if (srcAnsDictList == null || srcAnsDictList.isEmpty()) {
                return new TranslateResult(0, "无应答", srcServiceId, null);
            }

            // 检查是否需要应答参数转换
            if (!FUNCTION_DICT.containsKey(srcServiceId) || !FUNCTION_DICT.get(srcServiceId).containsKey("ans")) {
                dstAnsDictList.addAll(srcAnsDictList);
                return new TranslateResult(0, "无需应答参数转换", srcServiceId, null);
            }

            Map<String, Object> funcConfig = FUNCTION_DICT.get(srcServiceId);
            List<Map<String, Object>> ansConfig = (List<Map<String, Object>>)funcConfig.get("ans");

            // 处理每条应答记录
            for (Map<String, Object> eachRow : srcAnsDictList) {
                Map<String, Object> dictOneAns = new HashMap<>();

                for (Map<String, Object> eachAnsValue : ansConfig) {
                    for (Map.Entry<String, Object> entry : eachAnsValue.entrySet()) {
                        String srcKey = entry.getKey();
                        Map<String, Object> config = (Map<String, Object>)entry.getValue();

                        processResponseParameter(srcKey, config, eachRow, dictOneAns);
                    }
                }

                if (!dictOneAns.isEmpty()) {
                    dstAnsDictList.add(new HashMap<>(dictOneAns));
                }
            }

            logger.debug("出参转换完成, serviceId: {}, 转换记录数: {}", srcServiceId, dstAnsDictList.size());
            return new TranslateResult(0, "应答参数转换成功", srcServiceId, null);

        } catch (Exception e) {
            logger.error("出参转换异常, serviceId: {}", srcServiceId, e);
            return new TranslateResult(-3003, "出参转换异常: " + e.getMessage(), null, null);
        }
    }

    /**
     * 处理请求参数
     */
    private void processRequestParameter(String srcKey, Map<String, Object> config, Map<String, Object> srcDict,
            Map<String, Object> dstDict) {
        String dstKey = (String)config.get("dst");

        // 处理函数插件
        if (config.containsKey("functionPlugIn")) {
            String functionPlugIn = (String)config.get("functionPlugIn");
            Object result = processFunctionPlugin(functionPlugIn, srcDict);
            if (result != null) {
                dstDict.put(dstKey, result);
                srcDict.put(srcKey, result); // 更新源字典供后续使用
            }
            return;
        }

        // 处理普通字段映射
        String cleanSrcKey = srcKey.replaceAll("#.*$", ""); // 移除#后缀

        if (srcDict.containsKey(cleanSrcKey)) {
            Object value = srcDict.get(cleanSrcKey);
            if (value != null && !value.toString().isEmpty()) {
                dstDict.put(dstKey, value);
            } else if (config.containsKey("default")) {
                Object defaultValue = config.get("default");
                dstDict.put(dstKey, defaultValue);
                srcDict.put(cleanSrcKey, defaultValue);
            } else {
                dstDict.put(dstKey, "");
            }
        } else if (config.containsKey("default")) {
            Object defaultValue = config.get("default");
            dstDict.put(dstKey, defaultValue);
            srcDict.put(cleanSrcKey, defaultValue);
        } else {
            dstDict.put(dstKey, "");
        }

        // 处理字典转换
        if (config.containsKey("dict")) {
            Map<String, Object> dictConfig = (Map<String, Object>)config.get("dict");
            Object currentValue = dstDict.get(dstKey);
            if (currentValue != null && dictConfig.containsKey(currentValue.toString())) {
                dstDict.put(dstKey, dictConfig.get(currentValue.toString()));
            }
        }
    }

    /**
     * 处理响应参数
     */
    private void processResponseParameter(String srcKey, Map<String, Object> config, Map<String, Object> srcRow,
            Map<String, Object> dstRow) {
        String dstKey = (String)config.get("dst");

        // 处理函数插件
        if (config.containsKey("functionPlugIn")) {
            String functionPlugIn = (String)config.get("functionPlugIn");
            Object result = processFunctionPlugin(functionPlugIn, srcRow);
            if (result != null) {
                dstRow.put(dstKey, result);
                srcRow.put(srcKey, result);
            }
            return;
        }

        // 处理普通字段映射
        if (srcRow.containsKey(srcKey)) {
            Object value = srcRow.get(srcKey);
            if (value != null && !value.toString().isEmpty()) {
                dstRow.put(dstKey, value);
            } else if (config.containsKey("default")) {
                Object defaultValue = config.get("default");
                dstRow.put(dstKey, defaultValue);
                srcRow.put(srcKey, defaultValue);
            } else {
                dstRow.put(dstKey, "");
            }
        } else if (config.containsKey("default")) {
            Object defaultValue = config.get("default");
            dstRow.put(dstKey, defaultValue);
            srcRow.put(srcKey, defaultValue);
        } else {
            dstRow.put(dstKey, "");
        }

        // 处理字典转换
        if (config.containsKey("dict")) {
            Map<String, Object> dictConfig = (Map<String, Object>)config.get("dict");
            Object currentValue = dstRow.get(dstKey);
            if (currentValue != null && dictConfig.containsKey(currentValue.toString())) {
                dstRow.put(dstKey, dictConfig.get(currentValue.toString()));
            }
        }
    }

    /**
     * 处理函数插件
     * 对应Python中的functionPlugIn函数
     */
    private Object processFunctionPlugin(String functionPlugIn, Map<String, Object> dataDict) {
        try {
            Matcher matcher = FUNCTION_PLUGIN_PATTERN.matcher(functionPlugIn);
            if (!matcher.find()) {
                return null;
            }

            String params = matcher.group(1);
            String[] paramArray = params.split(",");
            if (paramArray.length == 0) {
                return null;
            }

            String functionName = paramArray[0].trim();
            List<String> paramList = new ArrayList<>();

            for (int i = 1; i < paramArray.length; i++) {
                String param = paramArray[i].trim();
                if (param.startsWith("\"") && param.endsWith("\"")) {
                    // 字符串常量
                    paramList.add(param.substring(1, param.length() - 1));
                } else if (dataDict.containsKey(param)) {
                    // 从数据字典中获取值
                    Object value = dataDict.get(param);
                    paramList.add(value != null ? value.toString() : "");
                } else {
                    // 默认空字符串
                    paramList.add("");
                }
            }

            // 调用具体的函数处理逻辑
            return executeFunctionPlugin(functionName, paramList, dataDict);

        } catch (Exception e) {
            logger.error("处理函数插件异常: {}", functionPlugIn, e);
            return null;
        }
    }

    /**
     * 执行具体的函数插件逻辑
     */
    private Object executeFunctionPlugin(String functionName, List<String> params, Map<String, Object> dataDict) {
        switch (functionName) {
            case "WSMultiEncodeByChannel":
                // 密码加密处理
                return handlePasswordEncoding(params);
            case "tranOpStation":
                // TCC操作站点转换
                return handleOpStationTransform(params);
            case "TimeFormat":
                // 时间格式化
                return handleTimeFormat(params);
            case "FormatFloat":
                // 浮点数格式化
                return handleFloatFormat(params);
            case "WSGetCustid":
            case "WSGetOrgid":
            case "WSGetTrdpwd":
            case "WSGetFundid":
                // 从SESSION_JSON中获取信息
                return handleSessionExtraction(functionName, params, dataDict);
            default:
                logger.warn("未实现的函数插件: {}", functionName);
                return params.isEmpty() ? "" : params.get(0);
        }
    }

    /**
     * 处理密码编码
     */
    private String handlePasswordEncoding(List<String> params) {
        if (params.isEmpty()) {
            return "";
        }
        // 这里应该实现具体的密码编码逻辑
        // 暂时返回原值，实际应该调用加密服务
        return params.get(0);
    }

    /**
     * 处理操作站点转换
     */
    private String handleOpStationTransform(List<String> params) {
        if (params.isEmpty()) {
            return "";
        }

        String tcc = params.get(0);
        if (!StringUtils.hasText(tcc)) {
            return "";
        }

        // 对应Python中的tranOpStation函数逻辑
        if (tcc.startsWith("MA") || tcc.startsWith("MI")) {
            return tcc;
        }

        String[] parts = tcc.split("\\|");
        if (parts.length >= 3) {
            return parts[2] + ";" + parts[1] + ";" + parts[0];
        }

        return tcc;
    }

    /**
     * 处理时间格式化
     */
    private String handleTimeFormat(List<String> params) {
        if (params.isEmpty()) {
            return "";
        }
        // 实现时间格式化逻辑
        return params.get(0);
    }

    /**
     * 处理浮点数格式化
     */
    private String handleFloatFormat(List<String> params) {
        if (params.size() < 2) {
            return params.isEmpty() ? "" : params.get(0);
        }
        // 实现浮点数格式化逻辑
        return params.get(0);
    }

    /**
     * 处理SESSION信息提取
     */
    private String handleSessionExtraction(String functionName, List<String> params, Map<String, Object> dataDict) {
        // 这里应该从SESSION_JSON中提取相应信息
        // 暂时返回空字符串，实际应该解析SESSION数据
        return "";
    }

    /**
     * 转换结果封装类
     */
    @Data
    @AllArgsConstructor
    public static class TranslateResult {
        private int code;
        private String message;
        private String dstServiceId;
        private Map<String, Object> translatedData;
    }
}
