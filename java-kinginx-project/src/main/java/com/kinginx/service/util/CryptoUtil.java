package com.kinginx.service.util;

import com.kinginx.common.constants.ServiceConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Security;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;

/**
 * 加密工具类
 * 对应Python项目中的加密解密功能
 */
@Slf4j
@Component
public class CryptoUtil {

    public static final int MAX_DECRYPT_BLOCK = 128;
    private static final Logger logger = LoggerFactory.getLogger(CryptoUtil.class);

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    @Value("${kinginx.encryption.rsa.private-key-path}")
    private String privateKeyPath;
    @Value("${kinginx.encryption.rsa.public-key-path}")
    private String publicKeyPath;
    @Value("${kinginx.encryption.des.key}")
    private String desKey;
    private RSAPrivateKey rsaPrivateKey;
    private RSAPublicKey rsaPublicKey;

    private static RSAPrivateKey convertToPrivateKey(PrivateKeyInfo privateKeyInfo) throws Exception {
        JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");
        PrivateKey privateKey = converter.getPrivateKey(privateKeyInfo);

        if (!(privateKey instanceof RSAPrivateKey)) {
            throw new IllegalArgumentException("密钥不是RSA私钥");
        }
        return (RSAPrivateKey)privateKey;
    }

    @PostConstruct
    public void init() {
        try {
            loadRSAKeys();
            logger.info("加密工具初始化成功");
        } catch (Exception e) {
            logger.error("加密工具初始化失败", e);
        }
    }

    /**
     * 加载RSA密钥对
     */
    private void loadRSAKeys() throws Exception {
        // 加载私钥
        privateKey();

        // 加载公钥
        if (publicKeyPath != null && !publicKeyPath.isEmpty()) {
            try {
                ClassPathResource publicKeyResource = new ClassPathResource(publicKeyPath.replace("classpath:", ""));
                byte[] publicKeyBytes = readAllBytesFromInputStream(publicKeyResource.getInputStream());
                String publicKeyContent =
                        new String(publicKeyBytes, StandardCharsets.UTF_8).replace("-----BEGIN PUBLIC KEY-----", "")
                                .replace("-----END PUBLIC KEY-----", "").replaceAll("\\s", "");

                byte[] publicKeyDecoded = Base64.decodeBase64(publicKeyContent);
                X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(publicKeyDecoded);
                KeyFactory keyFactory = KeyFactory.getInstance("RSA");
                rsaPublicKey = (RSAPublicKey)keyFactory.generatePublic(publicKeySpec);

                logger.info("RSA公钥加载成功");
            } catch (Exception e) {
                logger.warn("RSA公钥加载失败: {}", e.getMessage());
            }
        }
    }

    private void privateKey() {
        try (PEMParser pemParser = new PEMParser(
                new FileReader(new ClassPathResource(privateKeyPath.replace("classpath:", "")).getFile()))) {
            // 读取PEM对象
            Object pemObject = pemParser.readObject();

            if (pemObject instanceof PrivateKeyInfo) {
                // 直接处理PKCS#8格式
                PrivateKeyInfo privateKeyInfo = (PrivateKeyInfo)pemObject;
                rsaPrivateKey = convertToPrivateKey(privateKeyInfo);
            } else if (pemObject instanceof PEMKeyPair) {
                // 处理PKCS#1格式
                PEMKeyPair pemKeyPair = (PEMKeyPair)pemObject;
                PrivateKeyInfo privateKeyInfo = pemKeyPair.getPrivateKeyInfo();
                rsaPrivateKey = convertToPrivateKey(privateKeyInfo);
            } else {
                throw new IllegalArgumentException("不支持的PEM类型: " + pemObject.getClass().getName());
            }
            // log rsaPrivateKey all fields
            log.info("rsaPrivateKey: {}", rsaPrivateKey);
            log.info("rsaPrivateKey.getModulus(): {}", rsaPrivateKey.getModulus());
            log.info("rsaPrivateKey.getPrivateExponent(): {}", rsaPrivateKey.getPrivateExponent());
            log.info("rsaPrivateKey.getAlgorithm(): {}", rsaPrivateKey.getAlgorithm());
            log.info("rsaPrivateKey.getFormat(): {}", rsaPrivateKey.getFormat());
            log.info("rsaPrivateKey.getEncoded(): {}", rsaPrivateKey.getEncoded());
            // log key length
            log.info("rsaPrivateKey.getModulus().bitLength(): {}", rsaPrivateKey.getModulus().bitLength());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从InputStream读取所有字节 (Java 1.8兼容版本)
     */
    private byte[] readAllBytesFromInputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];

        try {
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            return buffer.toByteArray();
        } finally {
            inputStream.close();
        }
    }

    /**
     * RSA解密
     */
    public byte[] rsaDecrypt(String encryptedData, String transformation) {
        try {
            if (rsaPrivateKey == null) {
                throw new IllegalStateException("RSA私钥未加载");
            }

            Cipher cipher = Cipher.getInstance(transformation);
            cipher.init(Cipher.DECRYPT_MODE, rsaPrivateKey);

            byte[] encryptedBytes = hexToBytes(encryptedData);
            log.info("encryptedBytes: {}", encryptedBytes);
            log.info("encryptedBytes.length: {}", encryptedBytes.length);
            // keep last 128 bytes
            //            encryptedBytes = Arrays.copyOfRange(encryptedBytes, encryptedBytes.length - 128, encryptedBytes.length);
            int inputLen = encryptedBytes.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                    cache = cipher.doFinal(encryptedBytes, offSet, MAX_DECRYPT_BLOCK);
                } else {
                    cache = cipher.doFinal(encryptedBytes, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * MAX_DECRYPT_BLOCK;
            }
            byte[] decryptedData = out.toByteArray();
            out.close();
            return decryptedData;
        } catch (Exception e) {
            logger.error("RSA解密失败", e);
            throw new RuntimeException("RSA解密失败: " + e.getMessage());
        }
    }

    public String rsaDecryptBCD(String encryptedData, String transformation) {
        byte[] decryptedData = rsaDecrypt(encryptedData, transformation);
        // to binary coded decimal
        return bytesToBCD(decryptedData);
    }

    /**
     * 将字节数组转换为BCD码
     */
    private String bytesToBCD(byte[] decryptedData) {
        StringBuilder sb = new StringBuilder();
        for (byte b : decryptedData) {
            // 解码高4位：右移4位后取低4位（值范围0-9）
            int high = (b >> 4) & 0x0F;
            // 解码低4位：直接取低4位（值范围0-9）
            int low = b & 0x0F;
            sb.append(high).append(low);
        }
        return sb.toString();
    }

    /**
     * RSA加密
     */
    public String rsaEncrypt(String plainText) {
        try {
            if (rsaPublicKey == null) {
                throw new IllegalStateException("RSA公钥未加载");
            }

            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, rsaPublicKey);

            byte[] plainBytes = plainText.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = cipher.doFinal(plainBytes);

            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            logger.error("RSA加密失败", e);
            throw new RuntimeException("RSA加密失败: " + e.getMessage());
        }
    }

    /**
     * DES加密
     */
    public String desEncrypt(String plainText, String key) {
        try {
            DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
            SecretKeySpec secretKey = new SecretKeySpec(desKeySpec.getKey(), "DES");

            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] plainBytes = plainText.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = cipher.doFinal(plainBytes);

            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            logger.error("DES加密失败", e);
            throw new RuntimeException("DES加密失败: " + e.getMessage());
        }
    }

    public byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte)((Character.digit(hex.charAt(i), 16) << 4) + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * DES解密
     */
    public String desDecrypt(String encryptedData, String key) {
        try {
            DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));
            SecretKeySpec secretKey = new SecretKeySpec(desKeySpec.getKey(), "DES");

            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] encryptedBytes = hexToBytes(encryptedData);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("DES解密失败", e);
            throw new RuntimeException("DES解密失败: " + e.getMessage());
        }
    }

    /**
     * 加密密码
     * 对应Python中的encrypt_pwd函数
     */
    public String encryptPassword(String encryptedPassword) {
        try {
            // 先RSA解密得到明文密码
            String plainPassword =
                    new String(rsaDecrypt(encryptedPassword, "RSA/ECB/PKCS1Padding"), StandardCharsets.UTF_8);

            // 取密码后6位进行DES加密
            String last6Chars =
                    plainPassword.length() >= 6 ? plainPassword.substring(plainPassword.length() - 6) : plainPassword;

            return desEncrypt(last6Chars, ServiceConstants.DES_KEY_410301);
        } catch (Exception e) {
            logger.error("密码加密失败", e);
            throw new RuntimeException("密码加密失败: " + e.getMessage());
        }
    }

    /**
     * 生成MD5哈希
     */
    public String md5Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (Exception e) {
            logger.error("MD5哈希失败", e);
            throw new RuntimeException("MD5哈希失败: " + e.getMessage());
        }
    }

    /**
     * 生成SHA256哈希
     */
    public String sha256Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();
        } catch (Exception e) {
            logger.error("SHA256哈希失败", e);
            throw new RuntimeException("SHA256哈希失败: " + e.getMessage());
        }
    }

    /**
     * Session解密
     * 对应Python中的session_decrypt函数
     */
    public String sessionDecrypt(String sessionId, String key) {
        try {
            // 使用DES解密session
            return desDecrypt(sessionId, key);
        } catch (Exception e) {
            logger.error("Session解密失败", e);
            throw new RuntimeException("Session解密失败: " + e.getMessage());
        }
    }

    /**
     * MD5加密
     * 对应Python中的md5_encrypt函数
     */
    public String md5Encrypt(String input) {
        return md5Hash(input);
    }

    /**
     * AES加密
     * 对应Python中的AES加密功能
     */
    public String aesEncrypt(String plainText, String key) {
        try {
            // 使用AES/ECB/PKCS5Padding模式
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");

            // 确保key长度为16字节
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            if (keyBytes.length < 16) {
                byte[] paddedKey = new byte[16];
                System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
                keyBytes = paddedKey;
            } else if (keyBytes.length > 16) {
                byte[] truncatedKey = new byte[16];
                System.arraycopy(keyBytes, 0, truncatedKey, 0, 16);
                keyBytes = truncatedKey;
            }

            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            byte[] plainBytes = plainText.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = cipher.doFinal(plainBytes);

            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            logger.error("AES加密失败", e);
            throw new RuntimeException("AES加密失败: " + e.getMessage());
        }
    }

    /**
     * AES解密
     * 对应Python中的AES解密功能
     */
    public String aesDecrypt(String encryptedData, String key) {
        try {
            // 使用AES/ECB/PKCS5Padding模式
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");

            // 确保key长度为16字节
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            if (keyBytes.length < 16) {
                byte[] paddedKey = new byte[16];
                System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
                keyBytes = paddedKey;
            } else if (keyBytes.length > 16) {
                byte[] truncatedKey = new byte[16];
                System.arraycopy(keyBytes, 0, truncatedKey, 0, 16);
                keyBytes = truncatedKey;
            }

            SecretKeySpec secretKey = new SecretKeySpec(keyBytes, "AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            byte[] encryptedBytes = Base64.decodeBase64(encryptedData);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("AES解密失败", e);
            throw new RuntimeException("AES解密失败: " + e.getMessage());
        }
    }

    /**
     * Session加密
     * 对应Python中的session_encrypt函数
     */
    public String sessionEncrypt(String plainText, String key) {
        try {
            // 使用DES加密session，对应Python中的session_encrypt
            return desEncrypt(plainText, key);
        } catch (Exception e) {
            logger.error("Session加密失败", e);
            throw new RuntimeException("Session加密失败: " + e.getMessage());
        }
    }
}
