package com.kinginx.service;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Redis服务
 * 对应Python项目中的Redis操作
 */
@Service
public class RedisService {

    private static final Logger logger = LoggerFactory.getLogger(RedisService.class);

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${kinginx.session.expire-time:28800}")
    private int defaultExpireTime;

    /**
     * 设置Redis键值对
     */
    public void setKey(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            logger.debug("Redis设置键值对成功: key={}", key);
        } catch (Exception e) {
            logger.error("Redis设置键值对失败: key={}", key, e);
            throw new RuntimeException("Redis操作失败: " + e.getMessage());
        }
    }

    /**
     * 设置Redis键值对并指定过期时间
     */
    public void setKeyEx(String key, Object value, int expireSeconds) {
        try {
            redisTemplate.opsForValue().set(key, value, expireSeconds, TimeUnit.SECONDS);
            logger.debug("Redis设置键值对成功: key={}, expire={}", key, expireSeconds);
        } catch (Exception e) {
            logger.error("Redis设置键值对失败: key={}, expire={}", key, expireSeconds, e);
            throw new RuntimeException("Redis操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取Redis键对应的值
     */
    public Object getKey(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            logger.debug("Redis获取键值成功: key={}, hasValue={}", key, value != null);
            return value;
        } catch (Exception e) {
            logger.error("Redis获取键值失败: key={}", key, e);
            throw new RuntimeException("Redis操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取Redis键对应的字符串值
     */
    public String getStringKey(String key) {
        Object value = getKey(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 删除Redis键
     */
    public void deleteKey(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            logger.debug("Redis删除键成功: key={}, deleted={}", key, result);
        } catch (Exception e) {
            logger.error("Redis删除键失败: key={}", key, e);
            throw new RuntimeException("Redis操作失败: " + e.getMessage());
        }
    }

    /**
     * 设置键的过期时间
     */
    public void expireKey(String key, int expireSeconds) {
        try {
            Boolean result = redisTemplate.expire(key, expireSeconds, TimeUnit.SECONDS);
            logger.debug("Redis设置过期时间成功: key={}, expire={}, result={}", key, expireSeconds, result);
        } catch (Exception e) {
            logger.error("Redis设置过期时间失败: key={}, expire={}", key, expireSeconds, e);
            throw new RuntimeException("Redis操作失败: " + e.getMessage());
        }
    }

    /**
     * 检查键是否存在
     */
    public boolean hasKey(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            return result != null && result;
        } catch (Exception e) {
            logger.error("Redis检查键存在失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 设置会话信息
     */
    public void setSessionInfo(String userIdCls, String companyId, String userId, Map<String, Object> sessionValue) {
        String redisKey = userIdCls + companyId + userId;
        setKeyEx(redisKey, sessionValue, defaultExpireTime);
        logger.info("设置会话信息成功: key={}", redisKey);
    }

    /**
     * 获取会话信息
     */
    public Map<String, Object> getSessionInfo(String userIdCls, String companyId, String userId) {
        String redisKey = userIdCls + companyId + userId;
        Object value = getKey(redisKey);
        if (value != null) {
            if (value instanceof Map) {
                return (Map<String, Object>)value;
            } else if (value instanceof String) {
                return JSON.parseObject((String)value, Map.class);
            }
        }
        return null;
    }

    /**
     * 延长会话过期时间
     */
    public void extendSessionExpire(String userIdCls, String companyId, String userId) {
        String redisKey = userIdCls + companyId + userId;
        expireKey(redisKey, defaultExpireTime);
        logger.debug("延长会话过期时间: key={}", redisKey);
    }

    /**
     * 删除会话信息
     */
    public void deleteSessionInfo(String userIdCls, String companyId, String userId) {
        String redisKey = userIdCls + companyId + userId;
        deleteKey(redisKey);
        logger.info("删除会话信息: key={}", redisKey);
    }

    /**
     * 设置操作站点信息
     */
    public void setOpStationInfo(String account, String companyId, String opStation) {
        String redisKey = "op_station_" + companyId + "_" + account;
        setKeyEx(redisKey, opStation, defaultExpireTime);
        logger.debug("设置操作站点信息: key={}", redisKey);
    }

    /**
     * 获取操作站点信息
     */
    public String getOpStationInfo(String account, String companyId) {
        String redisKey = "op_station_" + companyId + "_" + account;
        return getStringKey(redisKey);
    }
}
