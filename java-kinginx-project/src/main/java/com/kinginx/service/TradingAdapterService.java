package com.kinginx.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.service.util.ParameterTranslator;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 交易适配器服务
 * 对应Python项目中的adaptbiz.py模块
 */
@Service
public class TradingAdapterService {

    private static final Logger logger = LoggerFactory.getLogger(TradingAdapterService.class);

    @Value("${kinginx.external.trading-gateway.url}")
    private String tradingGatewayUrl;

    @Value("${kinginx.external.trading-gateway.timeout:30000}")
    private int timeout;

    @Value("${kinginx.external.trading-gateway.mock-enabled:true}")
    private boolean mockEnabled;

    @Autowired
    private ParameterTranslator parameterTranslator;

    /**
     * 410301签约适配器
     * 对应Python中的adapt410301函数
     */
    public CompletableFuture<AdapterResult> adapt410301(String refRequest, Map<String, Object> jsonReq) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始执行410301签约适配器, refRequest: {}", refRequest);

                // Step 1: 先以operway=5调用410301A，校验客户身份
                jsonReq.put("operway", ServiceConstants.OPER_WAY_VERIFY);
                AdapterResult step1Result = callAdapter(refRequest, "410301A", jsonReq);
                if (!ServiceConstants.SUCCESS_CODE.equals(step1Result.getCode())) {
                    return step1Result;
                }

                // 获取orgid字段
                String orgId = "";
                List<Map<String, Object>> rows = step1Result.getRows();
                if (rows != null && !rows.isEmpty()) {
                    orgId = (String)rows.get(0).get("orgid");
                }

                // Step 2: 调用410324查询权限
                jsonReq.put("custorgid", orgId);
                jsonReq.put("orgid", orgId);
                AdapterResult step2Result = callAdapter(refRequest, "410324", jsonReq);
                if (!ServiceConstants.SUCCESS_CODE.equals(step2Result.getCode())) {
                    return step2Result;
                }

                // 检查是否需要增加权限e
                String operway = "";
                List<Map<String, Object>> step2Rows = step2Result.getRows();
                if (step2Rows != null && !step2Rows.isEmpty()) {
                    operway = (String)step2Rows.get(0).get("operway");
                }

                // Step 3: 如果没有权限e，则调用410323增加权限
                if (operway == null || !operway.contains("e")) {
                    jsonReq.put("newoperway", (operway != null ? operway : "") + "e");
                    jsonReq.put("fundid", jsonReq.get("inputid"));
                    jsonReq.put("custid", jsonReq.get("inputid"));

                    AdapterResult step3Result = callAdapter(refRequest, "410323", jsonReq);
                    if (!ServiceConstants.SUCCESS_CODE.equals(step3Result.getCode())) {
                        return step3Result;
                    }
                }

                // Step 4: 再次以operway=e调用410301A，确保权限开通成功
                jsonReq.put("operway", ServiceConstants.OPER_WAY_LOGIN);
                AdapterResult finalResult = callAdapter(refRequest, "410301A", jsonReq);

                logger.info("410301签约适配器执行完成, refRequest: {}, result: {}", refRequest, finalResult.getCode());
                return finalResult;

            } catch (Exception e) {
                logger.error("410301签约适配器执行异常, refRequest: {}", refRequest, e);
                return new AdapterResult(ServiceConstants.ERROR_CODE_ADAPTER_FAIL,
                        "410301签约适配器执行异常: " + e.getMessage(), null);
            }
        });
    }

    /**
     * 通用适配器调用
     * 对应Python中的adaptcommbiz函数，包含完整的translateReq和translateAns操作
     */
    public AdapterResult callAdapter(String refRequest, String serviceId, Map<String, Object> jsonReq) {
        try {
            logger.info("开始调用适配器, serviceId: {}, refRequest: {}", serviceId, refRequest);

            // Step 1: translateReq - 入参转换
            ParameterTranslator.TranslateResult translateReqResult =
                    parameterTranslator.translateReq(serviceId, jsonReq);

            if (translateReqResult.getCode() != 0) {
                String errorMsg =
                        String.format("adapt功能%s入参转换失败:%s", serviceId, translateReqResult.getMessage());
                logger.error(errorMsg);
                return new AdapterResult(ServiceConstants.ERROR_CODE_ADAPTER_FAIL, errorMsg, null);
            }

            Map<String, Object> translatedReq = translateReqResult.getTranslatedData();
            String dstServiceId = translateReqResult.getDstServiceId();

            // Step 2: 特殊处理 - op_station字段清理 (对应Python中的TCC处理逻辑)
            if (isValidServiceForOpStationProcessing(serviceId) && translatedReq.containsKey("op_station")) {
                String opStation = (String)translatedReq.get("op_station");
                if (opStation != null && !opStation.isEmpty()) {
                    // 处理 LIP 字段，替换特定 IP 为 NA
                    opStation = opStation.replace("LIP=127.0.0.1", "LIP=NA");
                    opStation = opStation.replace("LIP=0.0.0.0", "LIP=NA");

                    // 处理 MAC 字段，替换特定值为 NA
                    opStation = opStation.replace("MAC=020000000000", "MAC=NA");
                    opStation = opStation.replace("MAC=020000000001", "MAC=NA");
                    opStation = opStation.replace("MAC=020000000002", "MAC=NA");

                    translatedReq.put("op_station", opStation);
                }
            }

            // Step 3: 构建请求数据并发送到柜台
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("service_id", dstServiceId);
            requestData.put("data", translatedReq);

            String requestJson = JSON.toJSONString(requestData);
            String maskedRequestJson = maskSensitiveData(requestJson);
            logger.info("调用柜台{}的请求参数: {}", dstServiceId, maskedRequestJson);

            // 发送请求到柜台系统 (开发环境使用Mock数据)
            String responseJson = sendRequestToTrading(dstServiceId, requestJson);
            logger.info("调用柜台{}的应答: {}", dstServiceId, responseJson);

            // Step 4: 解析柜台响应
            JSONObject response = JSON.parseObject(responseJson);
            JSONObject answers = response.getJSONArray("ANSWERS").getJSONObject(0);

            String code = answers.getJSONObject("ANS_MSG_HDR").getString("MSG_CODE");
            String message = answers.getJSONObject("ANS_MSG_HDR").getString("MSG_TEXT");

            List<Map<String, Object>> ansCommData = null;
            if (answers.containsKey("ANS_COMM_DATA") && answers.get("ANS_COMM_DATA") != null) {
                ansCommData = (List<Map<String, Object>>)answers.get("ANS_COMM_DATA");
            }

            // 确保ANS_COMM_DATA不为null
            if (ansCommData == null) {
                ansCommData = new ArrayList<>();
            }

            // Step 5: translateAns - 出参转换
            List<Map<String, Object>> translatedAnsData = new ArrayList<>();
            ParameterTranslator.TranslateResult translateAnsResult =
                    parameterTranslator.translateAns(serviceId, translatedReq, ansCommData, translatedAnsData);

            if (translateAnsResult.getCode() != 0) {
                String errorMsg =
                        String.format("adapt功能%s出参转换失败:%s", serviceId, translateAnsResult.getMessage());
                logger.error(errorMsg);
                return new AdapterResult(ServiceConstants.ERROR_CODE_ADAPTER_FAIL, errorMsg, null);
            }

            // Step 6: 错误码转换 (对应Python中的g_TransErrCodeMsg.SelectErrorCodeAndMessage)
            String finalCode = translateErrorCode(serviceId, code, message);
            String finalMessage = translateErrorMessage(serviceId, code, message);

            logger.info("适配器调用完成, serviceId: {}, finalCode: {}, refRequest: {}", serviceId, finalCode,
                    refRequest);
            return new AdapterResult(finalCode, finalMessage, translatedAnsData);

        } catch (Exception e) {
            logger.error("调用适配器{}异常, refRequest: {}", serviceId, refRequest, e);
            return new AdapterResult(ServiceConstants.ERROR_CODE_ADAPTER_FAIL, "适配器调用异常: " + e.getMessage(),
                    null);
        }
    }

    /**
     * 发送请求到交易系统
     * 开发环境使用Mock数据，生产环境应该调用t2Client
     */
    private String sendRequestToTrading(String serviceId, String requestJson) {
        // 开发环境使用Mock数据
        if (isDevEnvironment()) {
            return generateMockResponse(serviceId, requestJson);
        }

        // 生产环境调用t2Client
        return sendHttpRequestToTrading(requestJson);
    }

    /**
     * 生产环境HTTP请求到交易网关 (实际应该调用t2Client)
     */
    private String sendHttpRequestToTrading(String requestJson) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(tradingGatewayUrl);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestJson, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            logger.error("调用交易网关异常", e);
            throw new RuntimeException("调用交易网关失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否为开发环境
     */
    private boolean isDevEnvironment() {
        // 优先使用配置文件中的mock-enabled设置
        if (mockEnabled) {
            return true;
        }

        // 其次检查环境变量
        String profile = System.getProperty("spring.profiles.active", "dev");
        return "dev".equals(profile) || "test".equals(profile);
    }

    /**
     * 检查是否需要进行op_station处理的功能号
     */
    private boolean isValidServiceForOpStationProcessing(String serviceId) {
        // 对应Python中需要处理TCC字段的功能号
        return Arrays.asList("410301", "410301A", "410323", "410324").contains(serviceId);
    }

    /**
     * 屏蔽敏感数据用于日志输出
     */
    private String maskSensitiveData(String requestJson) {
        if (requestJson == null) {
            return "";
        }

        // 屏蔽密码相关字段
        String masked = requestJson;
        masked = masked.replaceAll("(\"password\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
        masked = masked.replaceAll("(\"trdpwd\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
        masked = masked.replaceAll("(\"bankpwd\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");
        masked = masked.replaceAll("(\"fundpwd\"\\s*:\\s*\")([^\"]+)(\")", "$1***$3");

        return masked;
    }

    /**
     * 错误码转换
     * 对应Python中的g_TransErrCodeMsg.SelectErrorCodeAndMessage
     */
    private String translateErrorCode(String serviceId, String originalCode, String originalMessage) {
        // 成功码直接返回
        if (ServiceConstants.SUCCESS_CODE.equals(originalCode)) {
            return originalCode;
        }

        // 这里可以根据具体业务需求添加错误码转换逻辑
        // 对应Python中的错误码映射表
        switch (originalCode) {
            case "-1":
                return "-30011"; // 适配器通用错误
            case "-100":
                return "-30012"; // 系统错误
            default:
                return originalCode;
        }
    }

    /**
     * 错误消息转换
     */
    private String translateErrorMessage(String serviceId, String originalCode, String originalMessage) {
        // 成功消息直接返回
        if (ServiceConstants.SUCCESS_CODE.equals(originalCode)) {
            return originalMessage;
        }

        // 如果消息为空或者是默认消息，提供更友好的错误提示
        if (originalMessage == null || originalMessage.trim().isEmpty()) {
            return String.format("功能%s执行失败，错误码: %s", serviceId, originalCode);
        }

        return originalMessage;
    }

    /**
     * 生成Mock响应数据
     * 根据不同的功能号返回对应的模拟数据
     */
    private String generateMockResponse(String serviceId, String requestJson) {
        logger.info("使用Mock数据响应功能号: {}", serviceId);

        try {
            JSONObject request = JSON.parseObject(requestJson);
            Map<String, Object> requestData = (Map<String, Object>)request.get("data");

            switch (serviceId) {
                case "331100": // 410301签约对应的目标功能号
                    return generateMock331100Response(requestData);
                case "410301": // 410301A登录验证
                    return generateMock410301Response(requestData);
                case "410323": // 增加权限
                    return generateMock410323Response(requestData);
                case "410324": // 查询权限
                    return generateMock410324Response(requestData);
                default:
                    return generateDefaultMockResponse(serviceId);
            }
        } catch (Exception e) {
            logger.error("生成Mock数据异常, serviceId: {}", serviceId, e);
            return generateErrorMockResponse(serviceId, e.getMessage());
        }
    }

    /**
     * 生成331100(410301签约)的Mock响应
     */
    private String generateMock331100Response(Map<String, Object> requestData) {
        String accountContent = (String)requestData.get("account_content");

        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> answers = new ArrayList<>();
        Map<String, Object> answer = new HashMap<>();

        // 构建响应头
        Map<String, Object> ansMsgHdr = new HashMap<>();
        ansMsgHdr.put("MSG_CODE", "0");
        ansMsgHdr.put("MSG_TEXT", "签约成功");
        ansMsgHdr.put("MSG_LEVEL", "0");
        answer.put("ANS_MSG_HDR", ansMsgHdr);

        // 构建响应数据 - 深A股东账号
        List<Map<String, Object>> ansCommData = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("fundid", accountContent != null ? accountContent : "test_fund_001");
        dataRow.put("custid", "test_cust_001");
        dataRow.put("custname", "测试客户");
        dataRow.put("orgid", "5010");
        dataRow.put("asset_prop", "0"); // 普通证券账户
        dataRow.put("market", "0"); // 深A
        dataRow.put("secuid", "**********");
        ansCommData.add(dataRow);

        // 添加沪A股东账号
        Map<String, Object> dataRow2 = new HashMap<>();
        dataRow2.put("fundid", accountContent != null ? accountContent : "test_fund_001");
        dataRow2.put("custid", "test_cust_001");
        dataRow2.put("custname", "测试客户");
        dataRow2.put("orgid", "5010");
        dataRow2.put("asset_prop", "0");
        dataRow2.put("market", "1"); // 沪A
        dataRow2.put("secuid", "**********");
        ansCommData.add(dataRow2);

        answer.put("ANS_COMM_DATA", ansCommData);
        answers.add(answer);
        response.put("ANSWERS", answers);

        return JSON.toJSONString(response);
    }

    /**
     * 生成410301(登录验证)的Mock响应
     */
    private String generateMock410301Response(Map<String, Object> requestData) {
        String operway = (String)requestData.get("operway");

        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> answers = new ArrayList<>();
        Map<String, Object> answer = new HashMap<>();

        Map<String, Object> ansMsgHdr = new HashMap<>();
        ansMsgHdr.put("MSG_CODE", "0");
        ansMsgHdr.put("MSG_TEXT", "验证成功");
        ansMsgHdr.put("MSG_LEVEL", "0");
        answer.put("ANS_MSG_HDR", ansMsgHdr);

        List<Map<String, Object>> ansCommData = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("fundid", "test_fund_001");
        dataRow.put("custid", "test_cust_001");
        dataRow.put("custname", "测试客户");
        dataRow.put("orgid", "5010");
        dataRow.put("operway", operway);
        ansCommData.add(dataRow);

        answer.put("ANS_COMM_DATA", ansCommData);
        answers.add(answer);
        response.put("ANSWERS", answers);

        return JSON.toJSONString(response);
    }

    /**
     * 生成410323(增加权限)的Mock响应
     */
    private String generateMock410323Response(Map<String, Object> requestData) {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> answers = new ArrayList<>();
        Map<String, Object> answer = new HashMap<>();

        Map<String, Object> ansMsgHdr = new HashMap<>();
        ansMsgHdr.put("MSG_CODE", "0");
        ansMsgHdr.put("MSG_TEXT", "权限增加成功");
        ansMsgHdr.put("MSG_LEVEL", "0");
        answer.put("ANS_MSG_HDR", ansMsgHdr);

        List<Map<String, Object>> ansCommData = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("result", "success");
        ansCommData.add(dataRow);

        answer.put("ANS_COMM_DATA", ansCommData);
        answers.add(answer);
        response.put("ANSWERS", answers);

        return JSON.toJSONString(response);
    }

    /**
     * 生成410324(查询权限)的Mock响应
     */
    private String generateMock410324Response(Map<String, Object> requestData) {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> answers = new ArrayList<>();
        Map<String, Object> answer = new HashMap<>();

        Map<String, Object> ansMsgHdr = new HashMap<>();
        ansMsgHdr.put("MSG_CODE", "0");
        ansMsgHdr.put("MSG_TEXT", "查询成功");
        ansMsgHdr.put("MSG_LEVEL", "0");
        answer.put("ANS_MSG_HDR", ansMsgHdr);

        List<Map<String, Object>> ansCommData = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("name", "测试客户");
        dataRow.put("fundid", "test_fund_001");
        dataRow.put("operway", "5"); // 默认没有e权限，需要增加
        ansCommData.add(dataRow);

        answer.put("ANS_COMM_DATA", ansCommData);
        answers.add(answer);
        response.put("ANSWERS", answers);

        return JSON.toJSONString(response);
    }

    /**
     * 生成默认Mock响应
     */
    private String generateDefaultMockResponse(String serviceId) {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> answers = new ArrayList<>();
        Map<String, Object> answer = new HashMap<>();

        Map<String, Object> ansMsgHdr = new HashMap<>();
        ansMsgHdr.put("MSG_CODE", "0");
        ansMsgHdr.put("MSG_TEXT", "Mock响应成功");
        ansMsgHdr.put("MSG_LEVEL", "0");
        answer.put("ANS_MSG_HDR", ansMsgHdr);

        List<Map<String, Object>> ansCommData = new ArrayList<>();
        Map<String, Object> dataRow = new HashMap<>();
        dataRow.put("service_id", serviceId);
        dataRow.put("result", "success");
        dataRow.put("timestamp", System.currentTimeMillis());
        ansCommData.add(dataRow);

        answer.put("ANS_COMM_DATA", ansCommData);
        answers.add(answer);
        response.put("ANSWERS", answers);

        return JSON.toJSONString(response);
    }

    /**
     * 生成错误Mock响应
     */
    private String generateErrorMockResponse(String serviceId, String errorMessage) {
        Map<String, Object> response = new HashMap<>();
        List<Map<String, Object>> answers = new ArrayList<>();
        Map<String, Object> answer = new HashMap<>();

        Map<String, Object> ansMsgHdr = new HashMap<>();
        ansMsgHdr.put("MSG_CODE", "-1");
        ansMsgHdr.put("MSG_TEXT", "Mock数据生成异常: " + errorMessage);
        ansMsgHdr.put("MSG_LEVEL", "0");
        answer.put("ANS_MSG_HDR", ansMsgHdr);

        answer.put("ANS_COMM_DATA", new ArrayList<>());
        answers.add(answer);
        response.put("ANSWERS", answers);

        return JSON.toJSONString(response);
    }

    /**
     * 适配器结果封装类
     */
    @Data
    @AllArgsConstructor
    public static class AdapterResult {
        private String code;
        private String message;
        private List<Map<String, Object>> rows;

        public boolean isSuccess() {
            return ServiceConstants.SUCCESS_CODE.equals(code);
        }
    }
}
