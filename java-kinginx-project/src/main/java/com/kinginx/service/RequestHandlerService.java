package com.kinginx.service;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.dto.KinginxResponse;
import com.kinginx.common.exception.KinginxException;
import com.kinginx.service.SignContractService.SignResult;
import com.kinginx.service.TokenExchangeService.TokenExchangeResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求处理服务
 * 负责处理各种业务请求的具体逻辑
 */
@Service
public class RequestHandlerService {

    private static final Logger logger = LoggerFactory.getLogger(RequestHandlerService.class);

    @Autowired
    private SignContractService signContractService;

    @Autowired
    private TokenExchangeService tokenExchangeService;

    /**
     * 处理600001签约请求
     */
    public KinginxResponse handle600001Sign(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600001签约请求, refRequest: {}", refRequest);

            // 参数验证
            validateSignRequest(reqData);

            // 调用签约服务
            SignResult signResult = signContractService.signContract410301(refRequest, reqData);

            if (signResult.isSuccess()) {
                logger.info("600001签约成功, refRequest: {}, session: {}", refRequest, signResult.getSession());
                return new KinginxResponse(signResult.getCode(), signResult.getMessage(), "", signResult.getSession(),
                        signResult.getData());
            } else {
                logger.warn("600001签约失败, refRequest: {}, code: {}, message: {}", refRequest, signResult.getCode(),
                        signResult.getMessage());
                return new KinginxResponse(signResult.getCode(), signResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600001签约请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "签约处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理600002解约请求
     */
    public KinginxResponse handle600002Unsign(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600002解约请求, refRequest: {}", refRequest);

            // 参数验证
            validateUnsignRequest(reqData);

            // 调用解约服务
            SignResult unsignResult = signContractService.unsignContract600002(refRequest, reqData);

            if (unsignResult.isSuccess()) {
                logger.info("600002解约成功, refRequest: {}", refRequest);
                return new KinginxResponse(unsignResult.getCode(), unsignResult.getMessage(), "", "");
            } else {
                logger.warn("600002解约失败, refRequest: {}, code: {}, message: {}", refRequest, unsignResult.getCode(),
                        unsignResult.getMessage());
                return new KinginxResponse(unsignResult.getCode(), unsignResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600002解约请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "解约处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理600003账户留痕请求
     */
    public KinginxResponse handle600003AccountTrace(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600003账户留痕请求, refRequest: {}", refRequest);

            // 参数验证
            validateAccountTraceRequest(reqData);

            // 调用账户留痕服务
            SignResult traceResult = signContractService.accountTrace600003(refRequest, reqData);

            if (traceResult.isSuccess()) {
                logger.info("600003账户留痕成功, refRequest: {}", refRequest);
                return new KinginxResponse(traceResult.getCode(), traceResult.getMessage(), "", "");
            } else {
                logger.warn("600003账户留痕失败, refRequest: {}, code: {}, message: {}", refRequest,
                        traceResult.getCode(), traceResult.getMessage());
                return new KinginxResponse(traceResult.getCode(), traceResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600003账户留痕请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "账户留痕处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理600010登录请求
     */
    public KinginxResponse handle600010Login(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理600010登录请求, refRequest: {}", refRequest);

            // 参数验证
            validateLoginRequest(reqData);

            // 调用登录服务
            SignResult loginResult = signContractService.login(refRequest, reqData);

            if (loginResult.isSuccess()) {
                logger.info("600010登录成功, refRequest: {}, session: {}", refRequest, loginResult.getSession());
                return new KinginxResponse(loginResult.getCode(), loginResult.getMessage(), "",
                        loginResult.getSession());
            } else {
                logger.warn("600010登录失败, refRequest: {}, code: {}, message: {}", refRequest, loginResult.getCode(),
                        loginResult.getMessage());
                return new KinginxResponse(loginResult.getCode(), loginResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理600010登录请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "登录处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理700001 session换token请求
     */
    public KinginxResponse handle700001TokenExchange(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始处理700001 session换token请求, refRequest: {}", refRequest);

            // 参数验证
            validateTokenExchangeRequest(reqData);

            // 调用token交换服务
            TokenExchangeResult tokenResult = tokenExchangeService.exchangeToken(refRequest, reqData);

            if (tokenResult.isSuccess()) {
                logger.info("700001 session换token成功, refRequest: {}", refRequest);

                // 构建响应数据
                Map<String, Object> resultData = new HashMap<>();
                if (tokenResult.getData() != null) {
                    resultData.putAll((Map<String, Object>)tokenResult.getData());
                }

                return new KinginxResponse(tokenResult.getCode(), tokenResult.getMessage(), "", "",
                        Collections.singletonList(resultData));
            } else {
                logger.warn("700001 session换token失败, refRequest: {}, code: {}, message: {}", refRequest,
                        tokenResult.getCode(), tokenResult.getMessage());
                return new KinginxResponse(tokenResult.getCode(), tokenResult.getMessage(), "", "");
            }

        } catch (Exception e) {
            logger.error("处理700001 session换token请求异常, refRequest: {}", refRequest, e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_SYSTEM, "token交换处理异常: " + e.getMessage(), e);
        }
    }

    /**
     * 验证签约请求参数
     */
    private void validateSignRequest(Map<String, Object> reqData) {
        if (reqData.get("LOGIN_CODE") == null) {
            throw new KinginxException("-1", "登录代码不能为空");
        }
        if (reqData.get("TRD_PWD") == null) {
            throw new KinginxException("-1", "交易密码不能为空");
        }
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-1", "用户ID不能为空");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
    }

    /**
     * 验证登录请求参数
     */
    private void validateLoginRequest(Map<String, Object> reqData) {
        if (reqData.get("LOGIN_CODE") == null) {
            throw new KinginxException("-1", "登录代码不能为空");
        }
        if (reqData.get("USER_PWD") == null) {
            throw new KinginxException("-1", "用户密码不能为空");
        }
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-1", "用户ID不能为空");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
    }

    /**
     * 验证解约请求参数
     */
    private void validateUnsignRequest(Map<String, Object> reqData) {
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-100", "入参有误：必要的入参[USER_ID]不存在");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
    }

    /**
     * 验证账户留痕请求参数
     */
    private void validateAccountTraceRequest(Map<String, Object> reqData) {
        if (reqData.get("USER_ID") == null) {
            throw new KinginxException("-100", "入参有误：必要的入参[USER_ID]不存在");
        }
        if (reqData.get("USER_ID_CLS") == null) {
            throw new KinginxException("-1", "用户ID类型不能为空");
        }
        if (reqData.get("COMPANY_ID") == null) {
            throw new KinginxException("-1", "公司ID不能为空");
        }
        if (reqData.get("ACCOUNT") == null) {
            throw new KinginxException("-1", "资金账号不能为空");
        }
    }

    /**
     * 验证token交换请求参数
     */
    private void validateTokenExchangeRequest(Map<String, Object> reqData) {
        if (reqData.get("session_id") == null) {
            throw new KinginxException("-7001", "[session_id]必传");
        }
        String sessionId = (String)reqData.get("session_id");
        if (sessionId == null || sessionId.trim().isEmpty()) {
            throw new KinginxException("-7001", "[session_id]不能为空");
        }
    }

    // ==================== 新增的Handler方法 ====================

    /**
     * 处理900010重置/修改密码请求
     */
    public KinginxResponse handle900010ResetPassword(String refRequest, Map<String, Object> reqData) {
        logger.info("处理900010重置/修改密码请求, refRequest: {}", refRequest);
        // TODO: 实现重置/修改密码逻辑
        return new KinginxResponse("0", "重置/修改密码功能待实现", "", "");
    }

    /**
     * 处理600011资金密码登录请求
     */
    public KinginxResponse handle600011FundPasswordLogin(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600011资金密码登录请求, refRequest: {}", refRequest);
        // TODO: 实现资金密码登录逻辑
        return new KinginxResponse("0", "资金密码登录功能待实现", "", "");
    }

    /**
     * 通用查询处理方法
     * 对应Python中的pyreqbpagentCommquery
     */
    public KinginxResponse handleCommQuery(String refRequest, Map<String, Object> reqData) {
        logger.info("处理通用查询请求, refRequest: {}", refRequest);
        // TODO: 实现通用查询逻辑
        return new KinginxResponse("0", "通用查询功能待实现", "", "");
    }

    /**
     * 处理600040用户信息查询请求
     * 对应Python中的pyreqbpagent600040
     */
    public KinginxResponse handle600040UserInfo(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600040用户信息查询请求, refRequest: {}", refRequest);
        // TODO: 实现用户信息查询逻辑
        return new KinginxResponse("0", "用户信息查询功能待实现", "", "");
    }

    /**
     * 处理600401延长会话请求
     */
    public KinginxResponse handle600401ExtendSession(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600401延长会话请求, refRequest: {}", refRequest);
        // TODO: 实现延长会话逻辑
        return new KinginxResponse("0", "延长会话功能待实现", "", "");
    }

    /**
     * 处理600090插入券商公告请求
     */
    public KinginxResponse handle600090InsertNotice(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600090插入券商公告请求, refRequest: {}", refRequest);
        // TODO: 实现插入券商公告逻辑
        return new KinginxResponse("0", "插入券商公告功能待实现", "", "");
    }

    /**
     * 处理600091查询公告请求
     */
    public KinginxResponse handle600091SelectNotice(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600091查询公告请求, refRequest: {}", refRequest);
        // TODO: 实现查询公告逻辑
        return new KinginxResponse("0", "查询公告功能待实现", "", "");
    }

    /**
     * 处理600400腾讯查询公告请求
     */
    public KinginxResponse handle600400SelectNoticeTx(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600400腾讯查询公告请求, refRequest: {}", refRequest);
        // TODO: 实现腾讯查询公告逻辑
        return new KinginxResponse("0", "腾讯查询公告功能待实现", "", "");
    }

    /**
     * 处理600098删除券商公告请求
     */
    public KinginxResponse handle600098DeleteNotice(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600098删除券商公告请求, refRequest: {}", refRequest);
        // TODO: 实现删除券商公告逻辑
        return new KinginxResponse("0", "删除券商公告功能待实现", "", "");
    }

    /**
     * 处理600099修改券商公告请求
     */
    public KinginxResponse handle600099UpdateNotice(String refRequest, Map<String, Object> reqData) {
        logger.info("处理600099修改券商公告请求, refRequest: {}", refRequest);
        // TODO: 实现修改券商公告逻辑
        return new KinginxResponse("0", "修改券商公告功能待实现", "", "");
    }

    /**
     * 处理测试会话请求
     */
    public KinginxResponse handleTestForSession(String refRequest, Map<String, Object> reqData) {
        logger.info("处理测试会话请求, refRequest: {}", refRequest);
        // TODO: 实现测试会话逻辑
        return new KinginxResponse("0", "测试会话功能待实现", "", "");
    }

    /**
     * 处理获取客户IP信息请求
     */
    public KinginxResponse handleGetClientIp(String refRequest, Map<String, Object> reqData) {
        logger.info("处理获取客户IP信息请求, refRequest: {}", refRequest);
        // TODO: 实现获取客户IP信息逻辑
        return new KinginxResponse("0", "获取客户IP信息功能待实现", "", "");
    }

    /**
     * 处理获取应答字典残留应答请求
     */
    public KinginxResponse handleGetNginxInfo(String refRequest, Map<String, Object> reqData) {
        logger.info("处理获取应答字典残留应答请求, refRequest: {}", refRequest);
        // TODO: 实现获取应答字典残留应答逻辑
        return new KinginxResponse("0", "获取应答字典残留应答功能待实现", "", "");
    }

    /**
     * 处理清空残留应答请求
     */
    public KinginxResponse handleClearNginxXpAns(String refRequest, Map<String, Object> reqData) {
        logger.info("处理清空残留应答请求, refRequest: {}", refRequest);
        // TODO: 实现清空残留应答逻辑
        return new KinginxResponse("0", "清空残留应答功能待实现", "", "");
    }

    /**
     * 处理400001查询客户号请求（测试环境专用）
     */
    public KinginxResponse handle400001QueryAccount(String refRequest, Map<String, Object> reqData) {
        logger.info("处理400001查询客户号请求, refRequest: {}", refRequest);
        // TODO: 实现查询客户号逻辑
        return new KinginxResponse("0", "查询客户号功能待实现", "", "");
    }

    /**
     * 处理400002查询腾讯ID请求（测试环境专用）
     */
    public KinginxResponse handle400002QueryUserId(String refRequest, Map<String, Object> reqData) {
        logger.info("处理400002查询腾讯ID请求, refRequest: {}", refRequest);
        // TODO: 实现查询腾讯ID逻辑
        return new KinginxResponse("0", "查询腾讯ID功能待实现", "", "");
    }

    /**
     * 处理400020查询验证码请求（测试环境专用）
     */
    public KinginxResponse handle400020CheckSmsCode(String refRequest, Map<String, Object> reqData) {
        logger.info("处理400020查询验证码请求, refRequest: {}", refRequest);
        // TODO: 实现查询验证码逻辑
        return new KinginxResponse("0", "查询验证码功能待实现", "", "");
    }
}
