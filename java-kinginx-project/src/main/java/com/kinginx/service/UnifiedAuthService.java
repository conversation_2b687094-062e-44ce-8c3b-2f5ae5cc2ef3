package com.kinginx.service;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.common.exception.KinginxException;
import com.kinginx.entity.UserIdentity;
import com.kinginx.entity.UserSignAndUnsign;
import com.kinginx.entity.UserSignMark;
import com.kinginx.repository.UserIdentityRepository;
import com.kinginx.repository.UserSignAndUnsignRepository;
import com.kinginx.repository.UserSignMarkRepository;
import com.kinginx.service.util.CryptoUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 统一认证服务
 * 对应Python项目中的tyrzBiz.py模块
 */
@Service
public class UnifiedAuthService {

    private static final Logger logger = LoggerFactory.getLogger(UnifiedAuthService.class);

    @Autowired
    private UserIdentityRepository userIdentityRepository;

    @Autowired
    private UserSignMarkRepository userSignMarkRepository;

    @Autowired
    private UserSignAndUnsignRepository userSignAndUnsignRepository;

    @Autowired
    private CryptoUtil cryptoUtil;

    @Autowired
    private RedisService redisService;

    /**
     * T0005005签约
     * 对应Python中的T0005005_sign函数
     */
    @Transactional
    public AuthResult t0005005Sign(Map<String, Object> jsonReq) {
        try {
            logger.info("开始执行T0005005签约, userId: {}", jsonReq.get("USER_ID"));

            // 检查是否已经签约
            String userId = (String)jsonReq.get("USER_ID");
            String companyId = (String)jsonReq.get("COMPANY_ID");
            String userIdCls = (String)jsonReq.get("USER_ID_CLS");

            Optional<UserIdentity> existingUser =
                    userIdentityRepository.findByUserIdInfoAndUserIdClsAndCompanyId(userId, userIdCls, companyId);

            if (existingUser.isPresent()) {
                logger.warn("用户已经签约, userId: {}, companyId: {}", userId, companyId);
                return new AuthResult(ServiceConstants.ERROR_CODE_ALREADY_SIGNED, "该用户已经签约", null);
            }

            // 创建用户身份信息
            UserIdentity userIdentity = createUserIdentity(jsonReq);
            userIdentityRepository.save(userIdentity);

            // 生成会话信息
            Map<String, Object> sessionData = generateSessionData(jsonReq, userIdentity);
            String session = generateSessionToken(sessionData);

            // 记录签约操作
            recordSignOperation(jsonReq);

            logger.info("T0005005签约成功, userId: {}, session: {}", userId, session);

            List<Map<String, Object>> resultData = new ArrayList<>();
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("SESSION", session);
            sessionInfo.put("SESSION_JSON", sessionData);
            resultData.add(sessionInfo);

            return new AuthResult(ServiceConstants.SUCCESS_CODE, "签约成功", resultData);

        } catch (Exception e) {
            logger.error("T0005005签约异常", e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_TYRZ_FAIL, "统一认证签约失败: " + e.getMessage(), e);
        }
    }

    /**
     * T0005009解约
     * 对应Python中的T0005009_unsign函数
     */
    @Transactional
    public AuthResult t0005009Unsign(Map<String, Object> jsonReq) {
        try {
            logger.info("开始执行T0005009解约, userId: {}", jsonReq.get("USER_ID"));

            String userId = (String)jsonReq.get("USER_ID");
            String companyId = (String)jsonReq.get("COMPANY_ID");
            String userIdCls = (String)jsonReq.get("USER_ID_CLS");

            // 查询用户签约信息
            Optional<UserIdentity> userIdentity =
                    userIdentityRepository.findByUserIdInfoAndUserIdClsAndCompanyId(userId, userIdCls, companyId);

            if (!userIdentity.isPresent()) {
                String errorMsg = String.format("不存在该用户签约信息,第三方客户:%s,客户渠道:%s", userId, userIdCls);
                logger.warn(errorMsg);
                return new AuthResult("-200", errorMsg, null);
            }

            // 删除用户身份信息
            userIdentityRepository.deleteByUserIdInfoAndType(userId, companyId, userIdCls);

            // 记录解约操作
            recordUnsignOperation(jsonReq, userIdentity.get());

            logger.info("T0005009解约成功, userId: {}", userId);
            return new AuthResult(ServiceConstants.SUCCESS_CODE, "统一认证解约成功", null);

        } catch (Exception e) {
            logger.error("T0005009解约异常", e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_TYRZ_FAIL, "统一认证解约失败: " + e.getMessage(), e);
        }
    }

    /**
     * T0005001生成票据
     * 对应Python中的T0005001_makesession函数
     */
    public AuthResult t0005001MakeSession(Map<String, Object> jsonReq) {
        try {
            logger.info("开始执行T0005001生成票据, userId: {}", jsonReq.get("USER_ID"));

            String userId = (String)jsonReq.get("USER_ID");
            String companyId = (String)jsonReq.get("COMPANY_ID");
            String userIdCls = (String)jsonReq.get("USER_ID_CLS");

            // 查询用户签约信息
            Optional<UserIdentity> userIdentity =
                    userIdentityRepository.findByUserIdInfoAndUserIdClsAndCompanyId(userId, userIdCls, companyId);

            if (!userIdentity.isPresent()) {
                String errorMsg = String.format("不存在该用户签约信息,第三方客户:%s,客户渠道:%s", userId, userIdCls);
                logger.warn(errorMsg);
                return new AuthResult("-200", errorMsg, null);
            }

            // 生成会话信息
            Map<String, Object> sessionData = generateSessionData(jsonReq, userIdentity.get());
            String session = generateSessionToken(sessionData);

            logger.info("T0005001生成票据成功, userId: {}, session: {}", userId, session);

            List<Map<String, Object>> resultData = new ArrayList<>();
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("SESSION", session);
            sessionInfo.put("SESSION_JSON", sessionData);
            resultData.add(sessionInfo);

            return new AuthResult(ServiceConstants.SUCCESS_CODE, "生成票据成功", resultData);

        } catch (Exception e) {
            logger.error("T0005001生成票据异常", e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_TYRZ_FAIL, "生成票据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查用户是否已存在签约信息
     */
    public AuthResult isExistInTyrzDb(Map<String, Object> jsonReq) {
        try {
            String account = (String)jsonReq.get("ACCOUNT");
            String companyId = (String)jsonReq.get("COMPANY_ID");
            String userId = (String)jsonReq.get("USER_ID");

            // 检查资金账号是否已经绑定
            List<UserIdentity> accountBindings = userIdentityRepository.findByAccountBinding(account, companyId);
            if (!accountBindings.isEmpty()) {
                // 检查是否是同一个用户的重复绑定
                List<UserIdentity> sameUserBindings =
                        userIdentityRepository.findByAccountAndUserIdBinding(account, userId, companyId);

                if (!sameUserBindings.isEmpty()) {
                    return new AuthResult(ServiceConstants.SUCCESS_CODE, "该资金账号可以重复进行签约", null);
                } else {
                    return new AuthResult(ServiceConstants.ERROR_CODE_ALREADY_SIGNED,
                            "该资金账号已经绑定了另一个微信号，无法与当前微信号进行绑定", null);
                }
            }

            return new AuthResult(ServiceConstants.SUCCESS_CODE, "该资金账号可以进行签约", null);

        } catch (Exception e) {
            logger.error("检查签约信息异常", e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_TYRZ_FAIL, "检查签约信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 用户签约留痕
     */
    @Transactional
    public AuthResult userSignMark(Map<String, Object> jsonReq) {
        try {
            logger.info("开始执行用户签约留痕, userId: {}", jsonReq.get("USER_ID"));

            String userId = (String)jsonReq.get("USER_ID");
            String userIdCls = (String)jsonReq.get("USER_ID_CLS");
            String account = (String)jsonReq.get("ACCOUNT");
            String companyId = (String)jsonReq.get("COMPANY_ID");
            String tcc = (String)jsonReq.get("TCC");

            // 读取协议内容
            String disclaimer = readAgreementFile("disclaimer.txt");
            String noSecret = readAgreementFile("no_secret.txt");

            // 构建签约协议内容
            LocalDateTime now = LocalDateTime.now();
            String nowTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            String opStation = parseOpStation(tcc);
            String signAgreement =
                    String.format("%s|%s|%s|签署免责协议|%s|签署无密协议|%s|已签署", nowTime, account, opStation,
                            disclaimer, noSecret);

            // 保存签约留痕记录
            UserSignMark userSignMark = new UserSignMark();
            userSignMark.setUserId(userId);
            userSignMark.setUserIdCls(userIdCls);
            userSignMark.setAccount(account);
            userSignMark.setCompanyId(companyId);
            userSignMark.setTime(now);
            userSignMark.setSignAgreement(signAgreement);

            userSignMarkRepository.save(userSignMark);

            logger.info("用户签约留痕成功, userId: {}", userId);
            return new AuthResult(ServiceConstants.SUCCESS_CODE, "签约留痕成功", null);

        } catch (Exception e) {
            logger.error("用户签约留痕异常", e);
            throw new KinginxException(ServiceConstants.ERROR_CODE_TYRZ_FAIL, "签约留痕失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建用户身份信息
     */
    private UserIdentity createUserIdentity(Map<String, Object> jsonReq) {
        UserIdentity userIdentity = new UserIdentity();
        userIdentity.setUserIdInfo((String)jsonReq.get("USER_ID"));
        userIdentity.setUserIdType((String)jsonReq.get("USER_ID_CLS"));
        userIdentity.setUserIdCls((String)jsonReq.get("USER_ID_CLS"));
        userIdentity.setCompanyId((String)jsonReq.get("COMPANY_ID"));
        userIdentity.setAccount((String)jsonReq.get("ACCOUNT"));
        userIdentity.setCustId((String)jsonReq.get("CUSTID"));
        userIdentity.setUserName((String)jsonReq.get("USER_NAME"));
        userIdentity.setOrgCode((String)jsonReq.get("ORG_CODE"));
        userIdentity.setSecuSzA((String)jsonReq.get("SECUSZA"));
        userIdentity.setSecuSzB((String)jsonReq.get("SECUSZB"));
        userIdentity.setSecuShA((String)jsonReq.get("SECUSHA"));
        userIdentity.setSecuShB((String)jsonReq.get("SECUSHB"));
        userIdentity.setBankInfo((String)jsonReq.get("BANKINFO"));
        return userIdentity;
    }

    /**
     * 生成会话数据
     */
    private Map<String, Object> generateSessionData(Map<String, Object> jsonReq, UserIdentity userIdentity) {
        Map<String, Object> sessionData = new HashMap<>();
        sessionData.put("0", userIdentity.getCustId());
        sessionData.put("1", userIdentity.getUserName());
        sessionData.put("2", userIdentity.getOrgCode());
        sessionData.put("3", userIdentity.getAccount());
        sessionData.put("4", Arrays.asList(Arrays.asList("4", userIdentity.getSecuSzA(), "0", "0"),
                Arrays.asList("4", userIdentity.getSecuSzB(), "2", "0"),
                Arrays.asList("4", userIdentity.getSecuShA(), "1", "0"),
                Arrays.asList("4", userIdentity.getSecuShB(), "3", "0")));
        sessionData.put("5", userIdentity.getBankInfo());

        // 如果有交易密码，进行加密
        if (jsonReq.containsKey("TRD_PWD")) {
            String encryptedPwd = cryptoUtil.encryptPassword((String)jsonReq.get("TRD_PWD"));
            sessionData.put("6", encryptedPwd);
        }

        sessionData.put("7", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        sessionData.put("8", new ArrayList<>());

        return sessionData;
    }

    /**
     * 生成会话令牌
     */
    private String generateSessionToken(Map<String, Object> sessionData) {
        // 这里应该实现具体的会话令牌生成逻辑
        // 可以使用JWT或其他加密方式
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 记录签约操作
     */
    private void recordSignOperation(Map<String, Object> jsonReq) {
        String userIdCls = (String)jsonReq.get("USER_ID_CLS");

        // 只有特定渠道才记录签约操作
        if (Collections.singletonList("15900").contains(userIdCls)) {
            UserSignAndUnsign record = new UserSignAndUnsign();
            record.setUserCode((String)jsonReq.get("CUSTID"));
            record.setUserId((String)jsonReq.get("USER_ID"));
            record.setUserIdCls(userIdCls);
            record.setAccount((String)jsonReq.get("ACCOUNT"));
            record.setCompanyId((String)jsonReq.get("COMPANY_ID"));
            record.setTime(LocalDateTime.now());
            record.setType(0); // 0-签约

            userSignAndUnsignRepository.save(record);
        }
    }

    /**
     * 记录解约操作
     */
    private void recordUnsignOperation(Map<String, Object> jsonReq, UserIdentity userIdentity) {
        String userIdCls = (String)jsonReq.get("USER_ID_CLS");

        // 只有特定渠道才记录解约操作
        if (Collections.singletonList("15900").contains(userIdCls)) {
            UserSignAndUnsign record = new UserSignAndUnsign();
            record.setUserCode(userIdentity.getCustId());
            record.setUserId((String)jsonReq.get("USER_ID"));
            record.setUserIdCls(userIdCls);
            record.setAccount(userIdentity.getAccount());
            record.setCompanyId((String)jsonReq.get("COMPANY_ID"));
            record.setTime(LocalDateTime.now());
            record.setType(1); // 1-解约

            userSignAndUnsignRepository.save(record);
        }
    }

    /**
     * 读取协议文件内容
     */
    private String readAgreementFile(String filename) {
        try {
            ClassPathResource resource = new ClassPathResource("agreements/" + filename);
            byte[] bytes = readAllBytesFromInputStream(resource.getInputStream());
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            logger.warn("读取协议文件失败: {}", filename, e);
            return "协议内容";
        }
    }

    /**
     * 从InputStream读取所有字节 (Java 1.8兼容版本)
     */
    private byte[] readAllBytesFromInputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];

        try {
            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            buffer.flush();
            return buffer.toByteArray();
        } finally {
            inputStream.close();
        }
    }

    /**
     * 解析操作站点信息
     */
    private String parseOpStation(String tcc) {
        if (!StringUtils.hasText(tcc)) {
            return "";
        }

        if (tcc.startsWith("MA") || tcc.startsWith("MI")) {
            return tcc;
        }

        String[] parts = tcc.split("\\|");
        if (parts.length >= 3) {
            return parts[2] + ";" + parts[1] + ";" + parts[0];
        }

        return tcc;
    }

    /**
     * 认证结果封装类
     */
    @Data
    @AllArgsConstructor
    public static class AuthResult {
        private String code;
        private String message;
        private List<Map<String, Object>> data;

        public boolean isSuccess() {
            return ServiceConstants.SUCCESS_CODE.equals(code);
        }
    }
}
