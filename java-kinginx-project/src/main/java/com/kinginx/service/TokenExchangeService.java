package com.kinginx.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kinginx.service.util.CryptoUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Token交换服务
 * 对应Python项目中的sessionExchangeToken函数
 * 负责将session转换为国金网厅token
 */
@Service
public class TokenExchangeService {

    private static final Logger logger = LoggerFactory.getLogger(TokenExchangeService.class);

    @Value("${kinginx.external.gj-token.global-url:http://172.16.25.30:30000/yjbapi/sas/session/register}")
    private String globalUrl;

    @Value("${kinginx.external.gj-token.simple-url:http://172.16.25.30:30000/yjbapi/sas/instant_token/apply}")
    private String simpleUrl;

    @Value("${kinginx.external.gj-token.app-id:qqstack}")
    private String appId;

    @Value("${kinginx.external.gj-token.account-type:1}")
    private String accountType;

    @Value("${kinginx.external.gj-token.mock-enabled:true}")
    private boolean mockEnabled;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CryptoUtil cryptoUtil;

    /**
     * 700001 session换国金网厅token
     * 对应Python中的sessionExchangeToken函数
     */
    public TokenExchangeResult exchangeToken(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始执行700001 session换token, refRequest: {}", refRequest);

            // 1. 参数验证
            if (!reqData.containsKey("session_id")) {
                return new TokenExchangeResult("-7001", "[session_id]必传", null);
            }

            String sessionId = (String)reqData.get("session_id");
            if (!StringUtils.hasText(sessionId)) {
                return new TokenExchangeResult("-7001", "[session_id]不能为空", null);
            }

            // 2. 解密session_id
            String sessionJson = decryptSession(sessionId);
            if (!StringUtils.hasText(sessionJson)) {
                return new TokenExchangeResult("-7002", "[session_id]解析失败", null);
            }

            // 3. 解析session数据
            JSONObject sessionData;
            try {
                sessionData = JSON.parseObject(sessionJson);
            } catch (Exception e) {
                logger.error("解析session JSON失败: {}", sessionJson, e);
                return new TokenExchangeResult("-7003", "session数据格式错误", null);
            }

            String userId = sessionData.getString("user_id");
            if (!StringUtils.hasText(userId)) {
                return new TokenExchangeResult("-7004", "session中缺少user_id", null);
            }

            // 4. 从Redis验证票据
            String redisKey = "1" + "15900" + userId; // USER_ID_CLS + COMPANY_ID + USER_ID
            String redisValue = redisService.getStringKey(redisKey);

            if (!StringUtils.hasText(redisValue)) {
                return new TokenExchangeResult("-177", "票据已过期", null);
            }

            // 解析Redis中的票据信息
            JSONObject redisData = JSON.parseObject(redisValue);
            String storedSession = redisData.getString("SESSION");

            if (!sessionId.equals(storedSession)) {
                logger.warn("票据不匹配, 存储的session: {}", storedSession);
                return new TokenExchangeResult("-178", "票据已过期", null);
            }

            // 5. 获取交易密码
            JSONObject sessionInfo = redisData.getJSONObject("INFO");
            String password = extractPassword(sessionInfo);

            // 6. 调用国金网厅接口获取token
            if (mockEnabled) {
                return generateMockTokenResponse(sessionId, userId);
            } else {
                return callGJTokenService(sessionId, userId, password);
            }

        } catch (Exception e) {
            logger.error("700001 session换token异常, refRequest: {}", refRequest, e);
            return new TokenExchangeResult("-7100", "系统异常: " + e.getMessage(), null);
        }
    }

    /**
     * 解密session
     */
    private String decryptSession(String sessionId) {
        try {
            // 对应Python中的session_decrypt(session_id, g_session_des_key)
            return cryptoUtil.sessionDecrypt(sessionId, "se410301");
        } catch (Exception e) {
            logger.error("解密session失败: {}", sessionId, e);
            return null;
        }
    }

    /**
     * 从session信息中提取密码
     */
    private String extractPassword(JSONObject sessionInfo) {
        try {
            // 对应Python中从session_dict中提取密码的逻辑
            if (sessionInfo.containsKey("6")) {
                return sessionInfo.getString("6");
            }
            return ""; // 默认密码
        } catch (Exception e) {
            logger.error("提取密码失败", e);
            return "";
        }
    }

    /**
     * 调用国金网厅token服务
     */
    private TokenExchangeResult callGJTokenService(String sessionId, String userId, String password) {
        try {
            // Step 1: 调用global_url获取durableToken
            Map<String, Object> globalRequest = new HashMap<>();
            globalRequest.put("app_id", appId);
            globalRequest.put("account_type", accountType);
            globalRequest.put("expired_time", 28800); // 8小时
            globalRequest.put("password", password);

            String globalResponse = sendHttpRequest(globalUrl, globalRequest);
            JSONObject globalResult = JSON.parseObject(globalResponse);

            if (globalResult.getInteger("code") != 0) {
                return new TokenExchangeResult(globalResult.getString("code"), globalResult.getString("message"), null);
            }

            String durableToken = globalResult.getJSONObject("result").getString("durableToken");

            // Step 2: 调用simple_url获取最终token
            Map<String, Object> simpleRequest = new HashMap<>();
            simpleRequest.put("durable_token", durableToken);
            simpleRequest.put("expired_time", 28800);

            String simpleResponse = sendHttpRequest(simpleUrl, simpleRequest);
            JSONObject simpleResult = JSON.parseObject(simpleResponse);

            if (simpleResult.getInteger("code") != 0) {
                return new TokenExchangeResult(simpleResult.getString("code"), simpleResult.getString("message"), null);
            }

            // Step 3: 处理op_station信息
            JSONObject result = simpleResult.getJSONObject("result");
            String opStationKey = cryptoUtil.md5Encrypt(sessionId);
            String opStation = redisService.getStringKey(opStationKey);

            if (StringUtils.hasText(opStation)) {
                String encryptedOpStation = cryptoUtil.aesEncrypt(opStation, "op_aes_key");
                result.put("op_station", encryptedOpStation);

                // 删除临时存储的op_station
                redisService.deleteKey(opStationKey);
            } else {
                logger.warn("从redis取的op_station为空");
                result.put("op_station", "");
            }

            return new TokenExchangeResult("0", "success", result);

        } catch (Exception e) {
            logger.error("调用国金网厅token服务异常", e);
            return new TokenExchangeResult("-7200", "调用token服务失败: " + e.getMessage(), null);
        }
    }

    /**
     * 生成Mock token响应
     */
    private TokenExchangeResult generateMockTokenResponse(String sessionId, String userId) {
        logger.info("使用Mock数据生成token响应, userId: {}", userId);

        Map<String, Object> result = new HashMap<>();
        result.put("token", "mock_token_" + System.currentTimeMillis());
        result.put("expired_time", 28800);
        result.put("user_id", userId);
        result.put("app_id", appId);
        result.put("account_type", accountType);
        result.put("op_station", "mock_op_station_encrypted");

        return new TokenExchangeResult("0", "success", result);
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String url, Map<String, Object> requestData) throws IOException {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");

            String requestJson = JSON.toJSONString(requestData);
            httpPost.setEntity(new StringEntity(requestJson, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            }
        }
    }

    /**
     * Token交换结果封装类
     */
    @Data
    @AllArgsConstructor
    public static class TokenExchangeResult {
        private String code;
        private String message;
        private Object data;

        public boolean isSuccess() {
            return "0".equals(code);
        }
    }
}
