package com.kinginx.service;

import com.kinginx.common.constants.ServiceConstants;
import com.kinginx.entity.UserIdentity;
import com.kinginx.repository.UserIdentityRepository;
import com.kinginx.service.TradingAdapterService.AdapterResult;
import com.kinginx.service.UnifiedAuthService.AuthResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 签约服务
 * 对应Python项目中的bpagentBiz.py中的签约相关功能
 */
@Service
public class SignContractService {

    private static final Logger logger = LoggerFactory.getLogger(SignContractService.class);

    @Autowired
    private TradingAdapterService tradingAdapterService;

    @Autowired
    private UnifiedAuthService unifiedAuthService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SmsVerificationService smsVerificationService;

    @Autowired
    private UserIdentityRepository userIdentityRepository;

    /**
     * 410301签约功能
     * 对应Python中的newpyreqbpagent600001_hl函数
     */
    public SignResult signContract410301(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始执行410301签约, refRequest: {}, userId: {}", refRequest, reqData.get("USER_ID"));

            // Step 1: 检查客户是否已经签约
            AuthResult existCheck = unifiedAuthService.isExistInTyrzDb(reqData);
            if (!existCheck.isSuccess()) {
                logger.warn("签约检查失败: {}", existCheck.getMessage());
                return new SignResult(existCheck.getCode(), existCheck.getMessage(), "", null);
            }

            // Step 2: 验证短信验证码（如果提供了XID_SESSION）
            String xidSession = (String)reqData.get("XID_SESSION");
            if (StringUtils.hasText(xidSession)) {
                String userId = (String)reqData.get("USER_ID");
                boolean smsValid = smsVerificationService.checkXIDSession(userId, xidSession);
                if (!smsValid) {
                    logger.warn("短信验证码验证失败, userId: {}", userId);
                    return new SignResult("-1", "签约失败：短信验证码验证失败", "", null);
                }
            }

            // Step 3: 调用410301适配器进行签约
            CompletableFuture<AdapterResult> adapterFuture = tradingAdapterService.adapt410301(refRequest, reqData);
            AdapterResult adapterResult = adapterFuture.get();

            if (!adapterResult.isSuccess()) {
                logger.error("410301适配器调用失败: {}", adapterResult.getMessage());
                return new SignResult(adapterResult.getCode(), adapterResult.getMessage(), "", null);
            }

            // Step 4: 解析适配器返回的数据
            List<Map<String, Object>> adapterRows = adapterResult.getRows();
            if (adapterRows == null || adapterRows.isEmpty()) {
                logger.error("410301接口未返回结果集");
                return new SignResult("-100", "410301接口未返回结果集", "", null);
            }

            // 检查资产属性
            for (Map<String, Object> row : adapterRows) {
                String assetProp = (String)row.get("asset_prop");
                if (!ServiceConstants.ASSET_PROP_NORMAL.equals(assetProp)) {
                    String errorMsg = "当前为普通证券账户交易系统，请使用普通证券交易账户登录。";
                    logger.warn("资产属性检查失败: assetProp={}", assetProp);
                    return new SignResult("-102", errorMsg, "", null);
                }
            }

            // Step 5: 提取股东账户信息
            String secuSzA = "", secuShA = "", secuSzB = "", secuShB = "";
            String fundId = "", custId = "", orgId = "", custName = "";

            for (Map<String, Object> row : adapterRows) {
                fundId = (String)row.get("fundid");
                custId = (String)row.get("custid");
                orgId = (String)row.get("orgid");
                custName = (String)row.get("custname");
                String market = (String)row.get("market");

                switch (market) {
                    case ServiceConstants.MARKET_SZ_A:
                        secuSzA = (String)row.get("secuid");
                        break;
                    case ServiceConstants.MARKET_SH_A:
                        secuShA = (String)row.get("secuid");
                        break;
                    case ServiceConstants.MARKET_SZ_B:
                        secuSzB = (String)row.get("secuid");
                        break;
                    case ServiceConstants.MARKET_SH_B:
                        secuShB = (String)row.get("secuid");
                        break;
                }
            }

            // Step 6: 准备统一认证数据
            Map<String, Object> authData = new HashMap<>(reqData);
            authData.put("ID", "123");
            authData.put("USER_NAME", custName);
            authData.put("ORG_CODE", orgId);
            authData.put("CUSTID", custId);
            authData.put("ACCOUNT", fundId);
            authData.put("SECUSZA", secuSzA);
            authData.put("SECUSZB", secuSzB);
            authData.put("SECUSHA", secuShA);
            authData.put("SECUSHB", secuShB);
            authData.put("BANKINFO", "");

            // Step 7: 调用统一认证签约
            AuthResult signResult = unifiedAuthService.t0005005Sign(authData);
            if (!signResult.isSuccess()) {
                logger.error("统一认证签约失败: {}", signResult.getMessage());
                return new SignResult(signResult.getCode(), signResult.getMessage(), "", null);
            }

            // Step 8: 签约留痕
            AuthResult markResult = unifiedAuthService.userSignMark(authData);
            if (!markResult.isSuccess()) {
                logger.error("签约留痕失败: {}", markResult.getMessage());
                return new SignResult(markResult.getCode(), markResult.getMessage(), "", null);
            }

            // Step 9: 存储会话信息到Redis
            List<Map<String, Object>> signData = signResult.getData();
            if (signData != null && !signData.isEmpty()) {
                Map<String, Object> sessionInfo = signData.get(0);
                String session = (String)sessionInfo.get("SESSION");
                Map<String, Object> sessionJson = (Map<String, Object>)sessionInfo.get("SESSION_JSON");

                // 存储到Redis
                Map<String, Object> redisValue = new HashMap<>();
                redisValue.put("SESSION", session);
                redisValue.put("INFO", sessionJson);

                String userIdCls = (String)reqData.get("USER_ID_CLS");
                String companyId = (String)reqData.get("COMPANY_ID");
                String userId = (String)reqData.get("USER_ID");

                redisService.setSessionInfo(userIdCls, companyId, userId, redisValue);

                // 返回成功结果
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("ONLINE_FLAG", "0");

                logger.info("410301签约成功, refRequest: {}, userId: {}, session: {}", refRequest, userId, session);
                return new SignResult(ServiceConstants.SUCCESS_CODE, "签约成功", session,
                        Collections.singletonList(resultData));
            }

            logger.error("统一认证签约返回数据为空");
            return new SignResult("-100", "统一认证签约返回数据为空", "", null);

        } catch (Exception e) {
            logger.error("410301签约异常, refRequest: {}", refRequest, e);
            return new SignResult(ServiceConstants.ERROR_CODE_SYSTEM, "签约异常: " + e.getMessage(), "", null);
        }
    }

    /**
     * 600002解约功能
     * 对应Python中的pyreqbpagent600002和newpyreqbpagent600002_hl函数
     */
    public SignResult unsignContract600002(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始执行600002解约, refRequest: {}, userId: {}", refRequest, reqData.get("USER_ID"));

            String userId = (String)reqData.get("USER_ID");
            String userIdCls = (String)reqData.get("USER_ID_CLS");
            String companyId = (String)reqData.get("COMPANY_ID");

            // 参数验证
            if (!StringUtils.hasText(userId)) {
                return new SignResult("-200", "入参有误：USER_ID不能为空值", "", null);
            }

            // 删除Redis中的会话信息
            redisService.deleteSessionInfo(userIdCls, companyId, userId);

            // 检查绑定数量
            int bindCount = checkBindingCount(userId, companyId);

            if (bindCount > 1) {
                // 如果绑定多个账号，只删除当前用户的绑定记录
                AuthResult deleteResult = deleteUserBinding(userId, companyId, userIdCls);
                if (!deleteResult.isSuccess()) {
                    logger.error("删除用户绑定失败: {}", deleteResult.getMessage());
                    return new SignResult(deleteResult.getCode(), deleteResult.getMessage(), "", null);
                }
            } else {
                // 如果只有一个绑定，执行完整解约流程
                AuthResult unsignResult = unifiedAuthService.t0005009Unsign(reqData);
                if (!unsignResult.isSuccess()) {
                    logger.error("统一认证解约失败: {}", unsignResult.getMessage());
                    return new SignResult(unsignResult.getCode(), unsignResult.getMessage(), "", null);
                }
            }

            logger.info("600002解约成功, refRequest: {}, userId: {}", refRequest, userId);
            return new SignResult(ServiceConstants.SUCCESS_CODE, "解约成功", "", null);

        } catch (Exception e) {
            logger.error("600002解约异常, refRequest: {}", refRequest, e);
            return new SignResult(ServiceConstants.ERROR_CODE_SYSTEM, "解约异常: " + e.getMessage(), "", null);
        }
    }

    /**
     * 600003账户留痕功能
     * 对应Python中的newpyreqbpagent600003_hl函数
     */
    public SignResult accountTrace600003(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始执行600003账户留痕, refRequest: {}, userId: {}", refRequest, reqData.get("USER_ID"));

            String userId = (String)reqData.get("USER_ID");

            // 参数验证
            if (!StringUtils.hasText(userId)) {
                return new SignResult("-200", "入参有误：USER_ID不能为空值", "", null);
            }

            // 执行签约留痕
            AuthResult markResult = unifiedAuthService.userSignMark(reqData);
            if (!markResult.isSuccess()) {
                logger.error("账户留痕失败: {}", markResult.getMessage());
                return new SignResult(markResult.getCode(), markResult.getMessage(), "", null);
            }

            logger.info("600003账户留痕成功, refRequest: {}, userId: {}", refRequest, userId);
            return new SignResult(ServiceConstants.SUCCESS_CODE, "success", "", null);

        } catch (Exception e) {
            logger.error("600003账户留痕异常, refRequest: {}", refRequest, e);
            return new SignResult(ServiceConstants.ERROR_CODE_SYSTEM, "账户留痕异常: " + e.getMessage(), "", null);
        }
    }

    /**
     * 登录功能
     * 对应Python中的pyreqbpagent600010函数
     */
    public SignResult login(String refRequest, Map<String, Object> reqData) {
        try {
            logger.info("开始执行登录, refRequest: {}, userId: {}", refRequest, reqData.get("USER_ID"));

            // 设置会话类型为登录
            reqData.put("SESSION_TYPE", ServiceConstants.SESSION_TYPE_LOGIN);
            reqData.put("TRD_PWD", reqData.get("USER_PWD"));

            // 调用统一认证生成票据
            AuthResult authResult = unifiedAuthService.t0005001MakeSession(reqData);
            if (!authResult.isSuccess()) {
                logger.error("生成登录票据失败: {}", authResult.getMessage());
                return new SignResult(authResult.getCode(), authResult.getMessage(), "", null);
            }

            // 获取会话信息
            List<Map<String, Object>> authData = authResult.getData();
            if (authData != null && !authData.isEmpty()) {
                Map<String, Object> sessionInfo = authData.get(0);
                String session = (String)sessionInfo.get("SESSION");
                Map<String, Object> sessionJson = (Map<String, Object>)sessionInfo.get("SESSION_JSON");

                // 存储到Redis
                Map<String, Object> redisValue = new HashMap<>();
                redisValue.put("SESSION", session);
                redisValue.put("INFO", sessionJson);

                String userIdCls = (String)reqData.get("USER_ID_CLS");
                String companyId = (String)reqData.get("COMPANY_ID");
                String userId = (String)reqData.get("USER_ID");

                redisService.setSessionInfo(userIdCls, companyId, userId, redisValue);

                logger.info("登录成功, refRequest: {}, userId: {}, session: {}", refRequest, userId, session);
                return new SignResult(ServiceConstants.SUCCESS_CODE, "登录成功", session, null);
            }

            logger.error("生成登录票据返回数据为空");
            return new SignResult("-100", "生成登录票据返回数据为空", "", null);

        } catch (Exception e) {
            logger.error("登录异常, refRequest: {}", refRequest, e);
            return new SignResult(ServiceConstants.ERROR_CODE_SYSTEM, "登录异常: " + e.getMessage(), "", null);
        }
    }

    /**
     * 检查绑定数量
     */
    private int checkBindingCount(String userId, String companyId) {
        try {
            // 这里简化处理，实际应该查询数据库统计绑定数量
            List<UserIdentity> bindings = userIdentityRepository.findByAccountBinding(userId, companyId);
            return bindings.size();
        } catch (Exception e) {
            logger.error("检查绑定数量异常", e);
            return 1; // 异常情况下默认返回1
        }
    }

    /**
     * 删除用户绑定
     */
    private AuthResult deleteUserBinding(String userId, String companyId, String userIdCls) {
        try {
            userIdentityRepository.deleteByUserIdInfoAndType(userId, companyId, userIdCls);
            return new AuthResult(ServiceConstants.SUCCESS_CODE, "删除用户绑定成功", null);
        } catch (Exception e) {
            logger.error("删除用户绑定异常", e);
            return new AuthResult(ServiceConstants.ERROR_CODE_SYSTEM, "删除用户绑定失败: " + e.getMessage(), null);
        }
    }

    /**
     * 签约结果封装类
     */
    @Data
    @AllArgsConstructor
    public static class SignResult {
        private String code;
        private String message;
        private String session;
        private List<Map<String, Object>> data;

        public boolean isSuccess() {
            return ServiceConstants.SUCCESS_CODE.equals(code);
        }
    }
}
