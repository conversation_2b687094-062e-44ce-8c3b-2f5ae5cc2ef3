package com.kinginx.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 短信验证服务
 * 对应Python项目中的短信验证功能
 */
@Service
public class SmsVerificationService {

    private static final Logger logger = LoggerFactory.getLogger(SmsVerificationService.class);

    @Autowired
    private RedisService redisService;

    /**
     * 检查XID会话字符串
     * 对应Python中的CheckXIDSessionString函数
     */
    public boolean checkXIDSession(String userId, String xidSession) {
        try {
            logger.info("开始验证XID会话, userId: {}", userId);

            if (!StringUtils.hasText(xidSession)) {
                logger.warn("XID会话字符串为空, userId: {}", userId);
                return false;
            }

            // 从Redis中获取存储的验证码信息
            String redisKey = "xid_session_" + userId;
            String storedSession = redisService.getStringKey(redisKey);

            if (!StringUtils.hasText(storedSession)) {
                logger.warn("Redis中未找到XID会话信息, userId: {}", userId);
                return false;
            }

            // 验证会话字符串是否匹配
            boolean isValid = xidSession.equals(storedSession);

            if (isValid) {
                // 验证成功后删除Redis中的验证码信息（一次性使用）
                redisService.deleteKey(redisKey);
                logger.info("XID会话验证成功, userId: {}", userId);
            } else {
                logger.warn("XID会话验证失败, userId: {}", userId);
            }

            return isValid;

        } catch (Exception e) {
            logger.error("XID会话验证异常, userId: {}", userId, e);
            return false;
        }
    }

    /**
     * 生成并存储XID会话
     */
    public String generateXIDSession(String userId, String phoneNumber) {
        try {
            logger.info("生成XID会话, userId: {}, phone: {}", userId, phoneNumber);

            // 生成验证码（这里简化处理，实际应该调用短信服务）
            String verificationCode = generateVerificationCode();

            // 构建XID会话字符串
            String xidSession = userId + "_" + System.currentTimeMillis() + "_" + verificationCode;

            // 存储到Redis，设置5分钟过期
            String redisKey = "xid_session_" + userId;
            redisService.setKeyEx(redisKey, xidSession, 300);

            logger.info("XID会话生成成功, userId: {}", userId);
            return xidSession;

        } catch (Exception e) {
            logger.error("生成XID会话异常, userId: {}", userId, e);
            throw new RuntimeException("生成XID会话失败: " + e.getMessage());
        }
    }

    /**
     * 发送短信验证码
     */
    public boolean sendSmsVerificationCode(String phoneNumber, String userId) {
        try {
            logger.info("发送短信验证码, phone: {}, userId: {}", phoneNumber, userId);

            // 生成验证码
            String verificationCode = generateVerificationCode();

            // 存储验证码到Redis，设置5分钟过期
            String redisKey = "sms_code_" + phoneNumber;
            redisService.setKeyEx(redisKey, verificationCode, 300);

            // 这里应该调用实际的短信服务发送验证码
            // 为了演示，我们只是记录日志
            logger.info("短信验证码已发送, phone: {}, code: {}", phoneNumber, verificationCode);

            return true;

        } catch (Exception e) {
            logger.error("发送短信验证码异常, phone: {}", phoneNumber, e);
            return false;
        }
    }

    /**
     * 验证短信验证码
     */
    public boolean verifySmsCode(String phoneNumber, String inputCode) {
        try {
            logger.info("验证短信验证码, phone: {}", phoneNumber);

            if (!StringUtils.hasText(inputCode)) {
                logger.warn("验证码为空, phone: {}", phoneNumber);
                return false;
            }

            // 从Redis中获取存储的验证码
            String redisKey = "sms_code_" + phoneNumber;
            String storedCode = redisService.getStringKey(redisKey);

            if (!StringUtils.hasText(storedCode)) {
                logger.warn("验证码已过期或不存在, phone: {}", phoneNumber);
                return false;
            }

            // 验证码比较
            boolean isValid = inputCode.equals(storedCode);

            if (isValid) {
                // 验证成功后删除验证码（一次性使用）
                redisService.deleteKey(redisKey);
                logger.info("短信验证码验证成功, phone: {}", phoneNumber);
            } else {
                logger.warn("短信验证码验证失败, phone: {}", phoneNumber);
            }

            return isValid;

        } catch (Exception e) {
            logger.error("验证短信验证码异常, phone: {}", phoneNumber, e);
            return false;
        }
    }

    /**
     * 生成6位数字验证码
     */
    private String generateVerificationCode() {
        return String.format("%06d", (int)(Math.random() * 1000000));
    }

    /**
     * 检查验证码发送频率限制
     */
    public boolean checkSendFrequency(String phoneNumber) {
        try {
            String redisKey = "sms_frequency_" + phoneNumber;
            String lastSendTime = redisService.getStringKey(redisKey);

            if (StringUtils.hasText(lastSendTime)) {
                long lastTime = Long.parseLong(lastSendTime);
                long currentTime = System.currentTimeMillis();

                // 60秒内不能重复发送
                if (currentTime - lastTime < 60000) {
                    logger.warn("短信发送过于频繁, phone: {}", phoneNumber);
                    return false;
                }
            }

            // 记录本次发送时间
            redisService.setKeyEx(redisKey, String.valueOf(System.currentTimeMillis()), 60);
            return true;

        } catch (Exception e) {
            logger.error("检查短信发送频率异常, phone: {}", phoneNumber, e);
            return true; // 异常情况下允许发送
        }
    }
}
