package com.kinginx;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Kinginx Trading Service Application
 * Java SpringBoot implementation of the original Python nginx+python service
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableTransactionManagement
public class KinginxTradingServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(KinginxTradingServiceApplication.class, args);
    }
}
