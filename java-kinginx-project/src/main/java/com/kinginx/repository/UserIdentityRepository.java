package com.kinginx.repository;

import com.kinginx.entity.UserIdentity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户身份信息Repository
 */
@Repository
public interface UserIdentityRepository extends JpaRepository<UserIdentity, Long> {

    /**
     * 根据用户ID信息和公司ID查询用户身份
     */
    Optional<UserIdentity> findByUserIdInfoAndCompanyId(String userIdInfo, String companyId);

    /**
     * 根据资金账号和公司ID查询用户身份
     */
    Optional<UserIdentity> findByAccountAndCompanyId(String account, String companyId);

    /**
     * 根据客户号和公司ID查询用户身份
     */
    Optional<UserIdentity> findByCustIdAndCompanyId(String custId, String companyId);

    /**
     * 根据用户ID信息、用户ID类型和公司ID查询用户身份
     */
    Optional<UserIdentity> findByUserIdInfoAndUserIdClsAndCompanyId(String userIdInfo, String userIdCls,
            String companyId);

    /**
     * 根据用户ID信息和用户ID类型查询用户身份列表
     */
    List<UserIdentity> findByUserIdInfoAndUserIdType(String userIdInfo, String userIdType);

    /**
     * 检查是否存在相同的第三方账号绑定
     */
    @Query("SELECT u FROM UserIdentity u WHERE u.userIdInfo = :userIdInfo AND u.userIdType = '0'")
    List<UserIdentity> findExistingThirdPartyBinding(@Param("userIdInfo") String userIdInfo);

    /**
     * 根据QQ代码删除用户身份信息
     */
    @Modifying
    @Query("DELETE FROM UserIdentity u WHERE u.userIdInfo = :qqCode AND u.companyId = :companyId")
    void deleteByQqCode(@Param("qqCode") String qqCode, @Param("companyId") String companyId);

    /**
     * 根据客户ID删除用户身份信息
     */
    @Modifying
    @Query("DELETE FROM UserIdentity u WHERE u.custId = :custId AND u.companyId = :companyId")
    void deleteByCustId(@Param("custId") String custId, @Param("companyId") String companyId);

    /**
     * 根据用户ID信息和用户ID类型删除用户身份信息
     */
    @Modifying
    @Query("DELETE FROM UserIdentity u WHERE u.userIdInfo = :userIdInfo AND u.companyId = :companyId AND u.userIdType = :userIdType")
    void deleteByUserIdInfoAndType(@Param("userIdInfo") String userIdInfo, @Param("companyId") String companyId,
            @Param("userIdType") String userIdType);

    /**
     * 检查资金账号是否已经绑定
     */
    @Query("SELECT u FROM UserIdentity u WHERE u.account = :account AND u.companyId = :companyId")
    List<UserIdentity> findByAccountBinding(@Param("account") String account, @Param("companyId") String companyId);

    /**
     * 检查资金账号和用户ID是否已经绑定
     */
    @Query("SELECT u FROM UserIdentity u WHERE u.account = :account AND u.userIdInfo = :userIdInfo AND u.companyId = :companyId")
    List<UserIdentity> findByAccountAndUserIdBinding(@Param("account") String account,
            @Param("userIdInfo") String userIdInfo, @Param("companyId") String companyId);
}
