package com.kinginx.repository;

import com.kinginx.entity.UserSignAndUnsign;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户签约解约记录Repository
 */
@Repository
public interface UserSignAndUnsignRepository extends JpaRepository<UserSignAndUnsign, Long> {

    /**
     * 根据用户ID和公司ID查询签约解约记录
     */
    List<UserSignAndUnsign> findByUserIdAndCompanyIdOrderByTimeDesc(String userId, String companyId);

    /**
     * 根据资金账号和公司ID查询签约解约记录
     */
    List<UserSignAndUnsign> findByAccountAndCompanyIdOrderByTimeDesc(String account, String companyId);

    /**
     * 根据用户ID、公司ID和操作类型查询记录
     */
    List<UserSignAndUnsign> findByUserIdAndCompanyIdAndTypeOrderByTimeDesc(String userId, String companyId,
            Integer type);

    /**
     * 查询指定时间范围内的签约解约记录
     */
    @Query("SELECT u FROM UserSignAndUnsign u WHERE u.time BETWEEN :startTime AND :endTime ORDER BY u.time DESC")
    List<UserSignAndUnsign> findByTimeRange(@Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近的签约记录
     */
    @Query("SELECT u FROM UserSignAndUnsign u WHERE u.userId = :userId AND u.companyId = :companyId AND u.type = 0 ORDER BY u.time DESC")
    List<UserSignAndUnsign> findLatestSignRecord(@Param("userId") String userId, @Param("companyId") String companyId);

    /**
     * 查询最近的解约记录
     */
    @Query("SELECT u FROM UserSignAndUnsign u WHERE u.userId = :userId AND u.companyId = :companyId AND u.type = 1 ORDER BY u.time DESC")
    List<UserSignAndUnsign> findLatestUnsignRecord(@Param("userId") String userId,
            @Param("companyId") String companyId);
}
