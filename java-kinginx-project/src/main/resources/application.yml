server:
  port: 8080
  servlet:
    context-path: /kinginx
spring:
  application:
    name: kinginx-trading-service
  config:
    import:
      - classpath:service-handler-mapping.yml
  # Database Configuration
  datasource:
    url: ***************************************************************************************************************************************************
    username: root
    password: 1123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: create
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # Redis Configuration
  redis:
    #    cluster:
    #      nodes:
    #        - **************:7000
    #        - **************:7001
    #        - **************:7002
    #        - **************:7003
    #        - **************:7004
    #        - **************:7005
    #      max-redirects: 3
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 3000ms
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms

# Logging Configuration
logging:
  level:
    com.kinginx: INFO
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/kinginx-trading-service.log
    max-size: 100MB
    max-history: 30

# Custom Configuration
kinginx:
  # Trading Adapter Configuration
  trading:
    adapter:
      timeout: 30000
      retry-count: 3

  # Session Configuration  
  session:
    expire-time: 28800  # 8 hours in seconds

  # Company Configuration
  company:
    default-id: "15900"

  # Encryption Configuration
  encryption:
    rsa:
      private-key-path: "classpath:keys/private.pem"
      public-key-path: "classpath:keys/rsa_public_key.pem"
    des:
      key: "410301_DesPwd"

  # External Services
  external:
    # Trading System Gateway
    trading-gateway:
      url: "http://10.10.0.75:8182"
      timeout: 30000
      mock-enabled: true  # 开发环境启用Mock数据

    # 国金网厅Token服务
    gj-token:
      global-url: "http://172.16.25.30:30000/yjbapi/sas/session/register"
      simple-url: "http://172.16.25.30:30000/yjbapi/sas/instant_token/apply"
      app-id: "qqstack"
      account-type: "1"
      mock-enabled: true  # 开发环境启用Mock数据

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
