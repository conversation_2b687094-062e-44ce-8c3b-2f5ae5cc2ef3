# 服务处理器映射配置
# 对应Python项目中的bpagentspd.py中的g_reqBpagentFunctionDict

service-handler-mapping:
  # 签约解约相关
  "600001": "handle600001Sign"                    # 签约接口
  "600002": "handle600002Unsign"                  # 解约接口  
  "600003": "handle600003AccountTrace"            # 账户留痕接口
  "600010": "handle600010Login"                   # 登录接口
  "900010": "handle900010ResetPassword"           # 重置/修改密码接口
  "600011": "handle600011FundPasswordLogin"       # 资金密码登录接口
  
  # 通用查询接口 - 对应pyreqbpagentCommquery
  "600140": "handleCommQuery"                     # 委托下单
  "600141": "handleCommQuery"                     # 委托撤单
  "600410": "handleCommQuery"                     # 产品查询
  "600143": "handleCommQuery"                     # 委托修改
  "600150": "handleCommQuery"                     # 委托查询
  "600043": "handleCommQuery"                     # 证券信息查询
  "600045": "handleCommQuery"                     # 创业板权限查询
  "600046": "handleCommQuery"                     # 创业板开通
  "600020": "handleCommQuery"                     # 证券代码查询
  "600049": "handleCommQuery"                     # 新股申购
  "600050": "handleCommQuery"                     # 新股查询
  "600060": "handleCommQuery"                     # 股东账户查询
  "600070": "handleCommQuery"                     # 适当性查询
  "600080": "handleCommQuery"                     # 根据证件号查询资金账号
  "600200": "handleCommQuery"                     # 持仓查询
  "600120": "handleCommQuery"                     # 资金查询
  "600123": "handleCommQuery"                     # 资产查询
  "600124": "handleCommQuery"                     # 新股缴款查询
  "600160": "handleCommQuery"                     # 当日委托查询
  "600161": "handleCommQuery"                     # 当日成交查询
  "600170": "handleCommQuery"                     # 历史委托查询
  "600172": "handleCommQuery"                     # 历史委托查询2
  "600174": "handleCommQuery"                     # 委托撤单2
  "600180": "handleCommQuery"                     # 历史成交查询
  "600041": "handleCommQuery"                     # 风险测评查询
  "600042": "handleCommQuery"                     # ST股票权限查询
  "600230": "handleCommQuery"                     # 银证转账
  "600030": "handleCommQuery"                     # 营业部信息查询
  "600270": "handleCommQuery"                     # 银证转账查询
  "600320": "handleCommQuery"                     # 新股信息查询
  "600340": "handleCommQuery"                     # 配股查询
  "600380": "handleCommQuery"                     # 新股额度查询
  "600390": "handleCommQuery"                     # 资金流水查询
  "600391": "handleCommQuery"                     # 证券流水查询
  "600190": "handleCommQuery"                     # 交割单查询
  "600055": "handleCommQuery"                     # 证券信息查询2
  "600061": "handleCommQuery"                     # 股东账户开户查询
  "600063": "handleCommQuery"                     # 证券信息查询3
  "600064": "handleCommQuery"                     # 证券信息查询4
  "600330": "handleCommQuery"                     # 新股配号查询
  "600360": "handleCommQuery"                     # 银行账户查询
  "700713": "handleCommQuery"                     # 冻结资金查询
  "700714": "handleCommQuery"                     # 冻结资金操作
  "601160": "handleCommQuery"                     # 港股通委托查询
  "601170": "handleCommQuery"                     # 港股通历史委托
  "601180": "handleCommQuery"                     # 港股通历史成交
  "601190": "handleCommQuery"                     # 港股通交割单
  "500401": "handleCommQuery"                     # 港股通额度查询
  "500402": "handleCommQuery"                     # 港股通汇率查询
  "600121": "handleCommQuery"                     # 新股申购额度查询
  "500403": "handleCommQuery"                     # 港股通标的查询
  "500062": "handleCommQuery"                     # 科创板权限开通
  "500060": "handleCommQuery"                     # 科创板权限查询
  "500061": "handleCommQuery"                     # 科创板签署协议
  "500063": "handleCommQuery"                     # 科创板基础知识测试
  "800011": "handleCommQuery"                     # 短信验证码发送
  "800012": "handleCommQuery"                     # 短信验证码校验
  "800013": "handleCommQuery"                     # 客户资料更新
  "800014": "handleCommQuery"                     # 客户信息查询
  "800015": "handleCommQuery"                     # 客户信息查询2
  "600044": "handleCommQuery"                     # 客户资料更新2
  "600047": "handleCommQuery"                     # 客户资料更新3
  "601140": "handleCommQuery"                     # 港股通委托下单
  "601150": "handleCommQuery"                     # 港股通委托撤单
  "600210": "handleCommQuery"                     # 银证转账2
  "600220": "handleCommQuery"                     # 银证转账3
  "600052": "handleCommQuery"                     # 证券信息查询5
  "600026": "handleCommQuery"                     # 证券信息查询6
  
  # 特殊处理接口 - 对应pyreqbpagent600040
  "600040": "handle600040UserInfo"                # 用户信息查询接口
  
  # 会话相关
  "600401": "handle600401ExtendSession"           # 延长会话
  
  # 公告相关
  "600090": "handle600090InsertNotice"            # 插入券商公告
  "600091": "handle600091SelectNotice"            # 查询公告
  "600400": "handle600400SelectNoticeTx"          # 腾讯查询公告
  "600098": "handle600098DeleteNotice"            # 删除券商公告
  "600099": "handle600099UpdateNotice"            # 修改券商公告
  
  # Token相关
  "700001": "handle700001TokenExchange"           # session换国金网厅token
  
  # 测试接口
  "testforsession": "handleTestForSession"        # 测试会话
  "getclientip": "handleGetClientIp"              # 获取客户IP信息
  "GetNginxInfo": "handleGetNginxInfo"            # 获取应答字典残留应答
  "ClearNginxXpAns": "handleClearNginxXpAns"      # 清空残留应答
  
  # 测试环境专用接口
  "400001": "handle400001QueryAccount"            # 查询客户号
  "400002": "handle400002QueryUserId"             # 查询腾讯ID
  "400020": "handle400020CheckSmsCode"            # 查询验证码
  
  # 新客理财相关
  "800060": "handleCommQuery"                     # 查询TA账户信息
  "800049": "handleCommQuery"                     # 开通TA账户
  "800063": "handleCommQuery"                     # 理财产品适当性匹配查询
  "800064": "handleCommQuery"                     # 签署接口
  "800410": "handleCommQuery"                     # 查询新客理财产品信息
  "600171": "handleCommQuery"                     # 持仓记录查询
  "600173": "handleCommQuery"                     # 持仓记录查询2
  "800140": "handleCommQuery"                     # 新增理财委托接口
  "800061": "handleCommQuery"                     # 协议留痕接口
  "800160": "handleCommQuery"                     # 委托记录查询接口
  
  # 风险相关
  "600034": "handleCommQuery"                     # 查询风险股票提示信息
