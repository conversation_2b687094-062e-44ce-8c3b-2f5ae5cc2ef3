# Kinginx Trading Service

基于Java 1.8 + SpringBoot的证券交易服务系统，这是对原Python nginx+python项目的Java重构实现。

## 项目概述

本项目实现了原Python项目中的核心功能，特别是410301签约功能的完整流程。主要包括：

- 统一请求入口处理
- 410301签约业务流程
- 交易适配器服务
- 统一认证服务
- Redis会话管理
- 数据库操作

## 技术栈

- **Java**: 1.8
- **SpringBoot**: 2.7.18
- **数据库**: MySQL 8.0
- **缓存**: Redis Cluster
- **构建工具**: Maven
- **ORM**: Spring Data JPA
- **JSON处理**: FastJSON + Jackson
- **加密**: BouncyCastle
- **HTTP客户端**: Apache HttpClient

## 项目结构

```
src/main/java/com/kinginx/
├── KinginxTradingServiceApplication.java    # 主启动类
├── common/                                  # 公共模块
│   ├── constants/ServiceConstants.java     # 服务常量定义
│   ├── dto/                                # 数据传输对象
│   └── exception/                          # 异常处理
├── config/                                 # 配置类
│   ├── AsyncConfig.java                    # 异步配置
│   └── RedisConfig.java                    # Redis配置
├── controller/                             # 控制器层
│   └── TradingController.java              # 交易控制器
├── entity/                                 # 实体类
│   ├── UserIdentity.java                  # 用户身份实体
│   ├── UserSignMark.java                  # 签约留痕实体
│   └── UserSignAndUnsign.java             # 签约解约记录实体
├── repository/                             # 数据访问层
├── service/                                # 服务层
│   ├── SignContractService.java           # 签约服务
│   ├── TradingAdapterService.java         # 交易适配器服务
│   ├── UnifiedAuthService.java            # 统一认证服务
│   ├── RedisService.java                  # Redis服务
│   ├── SmsVerificationService.java        # 短信验证服务
│   └── util/CryptoUtil.java               # 加密工具类
└── resources/
    ├── application.yml                     # 应用配置
    └── agreements/                         # 协议文件
```

## 核心功能

### 1. 410301签约流程

实现了完整的410301签约业务流程：

1. **签约前检查**: 验证用户是否已签约
2. **短信验证**: 验证XID_SESSION短信验证码
3. **交易适配**: 调用410301适配器
  - 以operway=5验证客户身份
  - 调用410323/410324管理权限
  - 以operway=e确认开通成功
4. **统一认证**: 调用T0005005_sign进行签约
5. **签约留痕**: 记录签约操作和协议信息
6. **会话管理**: 将SESSION存储到Redis

### 2. 统一认证服务

- **T0005005_sign**: 用户签约
- **T0005001_makesession**: 生成登录票据
- **用户签约检查**: 防止重复签约
- **签约留痕**: 记录协议签署信息

### 3. 交易适配器

- **410301适配**: 组合业务签约流程
- **通用适配器调用**: 支持多种交易接口
- **异步处理**: 使用CompletableFuture提升性能

### 4. 会话管理

- **Redis集群**: 支持分布式会话存储
- **会话过期**: 自动管理会话生命周期
- **会话延期**: 支持会话时间延长

## 配置说明

### 数据库配置

```yaml
spring:
  datasource:
    url: ******************************************
    username: jwl
    password: Password
```

### Redis集群配置

```yaml
spring:
  redis:
    cluster:
      nodes:
        - 192.168.154.37:7000
        - 192.168.154.50:7001
        - 192.168.154.53:7002
        - 192.168.154.37:7003
        - 192.168.154.50:7004
        - 192.168.154.53:7005
    password: Password
```

### 自定义配置

```yaml
kinginx:
  session:
    expire-time: 28800  # 会话过期时间(秒)
  external:
    trading-gateway:
      url: "http://10.10.0.75:8182"  # 交易网关地址
```

## API接口

### 统一请求入口

**POST** `/api/trading/request`

请求格式：

```json
{
  "REQUESTS": [
    {
      "REQ_MSG_HDR": {
        "SERVICE_ID": "600001",
        "TIME_STAMP": "**********",
        "SIGN": "signature"
      },
      "REQ_COMM_DATA": {
        "LOGIN_CODE": "account",
        "TRD_PWD": "encrypted_password",
        "USER_ID": "user_id",
        "USER_ID_CLS": "1",
        "COMPANY_ID": "15900"
      }
    }
  ]
}
```

响应格式：

```json
{
  "ANSWERS": [
    {
      "ANS_MSG_HDR": {
        "MSG_CODE": "0",
        "MSG_LEVEL": "0",
        "MSG_TEXT": "签约成功",
        "DEBUG_MSG": "",
        "SESSION": "session_token"
      },
      "ANS_COMM_DATA": [
        {
          "ONLINE_FLAG": "0"
        }
      ]
    }
  ]
}
```

### 支持的功能号

- **600001**: 华林微证券签约接口
- **600002**: 华林微证券解约接口
- **600003**: 账户留痕接口
- **600010**: bpagent登录接口
- **700001**: session换国金网厅token接口

## 运行说明

### 环境要求

- Java 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis Cluster

### 启动步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd java-kinginx-project
```

2. **配置数据库**

- 创建数据库 `kbss_kcas`
- 执行数据库初始化脚本: `mysql -u root -p < sql/init.sql`

3. **配置Redis**

- 启动Redis集群
- 确保配置文件中的Redis节点地址正确

4. **Java 1.8兼容性检查**

```bash
# Linux/Mac
chmod +x compile-check.sh && ./compile-check.sh

# Windows
compile-check.bat
```

5. **编译运行**

```bash
# 使用启动脚本 (推荐)
chmod +x start.sh && ./start.sh  # Linux/Mac
start.bat                        # Windows

# 或手动启动
mvn clean compile
mvn spring-boot:run
```

6. **访问服务**

- 服务地址: http://localhost:8080/kinginx
- 健康检查: http://localhost:8080/kinginx/api/trading/health

### 测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=CryptoUtilTest
mvn test -Dtest=KinginxRequestTest

# 运行集成测试 (需要外部依赖)
mvn test -Dtest=TradingControllerSimpleTest

# 跳过测试编译
mvn compile -DskipTests
```

**测试说明**：

- `CryptoUtilTest`: 加密工具类测试
- `KinginxRequestTest`: 请求/响应DTO测试
- `TradingControllerSimpleTest`: 控制器集成测试
- `TradingControllerTest`: 控制器单元测试 (使用Mock)

## 部署说明

### Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY target/kinginx-trading-service-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置

1. **JVM参数优化**

```bash
-Xms2g -Xmx4g -XX:+UseG1GC
```

2. **日志配置**

```yaml
logging:
  level:
    com.kinginx: INFO
  file:
    name: logs/kinginx-trading-service.log
```

3. **监控配置**

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
```

## 注意事项

1. **Java版本兼容性**:
  - ✅ 已修复`readAllBytes()`方法的Java 1.8兼容性问题
  - ✅ 使用自定义的`readAllBytesFromInputStream()`方法替代
  - ✅ 所有代码均兼容Java 1.8+

2. **安全性**:
  - 生产环境请配置真实的RSA密钥对
  - 数据库密码请使用加密存储
  - Redis密码请定期更换

3. **性能优化**:
  - 根据实际负载调整线程池大小
  - 优化数据库连接池配置
  - 合理设置Redis过期时间

4. **监控告警**:
  - 配置应用性能监控
  - 设置关键业务指标告警
  - 定期检查日志异常

## 联系方式

如有问题请联系开发团队。
