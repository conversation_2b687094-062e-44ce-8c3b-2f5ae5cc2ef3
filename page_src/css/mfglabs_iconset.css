/*  MFG Labs iconset 1.0
    
    -------------------------------------------------------
    
    License
    -------------------------------------------------------
    • The MFG Labs iconset font is licensed under the SIL Open Font License - http://scripts.sil.org/OFL
    • MFG Labs inconset CSS files are licensed under the MIT License -
      http://opensource.org/licenses/mit-license.html
    • The MFG Labs iconset pictograms are licensed under the CC BY 3.0 License - http://creativecommons.org/licenses/by/3.0/
    • Attribution is no longer required in Font Awesome 3.0, but much appreciated:
      MFG Labs inconset by MFG Labs

    Contact
    -------------------------------------------------------
    Email: <EMAIL>
    Twitter: http://twitter.com/mfg_labs
    

    */
@font-face {
    font-family: 'mfg_labs_iconsetregular';
    src: url('font/mfglabsiconset-webfont.eot');
    src: url('font/mfglabsiconset-webfont.eot?#iefix') format('embedded-opentype'),
         url('font/mfglabsiconset-webfont.woff') format('woff'),
         url('font/mfglabsiconset-webfont.ttf') format('truetype'),
         url('font/mfglabsiconset-webfont.svg#mfg_labs_iconsetregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

i, .icon {
  font-family: 'mfg_labs_iconsetregular';
  font-style: normal;
  speak: none;
  font-weight: normal;
  font-size: 1em;
  -webkit-font-smoothing: antialiased;
}


.icon2x { font-size: 2em; }
.icon3x { font-size: 3em; }



/* style exemples */
.gradient {
  color: #999;
  text-shadow: 1px 1px 1px rgba(27, 27, 27, 0.19);
  
  background-image: -webkit-gradient(
  linear,
  left top, left bottom,
  from(rgba( 182, 182, 182, 1)),
  to(rgba(60, 60, 60, 1))
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}

.gradient:hover, .gradient .current {
  color: #eee;
	text-shadow: 0px 0px 3px rgba(255, 255, 255, 0.25);
	
  background-image: -webkit-gradient(
  linear,
  left top, left bottom,
  from(rgba( 255, 255, 255, 1)),
  to(rgba(187, 187, 187, 1))
  );
  
}

/*  MFG Labs iconset uses the Unicode Private Use Area (PUA) to ensure screen
    readers do not read off random characters that represent icons.
    We also use semantic unicode when they are available for the icon we provide. */

.icon-cloud:before                { content: "\2601"; }
.icon-at:before                   { content: "\0040"; }
.icon-plus:before                 { content: "\002B"; }
.icon-minus:before                { content: "\2212"; }

.icon-arrow_up:before             { content: "\2191"; }
.icon-arrow_down:before           { content: "\2193"; }
.icon-arrow_right:before          { content: "\2192"; }
.icon-arrow_left:before           { content: "\2190"; }
.icon-chevron_down:before         { content: "\f004"; }
.icon-chevron_up:before           { content: "\f005"; }
.icon-chevron_right:before        { content: "\f006"; }
.icon-chevron_left:before         { content: "\f007"; }
.icon-reorder:before              { content: "\f008"; }
.icon-list:before                 { content: "\f009"; }
.icon-reorder_square:before       { content: "\f00a"; }
.icon-reorder_square_line:before  { content: "\f00b"; }
.icon-coverflow:before            { content: "\f00c"; }
.icon-coverflow_line:before       { content: "\f00d"; }
.icon-pause:before                { content: "\f00e"; }
.icon-play:before                 { content: "\f00f"; }

.icon-step_forward:before         { content: "\f010"; }
.icon-step_backward:before        { content: "\f011"; }
.icon-fast_forward:before         { content: "\f012"; }
.icon-fast_backward:before        { content: "\f013"; }
.icon-cloud_upload:before         { content: "\f014"; }
.icon-cloud_download:before       { content: "\f015"; }
.icon-data_science:before         { content: "\f016"; }
.icon-data_science_black:before   { content: "\f017"; }
.icon-globe:before                { content: "\f018"; }
.icon-globe_black:before          { content: "\f019"; }
.icon-math_ico:before             { content: "\f01a"; }
.icon-math:before                 { content: "\f01b"; }
.icon-math_black:before           { content: "\f01c"; }
.icon-paperplane_ico:before       { content: "\f01d"; }
.icon-paperplane:before           { content: "\f01e"; }
.icon-paperplane_black:before     { content: "\f01f"; }

/* \f020 doesn't work in Safari. all shifted one down */
.icon-color_balance:before        { content: "\f020"; }
.icon-star:before                 { content: "\2605"; }
.icon-star_half:before            { content: "\f022"; }
.icon-star_empty:before           { content: "\2606"; }
.icon-star_half_empty:before      { content: "\f024"; }
.icon-reload:before               { content: "\f025"; }

.icon-heart:before                { content: "\2665"; }
.icon-heart_broken:before         { content: "\f028"; }
.icon-hashtag:before              { content: "\f029"; }
.icon-reply:before                { content: "\f02a"; }
.icon-retweet:before              { content: "\f02b"; }
.icon-signin:before               { content: "\f02c"; }
.icon-signout:before              { content: "\f02d"; }
.icon-download:before             { content: "\f02e"; }
.icon-upload:before               { content: "\f02f"; }


.icon-placepin:before             { content: "\f031"; }
.icon-display_screen:before       { content: "\f032"; }
.icon-tablet:before               { content: "\f033"; }
.icon-smartphone:before           { content: "\f034"; }
.icon-connected_object:before     { content: "\f035"; }
.icon-lock:before                 { content: "\F512"; }
.icon-unlock:before               { content: "\F513"; }
.icon-camera:before               { content: "\F4F7"; }
.icon-isight:before               { content: "\f039"; }
.icon-video_camera:before         { content: "\f03a"; }
.icon-random:before               { content: "\f03b"; }
.icon-message:before              { content: "\F4AC"; }
.icon-discussion:before           { content: "\f03d"; }
.icon-calendar:before             { content: "\F4C5"; }
.icon-ringbell:before             { content: "\f03f"; }

.icon-movie:before                { content: "\f040"; }
.icon-mail:before                 { content: "\2709"; }
.icon-pen:before                  { content: "\270F"; }
.icon-settings:before             { content: "\9881"; }
.icon-measure:before              { content: "\f044"; }
.icon-vector:before               { content: "\f045"; }
.icon-vector_pen:before           { content: "\2712"; }
.icon-mute_on:before              { content: "\f047"; }
.icon-mute_off:before             { content: "\f048"; }
.icon-home:before                 { content: "\2302"; }
.icon-sheet:before                { content: "\f04a"; }
.icon-arrow_big_right:before      { content: "\21C9"; }
.icon-arrow_big_left:before       { content: "\21C7"; }
.icon-arrow_big_down:before       { content: "\21CA"; }
.icon-arrow_big_up:before         { content: "\21C8"; }
.icon-dribbble_circle:before      { content: "\f04f"; }

.icon-dribbble:before             { content: "\f050"; }
.icon-facebook_circle:before      { content: "\f051"; }
.icon-facebook:before             { content: "\f052"; }
.icon-git_circle_alt:before       { content: "\f053"; }
.icon-git_circle:before           { content: "\f054"; }
.icon-git:before                  { content: "\f055"; }
.icon-octopus:before              { content: "\f056"; }
.icon-twitter_circle:before       { content: "\f057"; }
.icon-twitter:before              { content: "\f058"; }
.icon-google_plus_circle:before   { content: "\f059"; }
.icon-google_plus:before          { content: "\f05a"; }
.icon-linked_in_circle:before     { content: "\f05b"; }
.icon-linked_in:before            { content: "\f05c"; }
.icon-instagram:before            { content: "\f05d"; }
.icon-instagram_circle:before     { content: "\f05e"; }
.icon-mfg_icon:before             { content: "\f05f"; }
.icon-xing:before                 { content: "\F532"; }
.icon-xing_circle:before          { content: "\F533"; }

.icon-mfg_icon_circle:before      { content: "\f060"; }
.icon-user:before                 { content: "\f061"; }
.icon-user_male:before            { content: "\f062"; }
.icon-user_female:before          { content: "\f063"; }
.icon-users:before                { content: "\f064"; }

.icon-file_open:before            { content: "\F4C2"; }
.icon-file_close:before           { content: "\f067"; }
.icon-file_alt:before             { content: "\f068"; }
.icon-file_close_alt:before       { content: "\f069"; }
.icon-attachment:before           { content: "\f06a"; }
.icon-check:before                { content: "\2713"; }
.icon-cross_mark:before           { content: "\274C"; }
.icon-cancel_circle:before        { content: "\F06E"; }
.icon-check_circle:before         { content: "\f06d"; }
.icon-magnifying:before           { content: "\F50D"; }

.icon-inbox:before                { content: "\f070"; }
.icon-clock:before                { content: "\23F2"; }
.icon-stopwatch:before            { content: "\23F1"; }
.icon-hourglass:before            { content: "\231B"; }
.icon-trophy:before               { content: "\f074"; }
.icon-unlock_alt:before           { content: "\F075"; }
.icon-lock_alt:before             { content: "\F510"; }
.icon-arrow_doubled_right:before  { content: "\21D2"; }
.icon-arrow_doubled_left:before   { content: "\21D0"; }
.icon-arrow_doubled_down:before   { content: "\21D3"; }
.icon-arrow_doubled_up:before     { content: "\21D1"; }
.icon-link:before                 { content: "\f07B"; }
.icon-warning:before              { content: "\2757"; }
.icon-warning_alt:before          { content: "\2755"; }
.icon-magnifying_plus:before      { content: "\f07E"; }
.icon-magnifying_minus:before     { content: "\f07F"; }

.icon-white_question:before       { content: "\2754"; }
.icon-black_question:before       { content: "\2753"; }
.icon-stop:before                 { content: "\f080"; }
.icon-share:before                { content: "\f081"; }
.icon-eye:before                  { content: "\f082"; }
.icon-trash_can:before            { content: "\f083"; }
.icon-hard_drive:before           { content: "\f084"; }
.icon-information_black:before    { content: "\f085"; }
.icon-information_white:before    { content: "\f086"; }
.icon-printer:before              { content: "\f087"; }
.icon-letter:before               { content: "\f088"; }
.icon-soundcloud:before           { content: "\f089"; }
.icon-soundcloud_circle:before    { content: "\f08A"; }
.icon-anchor:before               { content: "\2693"; }

.icon-female_sign:before          { content: "\2640"; }
.icon-male_sign:before            { content: "\2642"; }
.icon-joystick:before             { content: "\F514"; }
.icon-high_voltage:before         { content: "\26A1"; }
.icon-fire:before                 { content: "\F525"; }
.icon-newspaper:before            { content: "\F4F0"; }
.icon-chart:before                { content: "\F526"; }
.icon-spread:before               { content: "\F527"; }

.icon-spinner_1:before            { content: "\F528"; }
.icon-spinner_2:before            { content: "\F529"; }

.icon-chart_alt:before            { content: "\F530"; }
.icon-label:before                { content: "\F531"; }

.icon-brush:before                { content: "\E000"; }
.icon-refresh:before              { content: "\E001"; }

.icon-node:before                 { content: "\E002"; }
.icon-node_2:before               { content: "\E003"; }
.icon-node_3:before               { content: "\E004"; }
.icon-link_2_nodes:before         { content: "\E005"; }
.icon-link_3_nodes:before         { content: "\E006"; }
.icon-link_loop_nodes:before      { content: "\E007"; }
.icon-node_size:before            { content: "\E008"; }
.icon-node_color:before           { content: "\E009"; }
.icon-layout_directed:before      { content: "\E010"; }
.icon-layout_radial:before        { content: "\E011"; }
.icon-layout_hierarchical:before  { content: "\E012"; }
.icon-node_link_direction:before  { content: "\E013"; }
.icon-node_link_short_path:before { content: "\E014"; }
.icon-node_cluster:before         { content: "\E015"; }
.icon-display_graph:before        { content: "\E016"; }
.icon-node_link_weight:before     { content: "\E017"; }
.icon-more_node_links:before      { content: "\E018"; }
.icon-node_shape:before           { content: "\E00A"; }
.icon-node_icon:before            { content: "\E00B"; }
.icon-node_text:before            { content: "\E00C"; }
.icon-node_link_text:before       { content: "\E00D"; }
.icon-node_link_color:before      { content: "\E00E"; }
.icon-node_link_shape:before      { content: "\E00F"; }

.icon-credit_card:before          { content: "\F4B3"; }
.icon-disconnect:before           { content: "\F534"; }
.icon-graph:before                { content: "\F535"; }
.icon-new_user:before             { content: "\F536"; }