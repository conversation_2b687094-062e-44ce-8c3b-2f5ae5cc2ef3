*{
    margin: 0;
    padding: 0;
}

#head{
    margin: 100px auto 0;
    height: 50px;
    line-height: 50px;
    width: 60%;
    text-align: center;
    position: relative;
}

#head p{
    text-align: left;
    height: inherit;
    line-height: inherit;
    font-size: 25px;
    font-weight: 600;
}

.li-icon{
    position: absolute;
    width: 150px;
    top: 0;
    right: 0;
    text-align: right;
}

.li-icon a{
    color: #2d8bf0;
    transition: all 1s;
}

.li-icon a:hover{
    color: #ea569a;
}

.li-icon i{
    font-size: 25px;
    height: 30px;
    line-height: 30px;
    padding-right: 10px;
}

#content{
    text-align: center;
}

table{
    width: 60%;
    margin: 0 auto;
    border: 1px solid #DDDDDD;
    border-collapse: collapse; /* 防止父和子标签的边框合并，使得边框变粗*/
    table-layout: fixed;
}

thead{
    color: white;
    background-color: #2d8bf0;
}

tbody{
    height: 50px;
}

th.th_xa_name {
    width: 15%;
}

th.th_xa_type {
    width: 10%;
}

th.th_system_id {
    width: 10%;
}

th.th_xa_config {
    width: 40%;
}

th.th_remark {
    width: 20%;
}

th.th_operator {
    width: 10%;
}

th, td{
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

td.config_str{

}

thead tr{
    width: 80%;
    height: 36px;
}
thead tr th{
    line-height: 100%;
    color: white;
}

tbody tr{
    height: 55px;
    line-height: 55px;
}

.xa_name:active{
    background-color: #ea569a;
}

input{
    border: none;
    outline: none;
}

.btn{
    color: white;
    border-radius: 8px;
    background-color: #2d8cf0;
    text-align: center;
    width: 50px;
    font-size: 16px;
    padding: 4px 10px;
    transition: all 1s;
}

.btn:hover{
    background-color: #ff2775;
}

#clk-win{
    width: 1000px;
    left: 25%;
    text-align: center;
    background-color: white;
    border-radius: 10px;
    margin: 150px auto 0;
    opacity: 1;
    padding: 10px 0 30px;
}

.win-head{
    position: relative;
    border-bottom: 1px solid #DDDDDD;
    padding-bottom: 25px;
    margin-bottom: 25px;
}

#clk-win, #mask {
    transition: all 300ms;
}

#clk-win p{
    text-align: center;
    margin: 10px 0 20px;
    font-weight: 600;
    font-size: 20px;
    height: 30px;
    line-height: 30px;
}

#clk-win div.item{
    width: 90%;
    height: 50px;
    margin: 10px auto 20px;
    position: relative;
}

.necess::before{
    content: '*';
    color: red;
}

label{
    float: left;
    text-align: right;
    margin-right: 10px;
    width: 110px;
    height: 40px;
    line-height: 40px;
}

.value{
    float: left;
    height: 100%;
    width: 780px;
    position: relative;
}

.value input{
    font-size: 15px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    border-radius: 5px;
    border: 1px solid #DDDDDD;
    box-sizing: border-box;
    padding-left: 8px;
}

.value #select-icon{
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 15px;
}

#xa_type{
    position: absolute;
    top: 45px;
    left: 120px;
    width: 780px;
    border-radius: 8px;
    background-color: white;
    z-index: 1001;
    text-align: left;
    box-shadow: 0 1px 6px rgba(0,0,0,.2);
}

#xa_type ul{
    margin: 0;
    list-style-type: none;
    padding: 5px 0;
}

#xa_type li{
    list-style-type: none;
    border: none;
    height: 40px;
    line-height: 40px;
    transition: all 1s;
    padding-left: 20px;
}

#xa_type li:hover{
    background: #f3f3f3;
}

.value input:focus{
    border: 1px solid #2aabd2;
    box-shadow: 0 0 0 2px rgba(45,140,240,.2);
}

#clk-win .tool-btn{
    border-top: 1px solid #DDDDDD;
    padding-top: 10px;
    text-align: center;
    height: 40px;
}

.tool-btn input{
    text-align: center;
    float: right;
    height: 40px;
    line-height: 40px;
    width: 80px;
    border-radius: 6px;
    padding: 0 15px;
    font-size: 18px;
    font-weight: 300;
    color: white;
    background-color: #2d8cf0;
    transition: all 1s;
}

.tool-btn input:hover{
    background-color: #ff2775;
}

#clk-win #cancal{

}

#clk-win #ok{
    margin-left: 20px;
}

#mask{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(55,55,55,.6);
    overflow: auto;
}
