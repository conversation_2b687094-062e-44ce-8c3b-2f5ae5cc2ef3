/**
 * Created by hasee on 4/11/2019.
 */

var xa_config_info = {
    XA_KCXP: {
        host: "IP地址", port: "端口", req_queue: "请求队列", ans_queue: "应答队列"
    },
    XA_MYSQL: {
        host: "IP地址", port: "端口", db: "数据库名称", user: "用户名", password: "密码", charset: "连接编码"
    },
    XA_KDGP: {
        host: "IP地址", port: "端口", appID: "appID", appSecret: "appSecret",
        appKey: "appKey"
    },
    XA_ORACLE: {
        host: "IP地址", port: "端口", protocol:"服务名" , user: "账号", password: "密码"
    }
};
var div_mask = document.getElementById("mask");
var btn_ok = document.getElementById("ok");
var btn_cancal = document.getElementById("cancal");
var i_selector = document.getElementById("select-icon");
var div_xa_type = document.getElementById("xa_type");
var ul_selector = document.getElementById("selector");
var div_xa_config = document.getElementById("xa_config");

var p_win_title = document.getElementById("win-title");
var win_clk = document.getElementById("clk-win");

var input_xa_name = document.getElementById("xa_name");
var input_xa_type_value = document.getElementById("xa_type_value");
var input_remark = document.getElementById("remark");
var input_system_id = document.getElementById("system_id");

var tbody_res_list = document.getElementById("res_list");

function assembleData (service_id, req_comm_data) {
    var reqJson = {
        REQUESTS: [{
            REQ_COMM_DATA: req_comm_data, // 请求内容
            REQ_MSG_HDR: {
                SERVICE_ID: service_id // 功能号
            }
        }]
    };
    return JSON.stringify(reqJson);
}

// 用于初始化弹框的内容
function initDialog() {
    input_xa_name.value = "";
    input_xa_type_value.value = "";
    input_system_id.value = "";

    div_xa_config.innerHTML = "";
    input_remark.value = "";

    input_xa_name.disabled = false;
    input_xa_type_value.disabled = false;
    input_system_id.disabled = false;
}

// 用于判断当前节点是父节点的第一个节点
function idx_of_parentElem(elem) {
    var children = elem.parentNode.childNodes;
    for (var i=0; i<children.length; i++){
        if (elem == children[i])
            return i;
    }
}

function toggleMask(show) {
    if (show) {
        div_mask.style.opacity = "0";
        win_clk.style.opacity = "0";
        div_mask.style.display = "inline-block";
        win_clk.style.display = "inline-block";
        setTimeout(function() {
            div_mask.style.opacity = "1";
            win_clk.style.opacity = "1";
        }, 20);
    }
    else {
        div_mask.style.opacity = "0";
        win_clk.style.opacity = "0";
        setTimeout(function() {
            win_clk.style.display = "none";
            div_mask.style.display = "none";
        }, 350);
    }
}

// 用于修改框 xa_config 配置的初始化
function format_xa_config_dom_str(xa_type, xa_config_value) {
    var config_elems_str = '';
    var xa_config = xa_config_info[xa_type];
    xa_config_arr = xa_config_value.split(",");
    var i = 0;
    for(var key in xa_config){
        config_elems_str += '<div class="item"><label class="necess">' + xa_config[key] + '：'
            + '</label><div class="value"><input type="text" '+ 'name="' + key
            + '" value=' + xa_config_arr[i] + '></div></div>';
        i += 1;
    }
    return config_elems_str;
}

// 用于添加框 xa_config 的项目的显示
function get_xa_config_dom(xa_type) {
    var config_elems_str = '';
    var xa_config = xa_config_info[xa_type];
    for(var key in xa_config){
        config_elems_str += '<div class="item"> <label class="necess">' + xa_config[key] + '：'
            + '</label><div class="value"><input type="text" '+ 'name="' + key
            + '"></div></div>';
    }
    return config_elems_str;
}

// 用于配置列表各资源项目的显示
function format_res_items(config_info) {
    var xa_config_str = format_xa_config_str(config_info["XA_TYPE"], config_info["XA_CONFIG"]);
    var res_config_dom_str = '<tr><td>' + config_info['XA_NAME'] + '</td><td>' + config_info['XA_TYPE']
        + '</td><td>' + config_info['THIRD_PARTY_SYSTEM_ID'] + '</td><td>' + xa_config_str
        + '</td><td>' + config_info['REMARK'] + '</td><td>'
        + '<input class="btn btn-modi" type="button" value="修改">'
        + ' <input class="btn btn-del" type="button" value="删除"></td></tr>';
    return res_config_dom_str;
}

// 用于将 dom字符串 转化成为 dom对象
function parseDom(arg) {
    var objE = document.createElement("tbody");
    objE.innerHTML = arg;
    return objE.childNodes;
}

// ajax方法
function ajax_post(url, data, func) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if(xhr.readyState === 4) {
            if(xhr.status === 200){
                func(xhr.responseText);
            }
            else{
                alert("操作失败！");
            }
        }
    };
    xhr.send(data);
}

// 组xa_config的字符串
function format_xa_config_str(xa_type, xa_config_json) {
    var config_str = "";
    var values = [];
    var key;
    switch (xa_type){
        case "XA_MYSQL":
            for (key in xa_config_info[xa_type]) {
                values.push(xa_config_json[key]);
            }
            config_str = '"' + values.join('","') + '"';
            break;
        case "XA_ORACLE":
            for (key in xa_config_info[xa_type]) {
                values.push(xa_config_json[key]);
            }
            config_str = '"' + values.join('","') + '"';
            break;
        case "XA_KCXP":
            for(i=0; i<xa_config_json.length; i++){
                for(var j=0; j<4; j++){
                    values.push(xa_config_json[i][j]);
                }
            }
            config_str = '"' + values.join('","') + '"';
            break;
        case "XA_KDGP":
            for (key in xa_config_info[xa_type]) {
                values.push(xa_config_json[0][key]);
            }
            config_str = '"' + values.join('","') + '"';
            break;
    }
    return config_str;
}

// 初始化xa_config的对象
function get_xa_config_json(xa_type, xa_config_str) {
    xa_config_str = xa_config_str.substr(1).substring(0, xa_config_str.length-2);
    var config_arr = xa_config_str.split('","');
    var config_json;
    var i=0;
    var key;
    switch (xa_type){
        case "XA_MYSQL":
            config_json = {};
            for (key in xa_config_info[xa_type]) {
                config_json[key] = config_arr[i++];
            }
            break;
        case "XA_ORACLE":
            config_json = {};
            for (key in xa_config_info[xa_type]) {
                config_json[key] = config_arr[i++];
            }
            break;
        case "XA_KCXP":
            config_json = [];
            config_json.push(config_arr);
            break;
        case "XA_KDGP":
            config_json = [{}];
            for (key in xa_config_info[xa_type]) {
                config_json[0][key] = config_arr[i++];
            }
            break;
    }
    // console.log(config_json);
    return config_json;
}

// 添加资源配置
var btn_add = document.getElementById("add");
btn_add.onclick = function (){
    initDialog();
    win_clk.flag = "ADD";
    p_win_title.innerHTML = "添加资源配置";
    toggleMask(1);
};

// 刷新资源配置
var btn_fresh = document.getElementById("refresh");
btn_fresh.onclick = function (){
    var req_str = assembleData('get_kinginx_config', {});
    var config_info = {XA_NAME: "", XA_TYPE: "", THIRD_PARTYSYSTEM_ID: "", XA_CONFIG: "", REMARK: ""};
    var dom_str = "";
    ajax_post(
        'manage_req/',
        req_str,
        function (ans) {
            var config_json = JSON.parse(ans);
            var xa_config_list = config_json["ANSWERS"][0]["ANS_COMM_DATA"][0];
            if (xa_config_list === []) {
                console.log("没有获取到配置信息");
            }
            else {
                for (var key in xa_config_list) {
                    config_info["XA_NAME"] = xa_config_list[key]["XA_NAME"];
                    config_info["XA_TYPE"] = xa_config_list[key]["XA_TYPE"];
                    config_info["THIRD_PARTY_SYSTEM_ID"] = xa_config_list[key]["THIRD_PARTY_SYSTEM_ID"];
                    config_info["XA_CONFIG"] = xa_config_list[key]["XA_CONFIG"];
                    config_info["REMARK"] = xa_config_list[key]["REMARK"];
                    dom_str += format_res_items(config_info);
                    tbody_res_list.innerHTML = dom_str;
                }
                alert(config_json["ANSWERS"][0]["ANS_MSG_HDR"]["MSG_TEXT"]);
            }
        }
    );
};

// 应用资源配置
var btn_apply = document.getElementById("apply");
btn_apply.onclick = function (){
    var kinginx_config_json = {};
    var res_list = tbody_res_list.getElementsByTagName('tr');
    for (var i=0; i<res_list.length; i++) {
        var items = res_list[i].getElementsByTagName('td');
        kinginx_config_json[items[0].innerText] = {
            GROUP_ID: "", XA_TYPE: items[1].innerText, XA_STATUS: 1, REMARK: items[4].innerText, KEY_STR: "",
            XA_NAME: items[0].innerText, THIRD_PARTY_SYSTEM_ID: items[2].innerText,
            XA_CONFIG: get_xa_config_json(items[1].innerText, items[3].innerText)
        };
    }
    var req_str = assembleData('update_kinginx_config', kinginx_config_json);
    // console.log(req_str);
    ajax_post(
        'manage_req/',
        req_str,
        function (ans) {
            // console.log(ans);
            var config_json = JSON.parse(ans);
            // var code = xa_config_list = config_json["ANSWERS"][0]["ANS_MSG_HDR"]["MSG_CODE"];
            alert(config_json["ANSWERS"][0]["ANS_MSG_HDR"]["MSG_TEXT"]);
        }
    );
};

// 响应点击事件
ul_selector.onclick = function (ev) {
    var event = ev || window.event;
    var target = event.target || event.srcElement;
    if (target.nodeName.toLocaleLowerCase() === 'li') {
        var xa_type_elem = target.parentNode.parentNode;
        var input_elem = xa_type_elem.previousElementSibling.childNodes[0];
        input_elem.value = target.innerText;
        xa_type_elem.style.display = "none";
        // 将选中的xa的配置项目显示出来
        div_xa_config.innerHTML = get_xa_config_dom(target.innerText);
    }
};

// 响应修改和删除
tbody_res_list.onclick = function (ev) {
    ev = ev || window.event;
    var target = ev.target || ev.srcElement;
    if (target.nodeName.toLocaleLowerCase() === 'input') {
        switch (target.className){
            case "btn btn-modi":
                p_win_title.innerHTML = "修改资源配置";
                win_clk.flag = "MODIFY";
                var res_item = target.parentNode.parentNode;
                win_clk.index = idx_of_parentElem(res_item); // 获取当前节点相对于父节点是第几个节点
                // 将选中的项目添加到弹出框对应的项中
                var res_config_items = res_item.getElementsByTagName("td");
                var config_info = {
                    XA_NAME: res_config_items[0].innerText,
                    XA_TYPE: res_config_items[1].innerText,
                    THIRD_PARTY_SYSTEM_ID: res_config_items[2].innerText,
                    XA_CONFIG: res_config_items[3].innerText,
                    REMARK: res_config_items[4].innerText
                };

                input_xa_name.value = config_info['XA_NAME'];
                input_xa_type_value.value = config_info['XA_TYPE'];
                config_dom = format_xa_config_dom_str(config_info['XA_TYPE'], res_config_items[3].innerText);
                input_xa_name.disabled = true;
                input_xa_type_value.disabled = true;
                i_selector.style.display = "none";
                input_system_id.disabled = true;
                input_system_id.value = config_info['THIRD_PARTY_SYSTEM_ID'];
                div_xa_config.innerHTML = config_dom;
                input_remark.value = config_info['REMARK'];
                toggleMask(1);
                break;

            case "btn btn-del":
                tbody_res_list.removeChild(target.parentNode.parentNode);
                break;
        }
    }
};

// 弹框的取消按钮
btn_cancal.onclick = function () {
    toggleMask(0);
    div_xa_type.style.display = "none";
    div_xa_config.innerHTML = "";
    i_selector.style.display = "inline-block";
};

// xa_type下拉选项的点击事件
i_selector.onclick = function () {
    var xa_type_elem = this.parentNode.nextElementSibling;
    if(xa_type_elem.style.display === "block"){
        xa_type_elem.style.display = "none";
    }
    else{
        xa_type_elem.style.display = "block";
    }
};

// 弹框的确认按钮
btn_ok.onclick = function () {
    var config_info = {
        XA_NAME: input_xa_name,
        XA_TYPE: input_xa_type_value,
        THIRD_PARTY_SYSTEM_ID: input_system_id,
        XA_CONFIG: div_xa_config,
        REMARK: input_remark
    };

    var config_inputs = config_info['XA_CONFIG'].getElementsByTagName("input");
    var values = [];
    var res_item_str = "";

    if (win_clk.flag === "ADD"){
        // 添加
        // 第一步：如果有必填项目没有填写，则不提交，并且将没有填写的项目设置为选中状态
        for (key in config_info){
            if (key === "XA_TYPE") {
                switch (config_info[key].value){
                    case "XA_KCXP":
                    case "XA_KDGP":
                    case "XA_MYSQL":
                    case "XA_ORACLE":
                        break;
                    default:
                        config_info[key].value = "";
                        config_info[key].focus();
                        alert("资源类型必须为：[XA_KCXP，XA_KDGP，XA_MYSQL，XA_ORACLE]");
                        return;
                }
            }

            if (key === "XA_CONFIG") {
                if (config_inputs.length === 0){
                    alert("请点击右侧下拉框资源类型");
                    config_info["XA_TYPE"].value = "";
                    i_selector.click();
                    return;
                }
                for (i=0; i<config_inputs.length; i++){
                    if(!config_inputs[i].value){
                        config_inputs[i].focus();
                        return ;
                    }
                }
                continue;
            }
            if (!config_info[key].value){
                config_info[key].focus();
                return ;
            }
        }
        for (i=0; i<config_inputs.length; i++)
            values.push(config_inputs[i].value);
        xa_config_str = '"' + values.join('","') + '"';
        res_item_str = '<tr><td>' + config_info['XA_NAME'].value + '</td><td>' + config_info['XA_TYPE'].value
            + '</td><td>' + config_info['THIRD_PARTY_SYSTEM_ID'].value + '</td><td class="config_str">' + xa_config_str
            + '</td><td class="remark_str">' + config_info['REMARK'].value + '</td><td>'
            + '<input class="btn btn-modi" type="button" value="修改">'
            + ' <input class="btn btn-del" type="button" value="删除"></td></tr>';

        res_item_node = parseDom(res_item_str);
        // tbody_res_list.innerHTML += res_item_str;
        tbody_res_list.appendChild(res_item_node[0]);
    }
    else{
        // 更改
        console.log(win_clk.index);
        res_index = win_clk.index;

        for (i=0; i<config_inputs.length; i++)
            values.push(config_inputs[i].value);
        xa_config_str = '"' + values.join('","') + '"';
        tbody_res_list.childNodes[res_index].innerHTML = '<td>' + config_info['XA_NAME'].value + '</td><td>'
            + config_info['XA_TYPE'].value + '</td><td>' + config_info['THIRD_PARTY_SYSTEM_ID'].value + '</td><td>'
            + xa_config_str + '</td><td>' + config_info['REMARK'].value + '</td><td>'
            + '<input class="btn btn-modi" type="button" value="修改">'
            + ' <input class="btn btn-del" type="button" value="删除"></td>';
    }
    i_selector.style.display = "inline-block";
    toggleMask(0);
};

btn_fresh.click();