<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
 <head> 
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /> 
  <title>编辑表格数据</title> 
  <style type="text/css">  
    <!--  
    body,div,p,ul,li,font,span,td,th{  
    font-size:10pt;  
    line-height:155%;  
    }  
    form{
    margin-left: 24%
    
    }
    table{  
    border-top-width: 1px;  
    border-right-width: 1px;  
    border-bottom-width: 0px;  
    border-left-width: 1px;  
    border-top-style: solid;  
    border-right-style: solid;  
    border-bottom-style: none;  
    border-left-style: solid;  
    border-top-color: #CCCCCC;  
    border-right-color: #CCCCCC;  
    border-bottom-color: #CCCCCC;  
    border-left-color: #CCCCCC;  
    }  
    td{  
    border-bottom-width: 1px;  
    border-bottom-style: solid;  
    border-bottom-color: #CCCCCC;  
    }  
    .EditCell_TextBox {  
    width: 90%;  
    border:1px solid #0099CC;  
    }  
    .EditCell_DropDownList {  
    width: 90%;  
    }  
    -->  
    </style> 
  
  <script src="GridFuction.js"></script> 
 </head> 
 <body> 
  <form id="form1" name="form1" method="post" action=""> 
  <h3>XA</h3> 
   <table width="698" border="0" cellpadding="0" cellspacing="0" id="XA_GRID"> 
    <tbody>
     <tr> 
      <td width="32" align="center" bgcolor="#EFEFEF" name="check"><input type="checkbox" name="checkbox" value="checkbox" /></td> 
      <td width="186" bgcolor="#EFEFEF" name="XA_NAME" edittype="TextBox">XA名称</td> 
      <td width="152" bgcolor="#EFEFEF" name="XA_TYPE" edittype="DropDownList" dataitems="{text:'KCXP',value:'KCXP'},{text:'MYSQL',value:'MYSQL'}">XA类型</td> 
      <td width="103" bgcolor="#EFEFEF" name="XA_CONFIG" edittype="TextBox">XA配置</td> 
      <td width="103" bgcolor="#EFEFEF" name="XA_FLAG" edittype="TextBox">XA标识</td> 
      <td width="103" bgcolor="#EFEFEF" name="REMARK" edittype="TextBox">备注</td> 
      
      <!--<td width="120" bgcolor="#EFEFEF" Name="SumMoney" Expression="Amount*Price" Format="#,###.00">合计</td> --> 
     </tr> 
     <tr> 
      <td align="center" bgcolor="#FFFFFF"><input type="checkbox" name="checkbox2" value="checkbox" /></td> 
      <td bgcolor="#FFFFFF">1</td> 
      <td bgcolor="#FFFFFF" >KCXP</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
     </tr> 
    </tbody>
   </table> 
   <br /> 
   <input type="button" name="Submit" value="新增" onclick="AddRow(document.getElementById('XA_GRID'),1)" /> 
   <input type="button" name="Submit2" value="删除" onclick="DeleteRow(document.getElementById('XA_GRID'),1)" /> 
   <input type="button" name="Submit22" value="重置" onclick="window.location.reload()" /> 
   <input type="button" value="提交" onclick="AddXa()" /> 
   <h3>功能字典</h3> 
   <table width="698" border="0" cellpadding="0" cellspacing="0" id="DICT_GRID"> 
    <tbody>
     <tr> 
      <td width="32" align="center" bgcolor="#EFEFEF" name="check"><input type="checkbox" name="checkbox" value="checkbox" /></td> 
      <td width="186" bgcolor="#EFEFEF" name="DICT_NAME" edittype="TextBox">字典名</td> 
      <td width="152" bgcolor="#EFEFEF" name="KEY_NAME" edittype="TextBox">KEY</td> 
      <td width="103" bgcolor="#EFEFEF" name="KEY_VALUE" edittype="TextBox">VALUE</td> 
      
      <!--<td width="120" bgcolor="#EFEFEF" Name="SumMoney" Expression="Amount*Price" Format="#,###.00">合计</td> --> 
     </tr> 
     <tr> 
      <td align="center" bgcolor="#FFFFFF"><input type="checkbox" name="checkbox2" value="checkbox" /></td> 
      <td bgcolor="#FFFFFF">1</td> 
      <td bgcolor="#FFFFFF" >0</td> 
      <td bgcolor="#FFFFFF">0</td> 
     </tr> 
    </tbody>
   </table> 
   <br /> 
   <input type="button" name="Submit" value="新增" onclick="AddRow(document.getElementById('DICT_GRID'),1)" /> 
   <input type="button" name="Submit2" value="删除" onclick="DeleteRow(document.getElementById('DICT_GRID'),1)" /> 
   <input type="button" name="Submit22" value="重置" onclick="window.location.reload()" /> 
   <input type="button" name="button" value="提交" onclick="AddGDict()" /> 
   <h3>服务配置</h3> 
   <table width="698" border="0" cellpadding="0" cellspacing="0" id="FUC_GRID"> 
    <tbody>
     <tr> 
      <td width="32" align="center" bgcolor="#EFEFEF" name="check"><input type="checkbox" name="checkbox" value="checkbox" /></td> 
      <td width="186" bgcolor="#EFEFEF" name="FUC_ID" edittype="TextBox">功能号</td> 
      <td width="152" bgcolor="#EFEFEF" name="PYTHON_FUN" edittype="TextBox">入口函数</td> 
      <td width="103" bgcolor="#EFEFEF" name="XA_NAME" edittype="TextBox">XA配置</td> 
      <td width="103" bgcolor="#EFEFEF" name="DST_FUC_ID" edittype="TextBox">目的功能</td> 
      <td width="103" bgcolor="#EFEFEF" name="IS_TRANS" edittype="TextBox">是否转换</td> 
      <td width="103" bgcolor="#EFEFEF" name="FUN_DESCRIBE" edittype="TextBox">备注</td> 
      <!--<td width="120" bgcolor="#EFEFEF" Name="SumMoney" Expression="Amount*Price" Format="#,###.00">合计</td> --> 
     </tr> 
     <tr> 
      <td align="center" bgcolor="#FFFFFF"><input type="checkbox" name="checkbox2" value="checkbox" /></td> 
      <td bgcolor="#FFFFFF">1</td> 
      <td bgcolor="#FFFFFF" >kinginx_comm_callxp_biz</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      
     </tr> 
    </tbody>
   </table> 
   <br /> 
   <input type="button" name="Submit" value="新增" onclick="AddRow(document.getElementById('FUC_GRID'),1)" /> 
   <input type="button" name="Submit2" value="删除" onclick="DeleteRow(document.getElementById('FUC_GRID'),1)" /> 
   <input type="button" name="Submit22" value="重置" onclick="window.location.reload()" /> 
   <input type="button" name="Submit3" value="提交" onclick="AddGFuc()" /> 
   <h3>参数转换</h3> 
   <table width="698" border="0" cellpadding="0" cellspacing="0" id="PARA_TARNS_GRID"> 
    <tbody>
     <tr> 
      <td width="32" align="center" bgcolor="#EFEFEF" name="check"><input type="checkbox" name="checkbox" value="checkbox" /></td> 
      <td width="186" bgcolor="#EFEFEF" name="FUC_ID" edittype="TextBox">功能号</td> 
      <td width="152" bgcolor="#EFEFEF" name="TRANS_TYPE" edittype="DropDownList" dataitems="{text:'req',value:0},{text:'ans',value:1}">参数类型</td> 
      <td width="103" bgcolor="#EFEFEF" name="PARA_ID" edittype="TextBox">参数序号</td> 
      <td width="103" bgcolor="#EFEFEF" name="KEY_NAME" edittype="TextBox">参数名</td> 
      <td width="103" bgcolor="#EFEFEF" name="KEY_VALUE" edittype="TextBox">目的值</td> 
      <td width="103" bgcolor="#EFEFEF" name="DICT" edittype="TextBox">字典</td> 
      <td width="103" bgcolor="#EFEFEF" name="DEFAULT_VALUE" edittype="TextBox">缺省值</td> 
      <td width="150" bgcolor="#EFEFEF" name="TRANS_FUC_NAME" edittype="TextBox">转换函数</td> 
      <td width="206" bgcolor="#EFEFEF" name="TRANS_FUC_PARA_LIST" edittype="TextBox">转换函数参数</td> 
      <td width="103" bgcolor="#EFEFEF" name="COMMENT" edittype="TextBox">备注</td> 
      <!--<td width="120" bgcolor="#EFEFEF" Name="SumMoney" Expression="Amount*Price" Format="#,###.00">合计</td> --> 
     </tr> 
     <tr> 
      <td align="center" bgcolor="#FFFFFF"><input type="checkbox" name="checkbox2" value="checkbox" /></td> 
      <td bgcolor="#FFFFFF">1</td> 
      <td bgcolor="#FFFFFF" value=0>req</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
      <td bgcolor="#FFFFFF">0</td> 
     </tr> 
    </tbody>
   </table> 
   <br /> 
   <input type="button" name="Submit" value="新增" onclick="AddRow(document.getElementById('PARA_TARNS_GRID'),1)" /> 
   <input type="button" name="Submit2" value="删除" onclick="DeleteRow(document.getElementById('PARA_TARNS_GRID'),1)" /> 
   <input type="button" name="Submit22" value="重置" onclick="window.location.reload()" /> 
   <input type="button" name="Submit3" value="提交" onclick="AddFucTrans()" /> 
   <h3>发布功能</h3> 
   <table width="698" border="0" cellpadding="0" cellspacing="0" id="PUBLISH_GRID"> 
    <tbody>
     <tr> 
      <td width="32" align="center" bgcolor="#EFEFEF" name="check"><input type="checkbox" name="checkbox" value="checkbox" /></td> 
      <td width="152" bgcolor="#EFEFEF" name="BIZ_TYPE" edittype="TextBox" >功能类型</td> 
      <td width="186" bgcolor="#EFEFEF" name="FUC_ID" edittype="TextBox">功能名称</td> 
      <td width="103" bgcolor="#EFEFEF" name="DESCRIBE" edittype="TextBox">功能描述</td> 
      
      <!--<td width="120" bgcolor="#EFEFEF" Name="SumMoney" Expression="Amount*Price" Format="#,###.00">合计</td> --> 
     </tr> 
     <tr> 
      <td align="center" bgcolor="#FFFFFF"><input type="checkbox" name="checkbox2" value="checkbox" /></td> 
      <td bgcolor="#FFFFFF">XA</td> 
      <td bgcolor="#FFFFFF" >KCXP_XA</td> 
      <td bgcolor="#FFFFFF">xp转换功能</td> 
       
     </tr> 
      <tr> 
      <td align="center" bgcolor="#FFFFFF"><input type="checkbox" name="checkbox2" value="checkbox" /></td> 
      <td bgcolor="#FFFFFF">fuction</td> 
      <td bgcolor="#FFFFFF" >900300</td> 
      <td bgcolor="#FFFFFF">基础业务</td> 
       
     </tr> 
    </tbody>
   </table> 
   <br /> 
   <input type="button" name="Submit" value="一键发布" onclick="PubLish()" /> 
   <input type="button" name="Submit2" value="删除" onclick="" /> 
   
   
  </form> 
  <script language="javascript">  
    //var tabProduct = document.getElementById("tabProduct");  
      
    // 设置表格可编辑  
    // 可一次设置多个，例如：EditTables(tb1,tb2,tb2,......)  
    EditTables(XA_GRID,DICT_GRID,FUC_GRID,PARA_TARNS_GRID,PUBLISH_GRID);  
    var a = GetTableData(XA_GRID);
    //console.log(a);
      
    </script>   
 </body>
</html>