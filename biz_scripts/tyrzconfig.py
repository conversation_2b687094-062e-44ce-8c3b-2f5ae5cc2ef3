# -*- coding: utf-8 -*-
import redis
import kwlpyfunction
import tyrzTccInfo
from rediscluster import StrictRedisCluster
import datetime

# 新客购买报价回购产品状态 查询url
BJHG_CHECK_STATUS_URL = 'http://172.16.25.45/mall/api/CRH-MALLU062.json'

#生成票据需要额外产生来自扩展信息表
g_session_extension_info = ['PHONE','IDCARD','sysnode_id']

#需要记录客户签约、解约操作的渠道,陆金所渠道
g_sign_and_unsign_channel = ['15900']

#各渠道TCC(柜台留痕)存储到登录票据中的格式

g_session_extension_TCC = {
    'Q': #全户通渠道券商12900 生成登录票据时会存TCC_PHONE和TCC_INFO两个key到票据json里
        {
            '12900':{#华林
                'TCC_PHONE':tyrzTccInfo.Q12900MakeTccPhone,
                'TCC_INFO':tyrzTccInfo.Q12900MakeTccInfo
            },
            '19000':{#恒泰
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            },
            '12500':{#长江
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            },
            '11600':{#平安
                'TCC_INFO':tyrzTccInfo.Q11600MakeTccInfo,   #设备标识串
                'TCC_DEVICEINFO':tyrzTccInfo.Q11600MakeDeviceInfo,    #手机设备信息
                'TCC_PHONEINFO':tyrzTccInfo.Q11600MakePhoneInfo   #手机操作系统信息
            },
            '12600':{#国元
                'TCC_INFO':tyrzTccInfo.Q11600MakeTccInfo,   #设备标识串
                'TCC_DEVICEINFO':tyrzTccInfo.Q11600MakeDeviceInfo,    #手机设备信息
                'TCC_PHONEINFO':tyrzTccInfo.Q11600MakePhoneInfo   #手机操作系统信息
            }

        },
    '2': #自选股生成登录票据时会存TCC_PHONE和TCC_INFO两个key到票据json里
        {
            '19900':{ #中山
                'TCC_PHONE':tyrzTccInfo.Q12900MakeTccPhone,
                'TCC_INFO':tyrzTccInfo.Q12900MakeTccInfo
            },
             '22300':{#联讯
                'TCC_PHONE':tyrzTccInfo.Q12900MakeTccPhone,
                'TCC_INFO':tyrzTccInfo.Q12900MakeTccInfo
            },
            '15900':{#国金
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            },
            '16300':{#湘财
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            },
            '19000':{#恒泰
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            }

        },
    '82': #牛菲特
        {
            '12500':{#长江
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            }
        },
    '84': #黑石科技
        {
            '12500':{#长江
                'TCC_INFO':tyrzTccInfo.Q19000MakeTccInfo
            }
        }
}


#这里配置统一认证数据库(客户签约信息)
g_TyrzDbDict ={
    'host':'**************',
    'user':'jwl',
    'passwd':'Password',
    'db':'kbss_kcas',
    'port':3306,
    'charset':'utf8'
    }

#这里配置统一认证report数据库(存放公告信息)
g_TyrzReportDbDict ={
    'host':'**************',
    'user':'jwl',
    'passwd':'Password',
    'db':'kbss_kcas',
    'port':3306,
    'charset':'utf8'
    }

g_redis_expiretime = 3600*8 #续票据时间

# 中登创业板签署日期和类型、T+A类型的有效时间，单位：秒
g_SignDate_time = 60*60*8

HENGSHENG_BRANCH_DICT_EXPIRE = 7 * 24 * 60 * 60
HENGSHENG_BRANCH_DICT_KEY = 'hengsheng_branch_dict'

#redis双机房数据同步开关 True开 False关
g_redis_publish = True

# redis-cluster集群节点地址信息
redis_nodes = [
               {'host':'**************','port':7000},
               {'host':'**************','port':7001},
               {'host':'**************','port':7002},
               {'host':'**************','port':7003},
               {'host':'**************','port':7004},
               {'host':'**************','port':7005}
              ]
# 集群的redis密码必须一致
redis_password = 'Password'

# op_station 的AES key
g_op_aes_key = 'g6j0z0q1w0e9bapp'

# session对换国金网厅token的配置 url
g_GJ_token_config = {
    'global_url':'http://************:30000/yjbapi/sas/session/register',
    'simple_url':'http://************:30000/yjbapi/sas/instant_token/apply',
    'app_id':'qqstack',
    'account_type':'1',
}

# 控制环境配置 'prod': 生产环境，测试接口失效； 'test': 测试环境，测试接口生效
g_environment = 'test'

def redis_cluster():
    global redis_nodes
    try:
        redisconn = StrictRedisCluster(startup_nodes=redis_nodes, password=redis_password)
    except Exception as e:
        raise Exception(repr(e))
    return redisconn

    raise Exception('内部错误,redis_cluster连接异常')

g_redisconn = redis_cluster()

#从redis取
def GetRedisKey(key):
    try:
        return g_redisconn.get(key)
    except Exception as e:
        raise Exception(repr(e))

#redis存值
def SetRedisKey(key,value):
    try:
        g_redisconn.setex(key, value)
    except Exception as e:
        #抛出异常
        raise Exception(repr(e))

#redis存值且设置生命周期
def SetRedisKeyEx(key,value,expire):
    try:
        g_redisconn.setex(key, expire, value)
        if g_redis_publish:
            data = key + '?' + str(value) + '?' + str(expire)
            #发布需要同步的data
            g_redisconn.publish("SetRedisKeyEx", data)
    except Exception as e:
        #抛出异常
        raise Exception(repr(e))

#redis存值且设置生命周期
def ExpireRedisKey(key,expire):
    try:
        g_redisconn.expire(key,expire)
        if g_redis_publish:
            data = key + '?' + str(expire)
            # 发布需要同步的data
            g_redisconn.publish("ExpireRedisKey", data)
    except Exception as e:
        #抛出异常
        raise Exception(repr(e))

#redis删除缓存
def DeleteRedisKey(key):
    try:
        g_redisconn.delete(key)
        if g_redis_publish:
            # 发布需要同步删除的key
            g_redisconn.publish("DeleteRedisKey", key)
    except Exception as e:
        #抛出异常
        raise Exception(repr(e))

if __name__=='__main__':
    # TIMESTAMP1 = datetime.datetime.now().strftime('%Y%m%d%H:%M:%S.%f')[8:-5]
    # i = 100000
    # while i > 0:
    #     a = GetRedisKey('cluster_key')
    #     i = i - 1
    #
    # TIMESTAMP2 = datetime.datetime.now().strftime('%Y%m%d%H:%M:%S.%f')[8:-5]
    # print(TIMESTAMP1)
    # print(TIMESTAMP2)

    print(GetRedisKey('7000'))

    # ExpireRedisKey('cluster_key', 10)

    # DeleteRedisKey('cluster_key')
