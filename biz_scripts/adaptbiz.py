# -*- coding: utf-8 -*-
import copy
import json
import traceback
import time
import asyncio
import datetime
from asyncio import *
from json.decoder import JSONDecodeError

import operator
import KwlTools
from adaptconfig import *
from kwlpyfunction import  *
from KWL import  *
from tyrzconfig import GetRedisKey, SetRedisKeyEx, g_SignDate_time, g_environment, HENGSHENG_BRANCH_DICT_EXPIRE, \
    HENGSHENG_BRANCH_DICT_KEY, BJHG_CHECK_STATUS_URL, g_redis_expiretime, g_GJ_token_config
# from msgmodel import GetRedisKeyEx, SetRedisKeyEx, g_SignDate_time
from kowbiz import MakeTecentCheckCode
from tyrzBiz import queryPriorMarket, setPriorMarket
from CHST2Connector import *
from kwl_py_log import *
from common_config import g_decrypt_param, STOCK_CODE, AGREE_MODEL_NO


if isLinuxSystem():
    from KWL_Nginx import *
else:
    def GetKWLParam(sKey):
        return '99999'
    def IsNginxOn():
        return True

g_TransErrCodeMsg = KwlTools.TransErrCodeMsg('ErrorInfomation.xml')


def decrypeLogMsg(jsondstreq):
    for param in g_decrypt_param:
        if param in jsondstreq:
            jsondstreq[param] = '******'
    return json.dumps(jsondstreq,ensure_ascii=False)


async def send_guojin(ref_request, jsondstreq, dstserviceid):
    strjsonreq = json.dumps(jsondstreq, ensure_ascii=False)
    deWriteLogMsg = decrypeLogMsg(jsondstreq)
    kwl_py_write_log('调柜台%s的请求参数:%s' % (dstserviceid, deWriteLogMsg), 'adaptcommbiz', ZTLOG_INFO, msgid=ref_request)
    ans = await get_CHST_instant().send_request(int(dstserviceid), strjsonreq)
    kwl_py_write_log('调柜台%s的应答:%s' % (dstserviceid, ans), 'adaptcommbiz', ZTLOG_INFO, msgid=ref_request)
    result = json.loads(ans)
    if 'ANS_COMM_DATA' not in result['ANSWERS'][0]:
        result['ANSWERS'][0]['ANS_COMM_DATA'] = []
    return result

async def branch(ref_request,jsonreq):
    branch_dict = redis_get_json2dict(HENGSHENG_BRANCH_DICT_KEY)
    if not branch_dict:
        branch_dict = await query_branch_dict_for_330011(ref_request,jsonreq)
        SetRedisKeyEx(HENGSHENG_BRANCH_DICT_KEY, json.dumps(branch_dict, ensure_ascii=False), HENGSHENG_BRANCH_DICT_EXPIRE)
    return branch_dict


async def query_branch_dict_for_330011(ref_request,jsonreq):
    jsondstreq = {
        "op_branch_no": "5010",
        "op_entrust_way": "h",
        "op_station": tranOpStation([jsonreq.get('TCC')]),
        "branch_no": '0',
    }
    res = await send_guojin(ref_request, jsondstreq, '330011')
    return {branch['branch_no']: branch['branch_name'] for branch in res['ANSWERS'][0]['ANS_COMM_DATA']}


def redis_get_json2dict(hengsheng_branch_dict):
    branch_json = GetRedisKey(hengsheng_branch_dict)
    return json.loads(branch_json.decode("utf-8")) if branch_json else None


async def adapt600030_new(ref_request,serviceid,jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request,serviceid,jsonreq)
    if code == '0' and (not rows[0].get('BRANCH_NAME')):
        branch_dict = await branch(ref_request,jsonreq)
        branch_name = branch_dict.get(rows[0]['BRANCH_CODE'])
        if not branch_name: # 这里为空的话，就是redis查不到，再调一下接口，同步一下字典
            branch_dict = await query_branch_dict_for_330011(ref_request, jsonreq)
            SetRedisKeyEx(HENGSHENG_BRANCH_DICT_KEY, json.dumps(branch_dict, ensure_ascii=False),
                          HENGSHENG_BRANCH_DICT_EXPIRE)
            branch_name = branch_dict.get(rows[0]['BRANCH_CODE'])
        rows[0]['BRANCH_NAME'] = branch_name
    return code, msg, rows


async def adapt600410(ref_request,serviceid,jsonreq):
    sub_jsonreq = copy.deepcopy(jsonreq)
    result_rows = []
    code = -1
    msg = ''
    for s_code in STOCK_CODE.split(','):
        jsonreq['stock_code'] = s_code
        code, msg, rows = await adaptcommbiz(ref_request, serviceid, jsonreq)
        if code != '0':
            return code, msg, rows
        sub_jsonreq_tmp = copy.deepcopy(sub_jsonreq)
        sub_jsonreq_tmp['stock_code'] = s_code
        ret_set = await adaptcommbiz(ref_request, '600410-330300', sub_jsonreq_tmp)
        if code != '0':
            return ret_set[0], ret_set[1], ret_set[2]
        row = rows[0]
        row['REPURCHASE_START_AMT'] = ret_set[2][0]['REPURCHASE_START_AMT']
        result_rows.append(row)
    return code, msg, result_rows


async def adapt900010(ref_request,serviceid,jsonreq):
    result_rows = []
    code = -1
    msg = ''
    code, msg, rows = await adaptcommbiz(ref_request, serviceid, jsonreq)

    return code, msg, result_rows



async def adapt600173(ref_request,serviceid,jsonreq):
    # order_type = jsonreq.get("ORDER_TYPE") or dict_order_type.get("提前购回")
    # if str(order_type) != dict_order_type.get("提前购回"):
    #     return '30011', "ORDER_TYPE参数值不合法", []
    jsonreq["ORDER_TYPE"] = dict_order_type.get("提前购回")
    code, msg, rows = await adaptcommbiz(ref_request,serviceid,jsonreq)
    if code == '0' and rows:
        rows = list(filter(lambda x: x.get('ORDER_DATE') not in ('0', '', None)
                           and x['BALANCE_CODE'] in STOCK_CODE.split(',')
                           and x.get('entrust_type') != '2', rows))
    return code, msg, rows

async def query_next_trade_date(ref_request,jsonreq, serviceid='330016'):
    """
    查询下一个交易日
    :param ref_request: 请求流水号
    :param jsonreq: dict类型 必传TCC，可选init_date
    :param serviceid: 柜台功能号
    :return: 下一交易日
    """
    code, msg, rows = await adaptcommbiz(ref_request, serviceid, jsonreq)
    return code, msg, rows


async def query_history_order_339601(ref_request, jsonreq, serviceid='339601'):
    """
    查询报价回购历史订单
    :param ref_request:
    :param jsonreq:
    :param serviceid:
    :return:
    """
    code, msg, rows = await adaptcommbiz(ref_request, serviceid, jsonreq)
    return code, msg, rows

async def adapt600171_332300(data_list, ref_request, jsonreq, serviceid='600171_332300'):
    """
    查询报价回购历史订单
  （1）332300请求入参unfinished_type=E、stock_type=Z、stock_code等于报价回产品代码，出参取date_back小于等于当天的数据；
  （2）根据以上符合要求数据的entrust_date和entrust_no，通过332620获取对应的原始委托单，出参回购金额back_balance强置成entrust_balance，其他信息同现在模式
    :param ref_request:
    :param jsonreq:
    :param serviceid:
    :return:
    """
    request_body = copy.deepcopy(jsonreq)
    request_body['unfinished_type'] = 'E'
    request_body['stock_type'] = 'Z'
    request_body['stock_code'] = STOCK_CODE

    code, msg, rows = await adaptcommbiz(ref_request, serviceid, request_body)
    today = datetime.datetime.now()
    # 数据过滤 只要小于等于今天的数据
    rows = list(filter(lambda x: x.get('date_back') not in ('0', '', None)
                                   and today >= datetime.datetime.strptime(x.get('date_back'), '%Y%m%d'), rows))
    if code != '0':
        return []

    # 数据分组
    order_id_dict = {r['entrust_date'] + r['entrust_no']: r for r in rows}

    # 其余的332300的数据查一遍332620
    result = await _query_332620_by_332300(order_id_dict, jsonreq, ref_request, )
    data_list.extend(result)
    return data_list

def _is_none_data_171(data_list):
    return data_list and data_list[0].get('ORDER_DATE') in ('0', '', None)

def _filter_by_ORDER_DATE_171(data_list, jsonreq):
    if not jsonreq.get('ORDER_DATE'):
        return data_list
    return [r for r in data_list if r['ORDER_DATE'] == jsonreq['ORDER_DATE']]

async def _set_EXPIRE_DATE_171(data_list, ref_request, tcc):
    for data in data_list:
        date_back = data['date_back']
        log_info('订单%s修改EXPIRE_DATE:%s date_back:%s '
                 % (data['ORDER_ID'], data['EXPIRE_DATE'], date_back), msgid=ref_request)
        if data['EXPIRE_DATE'] != '0':
            continue
        if date_back in ['0', None, '']:
            continue
        date_back_obj = datetime.datetime.strptime(date_back, '%Y%m%d')
        # 获取date_back的前一天
        init_date = (date_back_obj - datetime.timedelta(days=1)).strftime('%Y%m%d')
        code, msg, date_rows = await query_next_trade_date(ref_request, dict(TCC=tcc, init_date=init_date))
        log_info('订单%s修改EXPIRE_DATE:%s 下一交易日date_rows:%s'
                 % (data['ORDER_ID'], data['EXPIRE_DATE'], date_rows), msgid=ref_request)

        if code != '0':
            log_error("_set_EXPIRE_DATE_171 查询日期出错%s" % msg)
            continue
        # 如果date_back和the_date_back_before都不是交易日
        # （或者date_back不是交易日而the_date_back_before是交易日）那么他们的下一个交易日都是相同的，查一次就行
        # 如果date_back是交易日，那本身取这个值也没错
        data['EXPIRE_DATE'] = date_rows[0]['next_trade_date']
        log_info('订单%s已修改EXPIRE_DATE:%s 下一交易日date_rows:%s'
                 % (data['ORDER_ID'], data['EXPIRE_DATE'], date_rows), msgid=ref_request)

    return data_list



async def _query_332620_by_332300(order_id_dict, jsonreq, ref_request, ):
    """
    通过订单号查询详细信息，特殊设置REPURCHASE_AMT
    :param order_id_dict:
    :param jsonreq:
    :param ref_request:
    :return:
    """
    result = []
    for _k, _v in order_id_dict.items():
        request_body = copy.deepcopy(jsonreq)
        request_body['entrust_no'] = _v['entrust_no']
        request_body['start_date'] = _v['entrust_date']
        request_body['end_date'] = _v['entrust_date']

        code4, msg4, rows4 = await adapt600171_332620(ref_request, request_body)
        if rows4:
            result.append(rows4[0])
    return result

def _filter_332620(result:list, data_list):
    extend_rows = []
    for row1 in data_list:
        if row1['entrust_prop'] == 'QCA':
            continue
        if row1['entrust_status'] in ['0', '1', '2', 'C', 'Z']:
            extend_rows.append(row1)
        for row in result:
            # 如果是当天的订单，那么ORDER_TIME就是订单时间时间
            if row['ORDER_ID'] == row1['REDEEM_ID']:
                row['ORDER_TIME'] = row1['ORDER_TIME']
    result.extend(extend_rows)
    return result

async def adapt600171_only332620(ref_request,serviceid,jsonreq):
    jsonreq['query_type'] = '3'  # 查全部
    if jsonreq.get('ORDER_DATE'):
        jsonreq['start_date'] = jsonreq.get('ORDER_DATE')
        jsonreq['end_date'] = jsonreq.get('ORDER_DATE')

    code, msg, result = await adapt600171_332620(ref_request, jsonreq)
    result = await _set_EXPIRE_DATE_171(result, ref_request, jsonreq.get('TCC'))
    for r in result:
        code, msg, date_rows = await query_next_trade_date(ref_request, dict(init_date=r['EXPIRE_DATE'], TCC=jsonreq.get('TCC')))
        if code != '0':
            continue
        EXPIRE_DATE_n = datetime.datetime.strptime(date_rows[0]['next_trade_date'], "%Y%m%d")
        code, msg, date_rows = await query_next_trade_date(ref_request, dict(init_date=r['ORDER_DATE'], TCC=jsonreq.get('TCC')))
        if code != '0':
            continue
        ORDER_DATE_n = datetime.datetime.strptime(date_rows[0]['next_trade_date'], "%Y%m%d")
        date_difference = EXPIRE_DATE_n - ORDER_DATE_n
        r['TRADE_DAYS'] = str(date_difference.days)
    return  code, msg, result

async def adapt600171(ref_request,serviceid,jsonreq):
    try:
        jsonreq['stock_code'] = STOCK_CODE
        # 查持仓
        code, msg, result = await adaptcommbiz(ref_request,serviceid,jsonreq)
        if _is_none_data_171(result):
            result = []
        if code != '0':
            return code, msg, result

        jsonreq['query_type'] = '3' # 查全部
        # 查今日未报
        code1, msg1, rows1 = await adapt600171_332620(ref_request,jsonreq)
        if code1 != '0':
            return code, msg, result
        result = _filter_332620(result, rows1)

        result = await adapt600171_332300(result, ref_request, jsonreq)
        result = await _set_EXPIRE_DATE_171(result, ref_request, jsonreq['TCC'])
        result = _filter_by_ORDER_DATE_171(result, jsonreq)
        return code, msg, result
    except:
        log_error("adapt600171 内部错误%s" % (traceback.format_exc()))
        return -1, "内部错误", []

async def adapt600171_332620(ref_request,jsonreq, serviceid='600171_332620'):
    code, msg, rows = await adaptcommbiz(ref_request, serviceid, jsonreq)
    rows = list(filter(lambda x: x.get('ORDER_DATE') not in ('0', '', None)
                                 and x['entrust_prop'] != 'QCA'
                                 and x['BALANCE_CODE'] in STOCK_CODE.split(',')
                                 and x['entrust_type'] != '2', rows))
    return code, msg, rows

async def adaptcommbiz(ref_request,serviceid,jsonreq):
    jsondstreq = {}
    try:
        code,msg,dstserviceid = translateReq(serviceid,jsonreq,jsondstreq)
        if code != 0:
            return '-30011','adapt功能%s入参转换失败:%s'%(serviceid,msg),[]
    except Exception as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'adaptcommbiz', ZTLOG_ERROR, msgid='')
        return '-30012','adapt功能%s入参转换失败:%s'%(serviceid,repr(e)),[]

    if serviceid == '600140' \
       and (jsonreq['TRD_ID'] == '0B' or jsonreq['TRD_ID'] == '0S'):
        jsondstreq['entrust_prop'] = '0'

    valid_service_ids = {'600010', '600001', '600140', '600141', '410301'}

    if serviceid in valid_service_ids:
        if 'op_station' in jsondstreq and jsondstreq['op_station'] != '':
            tcc = jsondstreq['op_station']
            # 处理 LIP 字段，替换特定 IP 为 NA
            tcc = tcc.replace('LIP=127.0.0.1', 'LIP=NA')
            tcc = tcc.replace('LIP=0.0.0.0', 'LIP=NA')

            # 处理 MAC 字段，替换特定值为 NA
            tcc = tcc.replace('MAC=020000000000', 'MAC=NA')
            tcc = tcc.replace('MAC=020000000001', 'MAC=NA')
            tcc = tcc.replace('MAC=020000000002', 'MAC=NA')

            # 更新 jsonreq 的 TCC 字段
            jsondstreq['op_station'] = tcc

    strjsonreq = json.dumps(jsondstreq,ensure_ascii=False)
    deWriteLogMsg = decrypeLogMsg(jsondstreq)
    kwl_py_write_log('调柜台%s的请求参数:%s'%(dstserviceid,deWriteLogMsg), 'adaptcommbiz', ZTLOG_INFO, msgid=ref_request)
    ans = await get_CHST_instant().send_request(int(dstserviceid), strjsonreq)
    kwl_py_write_log('调柜台%s的应答:%s' % (dstserviceid, ans), 'adaptcommbiz', ZTLOG_INFO, msgid=ref_request)
    try:
        ansdict = json.loads(ans)
    except JSONDecodeError as e:
        ansdict = json.loads(ans.replace('\\', '\\\\'))
    if 'ANS_COMM_DATA' not in ansdict['ANSWERS'][0]:
        ansdict['ANSWERS'][0]['ANS_COMM_DATA'] = []
    dstlistRows = []
    try:
        code,msg = translateAns(serviceid,jsondstreq,ansdict['ANSWERS'][0]['ANS_COMM_DATA'],dstlistRows)
        if code != 0:
            return '-30014','adapt功能%s出参转换失败:%s'%(serviceid,msg),[]
    except Exception as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'adaptcommbiz', ZTLOG_ERROR, msgid='')
        return '-30015','adapt功能%s出参转换失败:%s'%(serviceid,repr(e)),[]

    transdErrInfo = g_TransErrCodeMsg.SelectErrorCodeAndMessage(serviceid,
                                                                ansdict['ANSWERS'][0]['ANS_MSG_HDR']['MSG_CODE'],
                                                                ansdict['ANSWERS'][0]['ANS_MSG_HDR']['MSG_TEXT'])
    return transdErrInfo.code, transdErrInfo.msg, dstlistRows


#交易适配器 410301 组合业务
#根据华林段工要求，目前权限开通流程如下
#1.先以operway--5,调用410301，校验客户身份，同时获取orgid字段（410323、410324接口入参需要）
#2.调用410323和410324增加权限e
#3.再次以operway---e，调用410301，确保操作方式e开通成功。
async def adapt410301(ref_request,serviceid,jsonreq):
    jsonreq['operway'] = '5'
    '''首先调用410301A,以委托方式5登录，确保能登录成功，然后取回orgid等字段'''
    code,msg,rows = await adaptcommbiz(ref_request,'410301A',jsonreq)
    if code != '0':
        return code,msg,rows
    Orgid = ''
    for eachrow in rows :
        Orgid = eachrow.get('orgid', '')

    jsonreq['custorgid'] = Orgid
    jsonreq['orgid'] = Orgid
    code,msg,rows = await adaptcommbiz(ref_request,'410324',jsonreq)
    if code != '0':
        return code,msg,rows
    operway = ''
    for eachrow in rows :
        operway = eachrow.get('operway', '')    #0135e
    if 'e' not in operway :
        jsonreq['newoperway'] = operway + 'e'   # 0135 + e
        jsonreq['fundid'] = jsonreq['inputid']
        jsonreq['custid'] = jsonreq['inputid']
        code,msg,rows = await adaptcommbiz(ref_request,'410323',jsonreq)
        if code != '0':
            return code,msg,rows

    '''然后调用410301A业务,以委托方式e登录,即原来的登录业务,现在变成辅助业务了'''
    jsonreq['operway'] = 'e'
    code,msg,rows = await adaptcommbiz(ref_request,'410301A',jsonreq)
    if code != '0':
        return code,msg,rows

    return code,msg,rows

'''    600040查询客户资料    '''
async def adapt600040(ref_request,serviceid,jsonreq):
    if 'ACCOUNT' in jsonreq:
        jsonreq['USER_CODE'] = jsonreq['ACCOUNT']
    code, msg, rowsB = await adaptcommbiz(ref_request, '600040B', jsonreq)
    if code != '0':
        return code, msg, rowsB
    if rowsB == []:     #没有查询数据直接返回
        return 100, msg, rowsB
    for eachrow in rowsB:
        if 'ID_TYPE' in eachrow:
            jsonreq['ID_TYPE'] = eachrow['ID_TYPE']
        if 'ID_CODE' in eachrow:
            jsonreq['ID_CODE'] = eachrow['ID_CODE']

    #根据证件号码查询客户基本信息
    code, msg, rows = await adaptcommbiz(ref_request, '600040A', jsonreq)
    if code != '0':
        return code, msg, rows
    for row in rows:
        for eachrow in rowsB:
            if 'ID_TYPE' in eachrow:
                row['ID_TYPE'] = eachrow['ID_TYPE']
            if 'ID_CODE' in eachrow:
                row['ID_CODE'] = eachrow['ID_CODE']
            if 'OPEN_DATE' in eachrow:
                row['ACCOUNT_DATE'] = eachrow['OPEN_DATE']
            if 'ID_BEG_DATE' in eachrow:
                row['ID_BEG_DATE'] = eachrow['ID_BEG_DATE']
            if 'ID_EXP_DATE' in eachrow:
                row['ID_EXP_DATE'] = eachrow['ID_EXP_DATE']
            row['ACCOUNT'] = jsonreq['ACCOUNT']
    ans = [{}]
    ans[0] = rows[0]

    #查询客户信息（职业类型）
    code, msg, rows = await adaptcommbiz(ref_request, '160001', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        if 'OCCU_TYPE' in eachrow:
            ans[0]['OCCU_TYPE'] = eachrow['OCCU_TYPE']

    return '0', 'success', ans

#600041查询客户风险测评级别
async def adapt600041(ref_request,serviceid,jsonreq):
    code,msg,rows = await adaptcommbiz(ref_request,'600041',jsonreq)
    if code != '0':
        return code,'调用600041查询客户风险测评级别失败:' + msg, rows

    todaydate = time.strftime('%Y%m%d', time.localtime())
    for row in rows:
        if int(row['corp_end_date']) >= int(todaydate):
            row['RISK_EXPIRED'] = '0'  # 未过期
        else:
            row['RISK_EXPIRED'] = '1'  # 已过期

        if row['corp_risk_level'] == '0':
            row['RISK_EXPIRED'] = '3'  # 未评测

        if row['corp_risk_level'] == '1' and row['min_rank_flag'] == '1':
            row['RISK_TYPE'] = '5'  # 未评测

    return '0', 'success', rows

#ST 风险警示权限查询接口
async def adapt600042(ref_request,serviceid,jsonreq):
    code,msg,rows = await adaptcommbiz(ref_request,'600042',jsonreq)
    if code != '0':
        return code, msg, rows
    ans = [{"STATUS": "0", "SZ_ST_STATUS": "0"}]
    for row in rows:
        # 主股东账户
        if row['MAIN_FLAG'] == '1':
            # 上海
            if row['MARKET'] == '1':
                # 有st权限
                if 'w' in row['holder_rights']:
                    ans[0]['STATUS'] = '1'
            # 深圳
            elif row['MARKET'] == '0':
                # 有st权限
                if 'w' in row['holder_rights']:
                    ans[0]['SZ_ST_STATUS'] = '1'
    return code, msg, ans


#600043更新风险测评
async def adapt600043(ref_request,serviceid,jsonreq):
    code,msg,rows = await adaptcommbiz(ref_request,'600043',jsonreq)
    if code != '0':
        return code, msg, rows

    return '0', '更新风险测评成功', []

#向中登发起创业板转签
async def adapt600046(ref_request,serviceid,jsonreq):
    #part1. 获取签署协议必须的入参 ：证件类型、证件号码等
    jsonreq['ANS_STATUS'] = '1'
    code,msg,rows = await adaptcommbiz(ref_request,'99000265',jsonreq)
    if code != '0':
        return '-60004600', '中登创业板协议签署处理失败' + msg, []
    CustCode = ''
    IdType = ''
    IdCode = ''
    TrdAcct = ''
    StkBd = ''
    for row in rows :
        if row['STKBD'] == '00' :   #只用深A的股东卡
            CustCode = row['CUST_CODE']
            IdType = row['ID_TYPE']
            IdCode = row['ID_CODE']
            TrdAcct = row['TRDACCT']
            StkBd = row['STKBD']

    #part2: 连接Redis数据库, 获取入参 ： 签署日期 | 开通类型
    redisvalue = GetRedisKey(jsonreq['USER_ID'])
    if redisvalue == None :
        return '-60004601', '中登创业板协议签署处理失败,存储的签署日期和类型已过期,请重新查询转签权限获取相关参数', []
    if redisvalue == b'' :
        return '-60004602', '中登创业板协议签署处理失败,于redis获取签署日期和类型失败', []
    redisvalue = redisvalue.decode('utf-8')
    listdate = redisvalue.split('|')
    listdate.append('')
    listdate.append('')
    listdate.append('')
    # listdate[0]签署日期strSignDate  listdate[1]开通类型strSignCls  [2]生效日期strEftDate  [3]开通类型strOpenType  [4]开通地点strSignPlace
    code,msg,rows = await CalcuDataTime(ref_request, listdate, jsonreq['USER_ID'])
    if code != '0':
        return code, '获取生效日期出错 ' + msg, []

    #part4: 调用账户业务发起创业板转签业务
    jsonreq['SIGN_DATE'] = listdate[0]
    jsonreq['SIGN_CLS'] = listdate[1]
    jsonreq['CUST_CODE'] = CustCode
    jsonreq['ID_TYPE'] = IdType
    jsonreq['ID_CODE'] = IdCode
    jsonreq['EFT_DATE'] = listdate[2]
    jsonreq['TRDACCT'] = TrdAcct
    jsonreq['STKBD'] = StkBd
    jsonreq['SIGN_PLACE'] = listdate[4]
    jsonreq['OPEN_TYPE_WIN'] = listdate[3]
    jsonreq['OPEN_TYPE'] = listdate[3]
    code,msg,rows = await adaptcommbiz(ref_request,'600384',jsonreq)
    if code != '0':
        return '-60004610', '中登创业板协议签署处理失败' + msg, []
    szSerialNo = ''
    for row in rows :
        szSerialNo = row['SERIAL_NO']

    #part5:调用99000385(证券账户业务信息查询)接口查询发送中登委托结果, 99000385(证券账户业务信息查询)接口的入参为99000384 (证券账户业务信息操作(新))的出参“流水序号”
    jsonreq['SERIAL_NO'] = szSerialNo
    iQueryCounts = 0
    AccStatus = ''
    DebugMsg = ''
    while iQueryCounts <= 35 :
        code,msg,rows = await adaptcommbiz(ref_request,'99000385',jsonreq)
        if code != '0':
            return '-60004612', '中登创业板协议签署处理失败' + msg, []
        for row in rows :
            AccStatus = row['ACCTBIZ_STATUS']
            DebugMsg = row['RETURN_MSG']
        if AccStatus == '3' :
            return '-60004603', '中登创业板协议签署处理失败' + DebugMsg, []
        if AccStatus == '4' :
            return '-60004604', '中登创业板协议签署结果查询失败' + DebugMsg, []
        if AccStatus != '2' :
            iQueryCounts = iQueryCounts + 1
            time.sleep(0.3)
        else:   #等于2时不用再循环
            break
    if AccStatus != 2 : #查了35次还是不等于2，失败
        return '-60004605', '中登创业板协议签署结果查询失败,请稍候重新提交', []

    #part6:调用******** (客户签署协议)接口转签创业板协议
    code,msg,rows = await adaptcommbiz(ref_request,'600261A',jsonreq)
    if code != '0':
        return '-60004613', '中登创业板协议签署处理失败' + msg, []

    code,msg,rows = await adaptcommbiz(ref_request,'600046A',jsonreq)
    if code != '0':
        return '-60004614', '修改联系人处理失败' + msg, []

    return '0', 'success', []

#获取生效日期
# listdate[0]签署日期strSignDate listdate[1]开通类型strSignCls [2]生效日期strEftDate [3]开通类型strOpenType [4]开通地点strSignPlace
async def CalcuDataTime(ref_request, listdate, user_id):
    jsonreq = {}
    jsonreq['PHYSICAL_DATE'] = listdate[0]  #日期
    jsonreq['DIFF_DAYS'] = listdate[1]      #T+n日
    code,msg,rows = await adaptcommbiz(ref_request,'260601',jsonreq)
    if code != '0':
        return code,msg,rows
    for row in rows :
        listdate[2] = row['PHYSICAL_DATE']    #生效日期

    todaydate = time.strftime( '%Y%m%d',  time.localtime() )
    strKey = user_id + 'TAkey'
    #part2: 连接Redis数据库, 获取入参 ：判断是否为T+A类型
    strIsTA = GetRedisKey(strKey)   #生效日期
    if strIsTA == None :
        return '-60004607', '创业板转签失败,存储的签署日期和类型已过期,请重新查询转签权限获取相关参数', []
    strIsTA = strIsTA.decode('utf-8')
    if strIsTA == '' :
        return '-60004608', '创业板转签失败,于redis获取是否T+A失败', []
    if strIsTA == "1" :
        listdate[1] = 'A'
        listdate[3] = 'A'
        listdate[4] = 'A'

        listdate[0] = todaydate
        if listdate[2] <= todaydate :
            listdate[2] = todaydate
    else:
        listdate[3] = listdate[1]
        listdate[4] = '0'
        if listdate[2] <= todaydate :
            jsonreq['PHYSICAL_DATE'] = todaydate
            jsonreq['DIFF_DAYS'] = '1'
            code,msg,rows = await adaptcommbiz(ref_request,'260601',jsonreq)
            if code != '0':
                return code,msg,rows
            for row in rows :
                listdate[2] = row['PHYSICAL_DATE']    #生效日期
    return '0', 'success', []


#600161 综合业务委托查询
async def adapt600161(ref_request,serviceid,jsonreq):
    reqcount = 10 #默认请求10条
    if 'REC_COUNT' in jsonreq: #获取需要请求的记录条数
        reqcount = int(jsonreq['REC_COUNT'])

    curcount = 0
    ans = []
    while curcount < reqcount:
        code,msg,rows = await adaptcommbiz(ref_request,'600161',jsonreq)
        if code != '0':
            return code,msg,rows

        if len(rows) == 0: #如果当前请求没有当日委托记录，则跳出循环
            break

        last_key_str = rows[-1:][0]['KEY_STR'] #获取最后一条记录的定位串
        answithdraw = []
        ansnormal = []
        for row in rows :
            if row['IS_WITHDRAW'] == '1':
                answithdraw.append(row)  # 撤单的
            else:
                ansnormal.append(row)  # 正常的

        for ansnor in ansnormal:
            for ansdraw in answithdraw:
                if ansdraw['ORDER_TEMPID'] == ansnor['ORDER_TEMPID'] and ansnor['DCL_FLAG'] == '1' and ansdraw['DCL_FLAG'] == '0' :
                    ansnor['DCL_FLAG'] = '7'

        ans += ansnormal[:reqcount-curcount] #将记录合并
        curcount += len(ansnormal)
        jsonreq['KEY_STR'] = last_key_str #将入参的定位串更新

        if len(rows) < reqcount:  # 如果当前返回的记录条数小于请求的记录条数，说明查询已经到底，则跳出循环
            break

    #rows = Include_FilterRwos(rows,[],[{"IS_WITHDRAW":"1"}])
    #ansnor.sort(key=lambda r: (r['ORDER_TIME']), reverse=True) #按日期时间降序
    return '0', '查询当日委托成功', ans


#600160今日委托查询
async def adapt600160(ref_request,serviceid,jsonreq):
    reqcount = 10 #默认请求10条
    if 'REC_COUNT' in jsonreq: #获取需要请求的记录条数
        reqcount = int(jsonreq['REC_COUNT'])

    curcount = 0
    ans = []
    while curcount < reqcount:
        code,msg,rows = await adaptcommbiz(ref_request,'600160',jsonreq)
        if code != '0':
            return code,msg,rows

        if len(rows) == 0: #如果当前请求没有当日委托记录，则跳出循环
            break

        last_key_str = rows[-1:][0]['KEY_STR'] #获取最后一条记录的定位串
        answithdraw = []
        ansnormal = []
        for row in rows :
            if row['IS_WITHDRAW'] == '1':
                answithdraw.append(row)  # 撤单的
            else:
                ansnormal.append(row)  # 正常的

        for ansnor in ansnormal:
            for ansdraw in answithdraw:
                if ansdraw['ORDER_TEMPID'] == ansnor['ORDER_TEMPID'] and ansnor['DCL_FLAG'] == '1' and ansdraw['DCL_FLAG'] == '0' :
                    ansnor['DCL_FLAG'] = '7'

        ans += ansnormal[:reqcount-curcount] #将记录合并
        curcount += len(ansnormal)
        jsonreq['KEY_STR'] = last_key_str #将入参的定位串更新

        if len(rows) < reqcount:  # 如果当前返回的记录条数小于请求的记录条数，说明查询已经到底，则跳出循环
            break

    #rows = Include_FilterRwos(rows,[],[{"IS_WITHDRAW":"1"}])
    #ansnor.sort(key=lambda r: (r['ORDER_TIME']), reverse=True) #按日期时间降序
    return '0', '查询当日委托成功', ans

#600170历史委托查询
async def adapt600170(ref_request,serviceid,jsonreq):
    code,msg,rows = await adaptcommbiz(ref_request,'600170',jsonreq) #要查历史
    if code != '0':
        return code,msg,rows
    ans = []
    for row in rows: # 过滤撤单 exchange_type 只保留以上类别( 129ADGH)的数据，是证券的 (MARKET是转换后的)
        if row['MARKET'] in ['0','1','2','3','9','A','7']:
            ans.append(row)

    return '0',msg,ans


async def adapt600174(ref_request,serviceid,jsonreq: dict):
    """
    加薪宝等余额理财产品赎回
    :param ref_request:
    :param serviceid:
    :param jsonreq:
    :return:
    """
    redeem_id = jsonreq.get('REDEEM_ID')
    if redeem_id and len(redeem_id) > 8:
        jsonreq['entrust_date'] = redeem_id[:8]
        jsonreq['serial_no'] = redeem_id[8:]
    else:
        return '-1009', 'REDEEM_ID不符合规则', []
    code,msg,rows = await adaptcommbiz(ref_request,'600174',jsonreq) #要查历史
    if code == '0':
        for row in rows:
            row['ORDER_ID'] = jsonreq['entrust_date'] + row['ORDER_ID']
    return code,msg,rows


#600180今日成交查询
async def adapt600180(ref_request,serviceid,jsonreq):
    #查询多接口，合并分页
    reqcount = 50  # 默认请求50条
    if 'REC_COUNT' in jsonreq: #获取需要请求的记录条数
        reqcount = int(jsonreq['REC_COUNT'])

    cur_key_str =''
    if 'KEY_STR' in jsonreq:
        cur_key_str = jsonreq['KEY_STR']

    currcount = 0
    ans = []
    nor_key_str = '!NOR'
    pfp_key_str = '!PFP'
    last_key_str = ''

    while currcount < reqcount:
        pfp_idx = cur_key_str.find(pfp_key_str)
        if pfp_idx == -1:
            nor_idx = cur_key_str.find(nor_key_str)
            if nor_idx != -1:
                jsonreq['KEY_STR'] = cur_key_str[0:nor_idx]
            code, msg, rows = await adaptcommbiz(ref_request, '600180', jsonreq)
            if code != '0':
                return code,msg,rows
            # 空包的判断
            rows = judge_return_null('600180', rows)
            return_len = len(rows)
            if return_len == 0:
                cur_key_str = pfp_key_str
                continue
            cur_key_str = rows[-1:][0]['KEY_STR']
            last_key_str = rows[-1:][0]['KEY_STR'] + nor_key_str
            rows = Include_FilterRwos(rows, [], [{"MATCH_TYPE": "1"}])
            ans.extend(rows)
            currcount = len(ans)
            jsonreq['KEY_STR'] = cur_key_str
            if return_len < reqcount:
                cur_key_str = pfp_key_str
                continue
        else:
            pfp_idx = cur_key_str.find(pfp_key_str)
            if pfp_idx != -1:
                jsonreq['KEY_STR'] = cur_key_str[0:pfp_idx]
            if currcount != 0:
                jsonreq['REC_COUNT'] = str(reqcount - currcount)
            code,msg,rows = await adaptcommbiz(ref_request,'600180A',jsonreq)
            if code != '0':
                return code,msg,rows
            rows = judge_return_null('600180', rows)
            return_len = len(rows)
            if return_len == 0:
                break
            cur_key_str = rows[-1:][0]['KEY_STR']
            last_key_str = rows[-1:][0]['KEY_STR'] + pfp_key_str
            rows = Include_FilterRwos(rows,[],[{"MATCH_TYPE":"1"}])
            ans.extend(rows)
            currcount = currcount + len(ans)
            jsonreq['KEY_STR'] = cur_key_str
            if return_len < reqcount:
                break
    for item in ans:
        item['KEY_STR'] = last_key_str
    ans.sort(key=lambda r: (r['MATCHED_TIME']), reverse=True) #按日期时间降序
    return '0','当日成交查询成功',ans

#600230银证流水当日查询
async def adapt600230(ref_request,serviceid,jsonreq):
    #查询多
    code,msg,rows = await adaptcommbiz(ref_request,'600230',jsonreq)
    if code != '0':
        return code,msg,rows
    if jsonreq['USER_ID_CLS'] == 'B':
        rows = Include_FilterRwos(rows,[{"BIZ_CODE":"1001"},{"BIZ_CODE":"1002"}],[])
    rows.sort(key=lambda r: (r['TRAN_DATE'],r['TRAN_TIME']), reverse=True) #按日期时间降序
    keystr = ''
    count = ''
    if 'KEY_STR' in jsonreq:
        keystr = jsonreq['KEY_STR']
    if 'REC_COUNT' in jsonreq:
        count = jsonreq['REC_COUNT']
    ans = makeKEY_STR(rows,keystr,count)
    return '0',msg,ans

#600270历史转账流水查询
async def adapt600270(ref_request,serviceid,jsonreq):
    #查询多
    code,msg,rows = await adaptcommbiz(ref_request,'600270',jsonreq)
    if code != '0':
        return code,msg,rows

    rows = Include_FilterRwos(rows,[{"BIZ_CODE":"1001"},{"BIZ_CODE":"1002"}],[])
    rows.sort(key=lambda r: (r['TRAN_DATE'],r['TRAN_TIME']), reverse=True)
    keystr = ''
    count = ''
    if 'KEY_STR' in jsonreq:
        keystr = jsonreq['KEY_STR']
    if 'REC_COUNT' in jsonreq:
        count = jsonreq['REC_COUNT']
    ans = makeKEY_STR(rows,keystr,count)
    return '0',msg,ans

#新股申购状态查询
async def adapt600331(ref_request,serviceid,jsonreq):
    #查新股申购信息
    #kwljsonreqtpm = copy.deepcopy(kwljsonreq)  #备份请求
    code,msg,rows330 = await adaptcommbiz(ref_request,'600330',jsonreq) #配号查询
    if code != '0':
        return code,msg,rows330
    code,msg,rows340 = await adaptcommbiz(ref_request,'600340',jsonreq) #中签查询
    if code != '0':
        return code,msg,rows340

    #整理配号查询数据
    for each330 in rows330:
        if each330['ASSIGN_COUNT'] == '0':
            each330['ASSIGN_STATUS'] = '1'   #未配号
        else:
            each330['ASSIGN_STATUS'] = '3'  #已配号
    if len(rows340) == 0:   #无中签结果的情况
        return '0','近期申购信息查询成功',rows330

    #整理中签查询结果(有些数据只有配号查询才有的)
    rows = []
    for each340 in rows340:
        each340['ASSIGN_STATUS'] = '5'  #已中签
        for each330 in rows330:
            if each330['MARKET'] == each340['MARKET']:
                if each330['SECU_CODE'][3:6] == each340['SECU_CODE'][3:6]:
                    #从配号查询里补充数据
                    each340['ASSIGN_COUNT'] = each330['ASSIGN_COUNT'] #配号个数
        rows.append(each340)

    #补充未中签记录到最终应答
    for each330 in rows330:
        bneedadd = True
        for each340 in rows340:
            if each330['MARKET'] == each340['MARKET']:
                #市场同且股票代码后三位同则是同一只股票
                if each330['SECU_CODE'][3:6] == each340['SECU_CODE'][3:6]:
                    bneedadd = False
        if bneedadd:
            rows.append(each330)   #配号查询出来的股票不在中签的查询里
    return code,msg,rows

#600380申购额查询
async def adapt600380(ref_request,serviceid,jsonreq):
    #查询多
    code,msg,rows = await adaptcommbiz(ref_request,'600380',jsonreq)
    if code != '0':
        return code,msg,rows
    if len(rows) == 0:
        return code,msg,[{"QYSL":"0","MARKET":"0"},{"QYSL":"0","MARKET":"1"},{"QYSL":"0","MARKET":"2"},{"QYSL":"0","MARKET":"3"}]
    return code,msg,rows


# 600030券商营业部信息查询
async def adapt600030(ref_request, serviced, jsonreq):
    #jsonreq_bak = copy.copy(jsonreq)
    account = ''
    if 'ACCOUNT' in jsonreq:
        account = jsonreq['ACCOUNT']
    jsonreq['USER_CODE'] = account
    code, msg, rows = await adaptcommbiz(ref_request, '600030B', jsonreq)
    if code != '0':
        return code, msg, rows

    for eachrow in rows: #有多少个市场，这里就会循环多少次
        if 'ID_TYPE' in eachrow:
            jsonreq['ID_TYPE'] = eachrow['ID_TYPE']
        if 'ID_CODE' in eachrow:
            jsonreq['ID_CODE'] = eachrow['ID_CODE']

    codetmp, msgtmp, rowstmp = await adaptcommbiz(ref_request, '600030A', jsonreq)
    if codetmp != '0':
        return codetmp, msgtmp, rowstmp

    ans = []
    rows = {}
    for eachrow in rowstmp:
        if 'BRANCH_CODE' in eachrow:
            rows['BRANCH_CODE'] = eachrow['BRANCH_CODE']
        if 'BRANCH_NAME' in eachrow:
            rows['BRANCH_NAME'] = eachrow['BRANCH_NAME']
        if 'COMPANY_NAME' in eachrow:
            rows['COMPANY_NAME'] = eachrow['COMPANY_NAME']
    ans.append(rows)
    return code, '查询客户营业部信息成功', ans

# 600060查询交易账户
async def adapt600060(ref_request, serviced, jsonreq):
    #jsonreq_bak = copy.copy(jsonreq)
    code, msg, rows = await adaptcommbiz(ref_request, '600060', jsonreq)
    if code != '0':
        return code, msg, rows

    for eachrow in rows:
        #应该是先根据holder_kind去判断是普通还是基金账户，然后根据exchange_type去判断上海还是深圳吧(holder_kind 为2为基金账户 and 0为普通账户) 后续要改
        # if eachrow['MARKET'] == '1' and  eachrow['SECU_ACC'][0:1] == 'F':
        #     eachrow['MARKET'] = '5'
        # if eachrow['MARKET'] == '0' and eachrow['SECU_ACC'][0:2] == '05':
        #     eachrow['MARKET'] = '4'

        # 沪深可转债交易权限的判断，MARKET=1, 5为沪A和沪基金
        if eachrow['MARKET'] in {'1', '5'}:
            # holder_rights中包含{则为可转债权限
            if '{' in eachrow['holder_rights']:
                eachrow['SH_KZZ_STATUS'] = '1'
            else:
                eachrow['SH_KZZ_STATUS'] = '0'
            eachrow['SZ_KZZ_STATUS'] = '0'
        # 沪深可转债交易权限的判断，MARKET=0, 4为深A和深基金
        elif eachrow['MARKET'] in {'0', '4'}:
            # holder_rights中包含{则为可转债权限
            if '{' in eachrow['holder_rights']:
                eachrow['SZ_KZZ_STATUS'] = '1'
            else:
                eachrow['SZ_KZZ_STATUS'] = '0'
            eachrow['SH_KZZ_STATUS'] = '0'
        else:
            eachrow['SZ_KZZ_STATUS'] = '0'
            eachrow['SH_KZZ_STATUS'] = '0'

        # 创业板权限
        if 'W' in eachrow['holder_rights']:
            eachrow['GEM_STATUS'] = '1'
            eachrow['GEM_FIX_STATUS'] = '0'
        elif 'j' in eachrow['holder_rights']:
            eachrow['GEM_STATUS'] = '1'
            eachrow['GEM_FIX_STATUS'] = '1'
        else:
            eachrow['GEM_STATUS'] = '0'
            eachrow['GEM_FIX_STATUS'] = '0'

        if 'O' in eachrow['holder_rights']: #科创版权限O
            eachrow['KE_CHUANG_STATUS'] = '1'
        else:
            eachrow['KE_CHUANG_STATUS'] = '0'

        # 是否开通余额理财服务 0:未开通 1:已开通
        if 'k' in eachrow['holder_rights']:
            eachrow['BALANCE_SERVICE_STATUS'] = '1'
        else:
            eachrow['BALANCE_SERVICE_STATUS'] = '0'

    if len(rows) == 1:
        onerow = rows[0]
        if onerow['ACCOUNT'] == '':
            rows = []

    return code, '查询交易账户状态成功', rows

# 600340中签查询
async def adapt600340(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)
    if 'BEGIN_DATE' not in jsonreq or 'END_DATE' not in jsonreq:
        return '-100', '中签查询失败：BEGIN_DATE和END_DATE不存在，请检查。', []
    if jsonreq['BEGIN_DATE'] == "" or jsonreq['END_DATE'] == "":
        return '-101', '中签查询失败：BEGIN_DATE和END_DATE不能为空，请检查。', []

    #查询已归档的数据
    code, msg, rows = await adaptcommbiz(ref_request, '600340B', jsonreq)
    if code != '0':
        return code, msg, rows

    ans = []
    onerow = {}
    for eachrow in rows:
        if 'KEY_STR' in eachrow:
            onerow['KEY_STR'] = eachrow['KEY_STR']
        if 'MARKET' in eachrow:
            onerow['MARKET'] = eachrow['MARKET']
        if 'SECU_ACC' in eachrow:
            onerow['SECU_ACC'] = eachrow['SECU_ACC']
        if 'SECU_CODE' in eachrow:
            onerow['SECU_CODE'] = eachrow['SECU_CODE']
        if 'SECU_NAME' in eachrow:
            onerow['SECU_NAME'] = eachrow['SECU_NAME']
        if 'MATCH_PRICE' in eachrow:
            onerow['MATCH_PRICE'] = eachrow['MATCH_PRICE']
        if 'MATCHED_QTY' in eachrow:
            onerow['MATCHED_QTY'] = eachrow['MATCHED_QTY']
        if 'ASSIGN_DATE' in eachrow:
            onerow['ASSIGN_DATE'] = eachrow['ASSIGN_DATE']
        ans.append(onerow.copy())
        onerow = {}

    #查询已中签但还未归档的数据
    codetmp, msgtmp, rowstmp = await adaptcommbiz(ref_request, '600340A', jsonreq)
    if codetmp != '0':
        return codetmp, msgtmp, rowstmp

    for eachrow in rowstmp:
        if 'ASSIGN_DATE' in eachrow:
            #判断起始日期在所给日期之间则加入结果集
            if  jsonreq['BEGIN_DATE'] <= eachrow['ASSIGN_DATE'] and eachrow['ASSIGN_DATE'] <= jsonreq['END_DATE']:
                onerow['ASSIGN_DATE'] = eachrow['ASSIGN_DATE']
                if 'KEY_STR' in eachrow:
                    onerow['KEY_STR'] = eachrow['KEY_STR']
                if 'MARKET' in eachrow:
                    onerow['MARKET'] = eachrow['MARKET']
                if 'SECU_ACC' in eachrow:
                    onerow['SECU_ACC'] = eachrow['SECU_ACC']
                if 'SECU_CODE' in eachrow:
                    onerow['SECU_CODE'] = eachrow['SECU_CODE']
                if 'SECU_NAME' in eachrow:
                    onerow['SECU_NAME'] = eachrow['SECU_NAME']
                if 'MATCH_PRICE' in eachrow:
                    onerow['MATCH_PRICE'] = eachrow['MATCH_PRICE']
                if 'MATCHED_QTY' in eachrow:
                    onerow['MATCHED_QTY'] = eachrow['MATCHED_QTY']
                ans.append(onerow.copy())
                onerow = {}

    return code, '查询新股申购中签信息成功', ans

# 600061查看股东账户开户标记
async def adapt600061(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)
    code, msg, rows = await adaptcommbiz(ref_request, '600042', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        eachrow['NEW_ACC_FLAG'] = '1'

    return code, '查询股东账户开户标记成功', rows

# 600063适当性匹配查询
async def adapt600063(ref_request, serviced, jsonreq):
    #  客户适当性信息查询
    code, msg, rows = await adaptcommbiz(ref_request, '600063', jsonreq)
    if code != '0':
        return code, msg, rows
    ans = []
    onerow = {}
    for eachrow in rows:
        onerow['RISK_TYPE'] = eachrow['RISK_TYPE']      #客户风险等级
        onerow['CUSTOMER_RANGE'] = eachrow['CUSTOMER_RANGE']    #客户投资偏好范围
        onerow['CUSTOMER_TERM'] = eachrow['CUSTOMER_TERM']      #客户投资偏好期限
        if eachrow['RISK_TYPE'] in ['0','1','2']:
            onerow['MATCH_RESULT'] = '2'    #当用户为保守型、谨慎型、稳健型则返回2
        elif eachrow['RISK_TYPE'] in ['3','4']:
            onerow['MATCH_RESULT'] = '1'    #当用户为激进型、积极型则返回1

        if onerow['MATCH_RESULT'] == '1':   #匹配
            onerow['RISK_FLAG'] = '1'
            onerow['TOTAL_FLAG'] = '1'
        elif onerow['MATCH_RESULT'] == '2':  #不匹配
            onerow['RISK_FLAG'] = '0'
            onerow['TOTAL_FLAG'] = '0'
        if eachrow['CUST_LASTRISK_FLAG'] == '1':
            onerow['RISK_TYPE'] = '5'   #保守型（最低等级）不能开通科创版

        onerow['INRANGE_FLAG'] = '1'    #投资范围是否适配固定返回1
        onerow['TERM_FLAG'] = '1'       #投资期限是否适配固定返回1
        onerow[' RISK_PRODUCT'] = '3'  # 产品风险等级（中高风险）
        onerow['PRODUCT_RANGE'] = ''  # 产品投资范围（柜台为空）
        onerow['PRODUCT_TERM'] = ''  # 产品投资期限（柜台为空）

    # 判断交易经验
    code, msg, rows = await adaptcommbiz(ref_request, '********', jsonreq)
    if code != '0':
        return code, msg, rows
    todaydate = time.strftime('%Y%m%d', time.localtime())
    if rows[0]['EARLY_TRD_DATE'] == '0':
        # 等于0代表没查到，要调L2100217查中登
        code, msg, rows217 = await adaptcommbiz(ref_request, 'L2100217', jsonreq)
        if code != '0':
            return code, msg, rows217
        year = int(rows217[0]['EARLY_TRD_DATE'])
        if year == 0:
            onerow['TRADE_EXPERIENCE_YEAR'] = '2'  # 未知
        elif int(todaydate) - year >= 20000:  # 20190711 - 20170711 = 20000 大于20000就满两年
            onerow['TRADE_EXPERIENCE_YEAR'] = '1'  # 交易经验满两年
        else:
            onerow['TRADE_EXPERIENCE_YEAR'] = '0'
    elif int(todaydate) - int(rows[0]['EARLY_TRD_DATE']) >= 20000:  # 20190711 - 20170711 = 20000 大于20000就满两年
        onerow['TRADE_EXPERIENCE_YEAR'] = '1'   #交易经验满两年
    else:
        onerow['TRADE_EXPERIENCE_YEAR'] = '0'

    code, msg, rows_birthday = await adaptcommbiz(ref_request, '160001', jsonreq)
    if code != '0':
        return code, msg, rows_birthday
    is_70yearold = 0
    year_now = time.strftime('%Y', time.localtime())
    for eachrow in rows_birthday:
        if int(year_now) - int(eachrow['BIRTHDAY']) >= 70:  #满70周岁
            is_70yearold = 1

    if rows[0]['CAN_OPEN']  == '0':   #符合券商开通条件
        if is_70yearold == 0:   #不满70岁符合可以开通
            onerow['CAN_OPEN'] = '0'
        else:
            onerow['CAN_OPEN'] = '1'    #符合券商开通条件，但70周岁以上不允许网上开通
            onerow['CANNOT_OPEN_REASON'] = '2'
    else:   #非0不符合券商开通条件
        if is_70yearold == 0:
            onerow['CANNOT_OPEN_REASON'] = '0,1'  #不满足券商开通条件但没70岁
        else:
            onerow['CANNOT_OPEN_REASON'] = '0,1,2'  #不满足券商开通条件而且70岁以上了

    ans.append(onerow)
    return code, '客户适当性信息查询记成功', ans

# 查询风险签署情况
async def adapt600055(ref_request, serviced, jsonreq):
    if jsonreq['SIGN_TYPE'] == '3':
        code = '0'
        msg = ''
        rows = [{'STATUS': '0'}]
    else:
        code = '1'
        msg = '暂不支持此功能'
        rows = []

    return code, msg, rows

# 签订风险警示书
async def adapt600064(ref_request, serviced, jsonreq):
    signType = jsonreq.get('SIGN_TYPE', '')

    if signType == '3':
        code = '0'
        msg = ''
        rows = [{'SIGN_CONTRACT': '0'}]
    elif signType == '4':
        # 隐私协议签署
        todayDate = datetime.datetime.now()
        nowTime = todayDate.strftime("%Y%m%d%H%M%S")
        jsonreq['csfc_begin_date'] = todayDate.strftime("%Y%m%d")
        # 往后推50年算法，用日期根据天数往后推。可以避免非法日期，比如：2074.02.29
        jsonreq['csfc_end_date'] = date_add_year(todayDate)
        remark = '隐私协议签署;%s;%s;%s;签署隐私协议%s版;已签署' % (nowTime, jsonreq['ACCOUNT'],
                                                    jsonreq['TCC'], jsonreq['SIGN_VER'])
        jsonreq['sdc_account'] = jsonreq['ACCOUNT']
        jsonreq['acct_exch_type'] = 'Z01'
        jsonreq['agree_type'] = '!'
        jsonreq['trans_account'] = jsonreq['ACCOUNT']
        jsonreq['sub_risk_flag'] = 'a'
        jsonreq['risk_level'] = ''
        jsonreq['agree_model_no'] = AGREE_MODEL_NO
        jsonreq['prodta_no'] = ''
        jsonreq['prod_code'] = ''
        jsonreq['remark'] = remark
        code, msg, rows = await adaptcommbiz(ref_request, '331203', jsonreq)
    elif signType == '5':
        # 股票风险信息提示留痕
        code, msg, risk_info = await adaptcommbiz(ref_request, '600034', jsonreq)
        if code != '0':
            return code, msg, risk_info
        if len(risk_info) == 0:
            return '-2301', '%s无风险信息'%jsonreq['SECU_CODE'], []

        todayDate = datetime.datetime.now()
        jsonreq['csfc_begin_date'] = todayDate.strftime("%Y%m%d")
        # 往后推50年算法，用日期根据天数往后推。可以避免非法日期，比如：2074.02.29
        jsonreq['csfc_end_date'] = date_add_year(todayDate)
        remark = '%s;%s;%s;已签署' % (jsonreq['SECU_CODE'], risk_info[0]['NOTICE_INFO'], jsonreq['TCC'])
        jsonreq['sdc_account'] = jsonreq['ACCOUNT']
        jsonreq['acct_exch_type'] = 'Z01'
        jsonreq['agree_type'] = '!'
        jsonreq['trans_account'] = jsonreq['ACCOUNT']
        jsonreq['sub_risk_flag'] = 'a'
        jsonreq['risk_level'] = ''
        jsonreq['agree_model_no'] = AGREE_MODEL_NO
        jsonreq['prodta_no'] = ''
        jsonreq['prod_code'] = ''
        jsonreq['remark'] = remark
        code, msg, rows = await adaptcommbiz(ref_request, '331203', jsonreq)
    elif signType == '6':
        # 隐私协议签署
        todayDate = datetime.datetime.now()
        nowTime = todayDate.strftime("%Y%m%d%H%M%S")
        jsonreq['csfc_begin_date'] = todayDate.strftime("%Y%m%d")
        # 往后推50年算法，用日期根据天数往后推。可以避免非法日期，比如：2074.02.29
        jsonreq['csfc_end_date'] = date_add_year(todayDate)
        remark = '自选股条件单协议签署;%s;%s;%s;签署条件单功能服务说明及风险揭示确认书%s版;已签署' % (
            nowTime, jsonreq['ACCOUNT'], jsonreq['TCC'], jsonreq['SIGN_VER'])
        jsonreq['sdc_account'] = jsonreq['ACCOUNT']
        jsonreq['acct_exch_type'] = 'Z01'
        jsonreq['agree_type'] = '!'
        jsonreq['trans_account'] = jsonreq['ACCOUNT']
        jsonreq['sub_risk_flag'] = 'a'
        jsonreq['risk_level'] = ''
        jsonreq['agree_model_no'] = AGREE_MODEL_NO
        jsonreq['prodta_no'] = ''
        jsonreq['prod_code'] = ''
        jsonreq['remark'] = remark
        code, msg, rows = await adaptcommbiz(ref_request, '331203', jsonreq)
    elif signType == '8':
        # 预约打新
        set_331203_param(jsonreq)
        nowTime = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        jsonreq['remark'] = '自选股预约打新协议及风险揭示书%s版;%s;%s;%s;已签署' % (
            jsonreq['SIGN_VER'], nowTime, jsonreq['ACCOUNT'], jsonreq['TCC'])
        code, msg, rows = await adaptcommbiz(ref_request, '331203', jsonreq)
    else:
        code = '1'
        msg = '暂不支持此功能'
        rows = []

    return code, msg, rows


def date_add_year(today_date, year=50):
    """
    往后推50年算法，用日期根据天数往后推。可以避免非法日期，比如：2074.02.29
    :param todayDate:
    :param year:
    :return:
    """
    return (today_date + datetime.timedelta(days=year * 365 + int(year / 4))).strftime("%Y%m%d")


def set_331203_param(jsonreq):
    jsonreq['sdc_account'] = jsonreq['ACCOUNT']
    jsonreq['acct_exch_type'] = 'Z01'
    jsonreq['agree_type'] = '!'
    jsonreq['trans_account'] = jsonreq['ACCOUNT']
    jsonreq['sub_risk_flag'] = 'a'
    jsonreq['risk_level'] = ''
    now = datetime.datetime.now()
    jsonreq['csfc_begin_date'] = now.strftime("%Y%m%d")
    jsonreq['csfc_end_date'] = date_add_year(now)
    jsonreq['agree_model_no'] = AGREE_MODEL_NO
    jsonreq['prodta_no'] = ''
    jsonreq['agency_no'] = ''
    jsonreq['prod_code'] = ''

# 600120-客户资金信息查询
async def adapt600120A(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)
    ans = []
    onerow = {}

    code_120, msg_120, rows_120 = await adaptcommbiz(ref_request, '600120', jsonreq)
    if code_120 != '0':
        return code_120, msg_120, rows_120

    for eachrow in rows_120:
        eachrow['ACCOUNT'] = jsonreq.get('ACCOUNT','')
    return code_120, msg_120, rows_120


# 600120-客户资金信息查询
async def adapt600120(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)
    ans = []
    onerow = {}

    #调用420507查询最大可取资金
    code_120A, msg_120A, rows_120A = await adaptcommbiz(ref_request, '600120A', jsonreq)
    if code_120A != '0':
        return code_120A, msg_120A, rows_120A

    ans_120A = {}
    for eachrow in rows_120A:
        onerow = {}
        #onerow['orgid'] = eachrow['orgid']
        #onerow['fundid'] = eachrow['fundid']
        onerow['moneytype'] = eachrow['moneytype']
        onerow['maxdraw'] = eachrow['maxdraw']
        ans_120A = onerow.copy()

    maxdraw_money = {'0':"", '1':"", '2':""}
    # 0人民币，1港元 ，2美元
    if ans_120A['moneytype'] == '0':
        maxdraw_money['0'] = ans_120A['maxdraw']
    elif ans_120A['moneytype'] == '1':
        maxdraw_money['1'] = ans_120A['maxdraw']
    elif ans_120A['moneytype'] == '2':
        maxdraw_money['2'] = ans_120A['maxdraw']

    # 410502查询客户的资金情况
    code_120B, msg_120B, rows_120B = await adaptcommbiz(ref_request, '600120B', jsonreq)
    if code_120B != '0':
        return code_120B, msg_120B, rows_120B

    for eachrow in rows_120B:
        onerow = {}
        onerow['ACCOUNT'] = eachrow['ACCOUNT']
        onerow['CURRENCY'] = eachrow['CURRENCY']
        onerow['BALANCE'] = eachrow['BALANCE']
        onerow['AVAILABLE'] = eachrow['AVAILABLE']
        onerow['FRZ_AMT'] = eachrow['FRZ_AMT']
        onerow['OUTSTANDING'] = eachrow['OUTSTANDING']
        onerow['MKT_VAL'] = eachrow['MKT_VAL']
        onerow['ASSERT_VAL'] = eachrow['ASSERT_VAL']

    if onerow['CURRENCY'] == '0':
        onerow['DRAW_AMT'] = maxdraw_money['0']
    elif onerow['CURRENCY'] == '1':
        onerow['DRAW_AMT'] = maxdraw_money['1']
    elif onerow['CURRENCY'] == '2':
        onerow['DRAW_AMT'] = maxdraw_money['2']
    ans.append(onerow.copy())

    return code_120B, '查询客户资金信息成功', ans

# 600200-证券持仓查询
async def adapt600200(ref_request, serviced, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '600200', jsonreq)
    if code != '0':
        return code, msg, rows
    rows = list(filter(lambda x: x['SECU_CODE'] not in STOCK_CODE.split(','), rows))
    return code, msg, rows

# 600070查询客户是否是存量客户
async def adapt600070(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)
    # 99000291 查询客户的信息是否存在
    code, msg, rows = await adaptcommbiz(ref_request, '600070A', jsonreq)
    if code != '0':
        return code, msg, rows

    ans = []
    onerow = {}
    if rows == []: #结果集为空
        onerow['STATUS'] = '0' #结果集为空，则表示未开户
        ans.append(onerow.copy())
        return  '0', '接口调用成功', ans

    # 获取客户代码
    user_code = ''
    custorgid = ''
    for eachrow in rows:
        user_code = eachrow['USER_CODE']
        custorgid = eachrow['INT_ORG']

    jsonreq['custid'] = user_code
    jsonreq['CUST_CODE'] = user_code
    jsonreq['custorgid'] = "0" + custorgid

    # 99000265 进行股东查询 判断是否一个股东账号都没有
    codetmp, msgtmp, rowstmp = await adaptcommbiz(ref_request, '600070B', jsonreq)
    if codetmp != '0': #柜台的100被转换成0返回
        return codetmp, msgtmp, rowstmp

    onerow = {}
    if rowstmp == []:  # 结果集为空
        # 结果集为空，则表示客户有资金账号，但是一个股东账号都没有，那么很有可能是跑批失败，则当做未开户处理
        onerow['STATUS'] = '0'
        ans.append(onerow.copy())
        return '0', '接口调用成功', ans
    else:
        onerow['STATUS'] = '1' #已开户
        ans.append(onerow.copy())
        return '0', '接口调用成功', ans

# 600080根据证件号码查询资金账号
async def adapt600080(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)
    # 99000401 查询客户的信息是否存在
    code, msg, rows = await adaptcommbiz(ref_request, '600080', jsonreq)
    if code != '0': #柜台的100被转换成0返回
        return code, msg, rows

    if rows == []:  # 结果集为空
        # 结果集为空，则表示没有查询出资金账号
        return '100', msg, rows
    ans = []
    for eachrow in rows:
        if eachrow['CUST_STATUS'] != '9':   #销户状态
            ans.append(eachrow)
    return code, msg, ans

# 600320
async def adapt600320(ref_request, serviced, jsonreq):
    code, msg, rows1 = await adaptcommbiz(ref_request, '600320', jsonreq)
    if code != '0':
        return code, msg, rows1

    jsonreq['sub_stock_type'] = 'G1'  #空格，表示获取新股申购信息；支持输入'G1',表示获取可转债信用申购信息
    code, msg, rows2 = await adaptcommbiz(ref_request, '600320', jsonreq)
    if code != '0':
        return code, msg, rows2

    for row in rows2:
        rows1.append(row)

    for eachrow in rows1:
        # if eachrow['SG_SECU_CODE'][0:3] == '300':   #创业板300**
        #     eachrow['PURCH_TYPE'] = '1'
        # if eachrow['SG_SECU_CODE'][0:3] == '787':   #科创版787**
        #     eachrow['PURCH_TYPE'] = '2'
        # 根据stkcode_ctrlstr字段的第25，26位进行判断是否为科创版
        if eachrow['GEM_TYPE'][24:26] in ['10', '01', '11']:   #科创版
            eachrow['PURCH_TYPE'] = '2'
        # stkcode_ctrlstr字段第31位判断该新股是否为注册制，如果该标记位是1表示注册制
        # 如果标记位是0，则表示该新股为核准制创业板或非创业板新股或可转债
        if len(eachrow['GEM_TYPE']) > 31:
            eachrow['GEM_TYPE'] = eachrow['GEM_TYPE'][30]
        else:
            eachrow['GEM_TYPE'] = 0

    return code, msg, rows1


# 600330
async def adapt600330(ref_request, serviced, jsonreq):
    code, msg, rows1 = await adaptcommbiz(ref_request, '600330', jsonreq)
    if code != '0':
        return code, msg, rows1
    for row in rows1:
        if row['PURC_CODE'] != '' and row['MARKET'] != '0': #深圳市场的不用转换直接用后台返回的stock_code
            # a股申购代码和配好代码不一样，要做关联（这里全部做关联,stock_code跟relative_code）
            jsonreq['stock_code'] = row['PURC_CODE']
            code, msg, rows2 = await adaptcommbiz(ref_request, '330300', jsonreq)
            if code != '0':
                return code, msg, rows2
            for each_row in rows2:
                if row['PURC_CODE'] == each_row['stock_code']:
                    row['PURC_CODE'] = each_row['relative_code']

    return code, msg, rows1

# 600360银行信息查询
async def adapt600360(ref_request, serviced, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '600360', jsonreq)
    if code != '0':
        return code, msg, rows

    code, msg, rows2 = await adaptcommbiz(ref_request, '332256', jsonreq)
    if code != '0':
        return code, msg, rows2

    for eachrow in rows:
        for each in rows2:
            if eachrow['EXT_INST'] == each['EXT_INST'] and each['main_flag'] == '1':
                eachrow['MAIN_FLAG'] = '01'

    return code, '查询指定客户银行账号成功', rows


# 600390资金流水信息查询
async def adapt600390(ref_request, serviced, jsonreq):
    # 每个功能号请求1000条数据，在本地实现分页
    jsonreq['#REC_COUNT'] = '1000'
    code, msg, rows1 = await adaptcommbiz(ref_request, '600390', jsonreq)
    if code != '0':
        return code, msg, rows1

    code, msg, rows2 = await adaptcommbiz(ref_request, '339305', jsonreq)
    if code != '0':
        return code, msg, rows2

    code, msg, rows3 = await adaptcommbiz(ref_request, '337497', jsonreq)
    if code != '0':
        return code, msg, rows3

    # 空包的判断
    RowsData1 = judge_return_null('600390', rows1)
    RowsData2 = judge_return_null('339305', rows2)
    RowsData3 = judge_return_null('337497', rows3)

    ans = []
    for eachrow in RowsData1:
        ans.append(eachrow)
    for row in RowsData2:
        if row['TRD_BIZ_CODE'] in ['2041','2042','2013']: #转账流水标志
            ans.append(row)

    for row in RowsData3:
        ans.append(row)

    ans = sorted(ans, key=operator.itemgetter('MATCH_DATE'), reverse=False)  #按成交日期排序 True 是倒叙  默认是False
    end_ans = makeKEY_STR(ans, jsonreq.get('KEY_STR', '0'), jsonreq.get('REC_COUNT', '40'))

    return code, '查询 资金流水成功', end_ans

# 700713中签预冻结资金
async def adapt700713(ref_request, serviced, jsonreq):
    # jsonreq_bak = copy.copy(jsonreq)

    jsonreq['matchdate'] = time.strftime("%Y%m%d", time.localtime())
    # 480011 中签预冻结资金，冻结当日中签的股票
    code, msg, rows = await adaptcommbiz(ref_request, '700713', jsonreq)

    return code, msg, []

# 中签新股资金锁定状态查询接口700714
async def adapt700714(ref_request, serviced, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '700714', jsonreq)
    if code != '0':
        return code, msg, rows

    #无第二结果集rows = [] (不判定下面会取不到第二结果集超时)
    if len(rows) == 0 :
        return code, '查询新股资金锁定状态成功', [{'FRZ_AMT':'.00','FRZ_STATUS':'0'}]

    for eachrow in rows:
        if 'FRZ_AMT' in eachrow :
            #无冻结金额FRZ_AMT = .00
            if eachrow['FRZ_AMT'][0] == "." :
                eachrow['FRZ_STATUS'] = '0'  #状态未锁定
            else :
                eachrow['FRZ_STATUS'] = '1'  #状态锁定

    return code, '查询新股资金锁定状态成功', rows

# 查询港股通汇率
async def adapt500402(ref_request, serviceid, jsonreq):
    # 430009 查询港股通汇率
    jsonreq['MARKET'] = '5'  # 先查沪港通
    code, msg, rows = await adaptcommbiz(ref_request, '430009', jsonreq)
    if code != '0':
        return code, msg, rows

    ans = []
    ansrow = {}
    for eachrow in rows:
        if 'buyrate' in eachrow:
            ansrow['RATE_BUY_SH'] = eachrow['buyrate']
        if 'salerate' in eachrow:
            ansrow['RATE_SELL_SH'] = eachrow['salerate']

    jsonreq['MARKET'] = 'S'  # 再查深港通
    code, msg, rows = await adaptcommbiz(ref_request, '430009', jsonreq)
    if code != '0':
        return code, msg, rows

    for eachrow in rows:
        if 'buyrate' in eachrow:
            ansrow['RATE_BUY_SZ'] = eachrow['buyrate']
        if 'salerate' in eachrow:
            ansrow['RATE_SELL_SZ'] = eachrow['salerate']

    ans.append(ansrow)
    return code, "查询港股通参考汇率成功", ans

# 601180港股当日成交查询
async def adapt601180(ref_request, serviced, jsonreq):
    # 430009 查询港股通汇率
    jsonreq['MARKET'] = '5'  # 先查沪港通
    code, msg, rows = await adaptcommbiz(ref_request, '430009', jsonreq)
    if code != '0':
        return code, msg, rows
    ansrow = {}
    for eachrow in rows:
        if 'buyrate' in eachrow:
            ansrow['RATE_BUY_SH'] = eachrow['buyrate']
        if 'salerate' in eachrow:
            ansrow['RATE_SELL_SH'] = eachrow['salerate']

    jsonreq['MARKET'] = 'S'  # 再查深港通
    code, msg, rows = await adaptcommbiz(ref_request, '430009', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        if 'buyrate' in eachrow:
            ansrow['RATE_BUY_SZ'] = eachrow['buyrate']
        if 'salerate' in eachrow:
            ansrow['RATE_SELL_SZ'] = eachrow['salerate']

    jsonreq['MARKET'] = ''
    code, msg, rows = await adaptcommbiz(ref_request, '601180', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        if 'TRD_ID' in eachrow and 'MARKET' in eachrow:
            if eachrow['TRD_ID'] == '0B':  #买入
                if eachrow['MARKET'] == '6':    #深港市场
                    eachrow['RATE'] = ansrow['RATE_SELL_SZ']    #买入行为取卖出汇率
                else:   #沪港市场
                    eachrow['RATE'] = ansrow['RATE_SELL_SH']
            if eachrow['TRD_ID'] == '0S' or eachrow['TRD_ID'] == '5C':   #卖出
                if eachrow['MARKET'] == '6':    #深港市场
                    eachrow['RATE'] = ansrow['RATE_BUY_SZ']    #卖出行为取买入汇率
                else:   #沪港市场
                    eachrow['RATE'] = ansrow['RATE_BUY_SH']
        if 'CURRENCY' in eachrow:
            eachrow['CURRENCY'] = '1'   #柜台返回的都是港币
        if 'ACCOUNT' in jsonreq:
            eachrow['ACCOUNT'] = jsonreq['ACCOUNT']

        #paraList = []
        #paraList.append(eachrow['MATCHED_AMT'])
        #paraList.append(eachrow['RATE'])
                                 #把MATCHED_AMT字段港币转成人民币
        #eachrow['MATCHED_AMT'] = functionPlugIn('HKDtoRMB',paraList)

    return code, msg, rows

# 600121计算当前可用资金的最大可买股数
async def adapt600121(ref_request, serviced, jsonreq):
    if 'MARKET' not in jsonreq:
        return '-1010', '可买入股票数量查询失败：没有传入MARKET，请检查。', []
    if jsonreq['MARKET'] == "":
        return '-1011', '可买入股票数量查询失败：市场不能为空，请检查。', []

    if jsonreq['MARKET'] == "6" or jsonreq['MARKET'] == "7":
        jsonreq['TRD_ID'] = '1R'
        #港股通的市场要先转，否则取不到股东号
        if jsonreq['MARKET'] == "6":
            jsonreq['MARKET'] = 'S'
        if jsonreq['MARKET'] == "7":
            jsonreq['MARKET'] = '5'
        #查港股的
        code, msg, rows = await adaptcommbiz(ref_request, '430007', jsonreq)
    else:
        #查A股的
        jsonreq['TRD_ID'] = '0B'
        code, msg, rows = await adaptcommbiz(ref_request, '410410', jsonreq)
    return code, msg, rows

# 查询港股通标的
async def adapt500403(ref_request, serviceid, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '500403', jsonreq)
    if code != '0':
        return code, msg, rows

    '''模拟柜台定位串跟记录数'''
    KEY_STR = jsonreq.get('KEY_STR','')
    REC_COUNT = jsonreq.get('REC_COUNT','')
    ans = makeKEY_STR(rows,KEY_STR,REC_COUNT)

    '''从redis中取沪港重叠的证券代码'''
    redis_stkcode = GetRedisKey('GGT_DOUBLE_CODE')
    redis_stkcode = eval(redis_stkcode)
    sno = 1
    for eachrow in ans:
        eachrow['SNO'] = str(sno)    #序号递增
        sno = sno + 1
        if eachrow['SECU_CODE'] in redis_stkcode :    #应答中的标的证券跟redis中的对比，有则是沪深重叠的标的
            eachrow['DOUBLE_FLAG'] = '1'
        else:
            eachrow['DOUBLE_FLAG'] = '0'

    return code, msg, ans

# 查询港股通市场额度
async def adapt500401(ref_request, serviceid, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '500401', jsonreq)
    if code != '0':
        return code, msg, rows

    ans = []
    ansrow = {}
    for eachrow in rows:
        if eachrow['market'] == 'S':
            ansrow['TOTAL_AMT_SZ']=eachrow['quotabal']   #当日总额度(深港通)
            ansrow['USABLE_AMT_SZ']=eachrow['quotaavl']  #当日可用额度(深港通)
            ansrow['STATUS_SZ']=eachrow['quotastatus']   #状态说明(深港通)
            ansrow['UPDATE_DATE_SZ']=eachrow['upddate']  #更新日期(深港通)
        if eachrow['market'] == '5':
            ansrow['TOTAL_AMT_SH']=eachrow['quotabal']   #当日总额度(沪港通)
            ansrow['USABLE_AMT_SH']=eachrow['quotaavl']  #当日可用额度(沪港通)
            ansrow['STATUS_SH']=eachrow['quotastatus']   #状态说明(沪港通)
            ansrow['UPDATE_DATE_SH']=eachrow['upddate']  #更新日期(沪港通)
    ans.append(ansrow)

    return code, msg, ans

#港股通权限查询
async def adapt500060(ref_request, serviceid, jsonreq):
    if len(jsonreq['ACCOUNT']) == 0:
        return 0, '港股通权限查询失败：必要入参[ACCOUNT]值不能为空', []

    account = jsonreq['ACCOUNT']
    #******** 查询客户是否具有港股通交易资质
    code, msg, rows = await adaptcommbiz(ref_request, '500060A', jsonreq)
    if code != '0':
        return code, msg, []

    # 沪市港股
    hu_hkstock = {'MARKET':'7', 'SECU_ACC':'', 'OPEN_FLAG':'0', 'PRIOR_FLAG':'0',
                  'CAN_OPEN_FLAG':'0', 'NOT_OPEN_REASON':'0', 'ACCOUNT': account}

    # 深市港股
    shen_hkstock = {'MARKET':'6', 'SECU_ACC':'', 'OPEN_FLAG':'0', 'PRIOR_FLAG':'0',
                  'CAN_OPEN_FLAG':'0', 'NOT_OPEN_REASON':'0', 'ACCOUNT': account}

    num = 0 #可以交易的港股通市场
    for eachrow in rows:
        if eachrow['CUST_AGMT_TYPE'] == '0l': #港股通协议类型为0l
            if eachrow['STKBD'] == '13': #表示沪港通已经开通
                hu_hkstock['OPEN_FLAG'] = '1'
                hu_hkstock['CAN_OPEN_FLAG'] = '1'
                hu_hkstock['NOT_OPEN_REASON'] = '1' #表示已经开通
                hu_hkstock['SECU_ACC'] = eachrow['TRDACCT']
                hu_hkstock['PRIOR_FLAG'] = '1'
                num = num + 1

            if eachrow['STKBD'] == '03': #表示深港通已经开通
                shen_hkstock['OPEN_FLAG'] = '1'
                shen_hkstock['CAN_OPEN_FLAG'] = '1'
                shen_hkstock['NOT_OPEN_REASON'] = '1'
                shen_hkstock['SECU_ACC'] = eachrow['TRDACCT']
                shen_hkstock['PRIOR_FLAG'] = '1'
                num = num + 1

    # 如果可交易的市场数量大于0，则表示该客户具有港股通交易权限，
    # 但是微证券中台不一定记录了该客户的港股通优先账号（因为客户可能是通过其它渠道开通的港股通）
    if num > 0:
        #查询中台是否有记录该客户的港股通优先账号
        iRet, msg, ans = queryPriorMarket(account)
        if iRet != 0:
            return '-1110', msg, []

        if len(ans) == 0: #表示客户没有设置港股通优先账号，则需要入库
            market = '5'  # 默认使用 沪港通 作为优先账号, 5为柜台字典的沪市港股
            if num == 1:
                if shen_hkstock['OPEN_FLAG'] == '1':
                    market = 'S' #S为柜台字典的深市港股
                iRet, msg, ans = setPriorMarket(account, market, 0) #新增设置
                if iRet != 0:
                    return '-1111', '港股通权限查询失败：优先账户查询失败', []
            elif num == 2: #表示客户有两个港股通账号
                shen_hkstock['PRIOR_FLAG'] = '0'
                iRet, msg, ans = setPriorMarket(account, market, 0) #新增设置
                if iRet != 0:
                    return '-1112', '港股通权限查询失败：优先账户查询失败', []

        if len(ans) > 0 and num == 2: #如果数据库中已经设置了优先账号，且港股通可交易市场数目为2
            for each in ans:
                if each['PRIOR_MARKET'] == '5':
                    hu_hkstock['PRIOR_FLAG'] = '1'
                    break
                if each['PRIOR_MARKET'] == 'S':
                    shen_hkstock['PRIOR_FLAG'] = '1'
                    break

    ans = []
    ans.append(hu_hkstock)
    ans.append(shen_hkstock)
    return code, '港股通权限查询成功', ans

#港股通开通
async def adapt500061(ref_request, serviceid, jsonreq):
    #查股东
    code, msg, rows = await adaptcommbiz(ref_request, '600061', jsonreq)
    if code != '0':
        return code, msg, rows
    '''把查出来的市场都记下来'''
    market0, market1, marketS, market5 = '', '', '', ''
    for eachrow in rows:
        eachrow.get('market','')
        if eachrow['market'] == '0':
            market0 = '0'
        if eachrow['market'] == '1':
            market1 = '1'
        if eachrow['market'] == 'S':
            marketS = 'S'
        if eachrow['market'] == '5':
            market5 = '5'
    MARKETS = jsonreq.get('MARKETS','')
    '''股东判断'''
    if MARKETS == '6':
        if market0 != '0':
            return '-1001', '该客户无深A股东卡', []
        if marketS != 'S':
            return '-1007', '该客户无深H股东卡', []
    if MARKETS == '7' :
        if market1 != '1':
            return '-1002', '该客户无沪A股东卡', []
        if market5 != '5':
            return '-1008', '该客户无沪H股东卡', []
    if MARKETS == '6,7':
        if market0 != '0':
            return '-1003', '该客户无深A股东卡', []
        if market1 != '1':
            return '-1004', '该客户无沪A股东卡', []
        if market5 != '5':
            return '-1005', '该客户无沪H股东卡', []
        if marketS != 'S':
            return '-1006', '该客户无深H股东卡', []

    '''查用户资料判断是否是个人投资者'''
    code, msg, rows = await adaptcommbiz(ref_request, '160001', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        eachrow.get('USER_TYPE','')
        if eachrow['USER_TYPE'] != '0':
            return '-1005', '该客户不是个人投资者', []

    '''查资金判断是否有资产50W'''
    code, msg, rows = await adaptcommbiz(ref_request, '600120B', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        eachrow.get('ASSERT_VAL','')
        if float(eachrow['ASSERT_VAL']) < 500000.0 :
            return '-1006', '该客户资产总值不足50W', []

    '''风险测评'''
    jsonreq['ANS_STATUS'] = '1'
    code, msg, rows = await adaptcommbiz(ref_request, '190086', jsonreq)
    if code != '0':
        return code, msg, rows
    jsonreq['ANS_STATUS'] = '2'
    jsonreq['RIST_ASSESSMENT'] = ''
    code, msg, rows = await adaptcommbiz(ref_request, '190086', jsonreq)
    if code != '0':
        return code, msg, rows
    '''判断风险测评等级是否是 4积极型或5激进型'''
    for eachrow in rows:
        eachrow.get('RATING_LVL','')
        if eachrow['RATING_LVL'] not in ['4','5']:
            return '-1016', '风险测评结果：' + eachrow.get('RATING_LVL_NAME','') + '不满足风险承受能力！', []

    '''设置港股权限'''
    if MARKETS == '6,7':    #多个市场一个一个设置
        jsonreq['MARKET'] = 'S'
        code, msg, rows = await adaptcommbiz(ref_request, '410331', jsonreq)
        if code != '0':
            return code, msg, rows
        jsonreq['MARKET'] = '5'
        code, msg, rows = await adaptcommbiz(ref_request, '410331', jsonreq)
        if code != '0':
            return code, msg, rows

    if MARKETS == '6':      #单个市场先转换
        jsonreq['MARKET'] = 'S'
    if MARKETS == '7':
        jsonreq['MARKET'] = '5'
    code, msg, rows = await adaptcommbiz(ref_request, '410331', jsonreq)
    if code != '0':
        return code, msg, rows

    return code, msg, []

#设置港股通优先股东账户
async def adapt500062(ref_request, serviceid, jsonreq):
    if 'ACCOUNT' not in jsonreq:
        return -100, '入参有误：必要的入参[ACCOUNT]不存在', []
    if 'MARKET' not in jsonreq:
        return -100, '入参有误：必要的入参[MARKET]不存在', []

    account = jsonreq['ACCOUNT']
    market = jsonreq['MARKET']
    if len(account) == 0 or len(market) == 0:
        return -200, '入参有误：[MARKET]和[ACCOUNT]不能为空值', []

    # 市场需要转换成对应的柜台字典来保存
    if jsonreq['MARKET'] == '7':
        market = '5'
        jsonreq['STKBD'] = '13' #沪市港股的 交易板块为 13
    elif jsonreq['MARKET'] == '6':
        market = 'S'
        jsonreq['STKBD'] = '03' #沪市港股的 交易板块为 03

    # ******** 查询客户对应的 交易板块 是否具有港股通交易资质
    code, msg, rows = await adaptcommbiz(ref_request, '500060A', jsonreq)
    if code != '0':
        return code, msg, []

    if len(rows) == 0:
        return "-1110", "设置港股通优先股东账户失败：该客户不具有该市场的港股通交易资质", []

    # 查询优先股东账户
    iRet, msg, data = queryPriorMarket(account)
    if iRet != 0:
        return "-1111", msg, []

    if len(data) == 0:
        iRet, msg, data = setPriorMarket(account, market, 0)
    else:
        iRet, msg, data = setPriorMarket(account, market, 1)

    if iRet != 0:
        return "-1112", msg, []

    return '0', "设置港股通优先股东账户成功", []

'''港股通知识测评'''
async def adapt500063(ref_request, serviceid, jsonreq):

    jsonreq['ANS_STATUS'] = '1'
    jsonreq['SURVEY_SN'] = '24'
    code, msg, rows = await adaptcommbiz(ref_request, '190086', jsonreq)
    if code != '0':
        return code, msg, rows
    jsonreq['ANS_STATUS'] = '2'
    jsonreq['SURVEY_SN'] = '24'
    jsonreq['RIST_ASSESSMENT'] = ''
    code, msg, rows = await adaptcommbiz(ref_request, '190086', jsonreq)
    if code != '0':
        return code, msg, rows

    ans = []
    ansrow = {}
    '''判断知识测评是否是 80分以上'''
    for eachrow in rows:
        eachrow.get('SURVEY_SCORE','')
        if float(eachrow['SURVEY_SCORE']) < 80.0:
            ansrow['BASIC_FLAG'] = '0'
        else:
            ansrow['BASIC_FLAG'] = '1'
    ans.append(ansrow)

    return code, msg, ans

# 发送短信验证码
async def adapt800011(ref_request, serviceid, jsonreq):
    if 'BIZ_TYPE' not in jsonreq:
        return -100, '入参有误：必要的入参[BIZ_TYPE]不存在', []
    strBizType = jsonreq['BIZ_TYPE'] #获取业务类型字段的值
    strIdType = jsonreq['ID_TYPE'] #获取证件类型
    strCardId = jsonreq['ID'] #获取证件号码
    strTelNo = jsonreq['M_TEL']  # 获取手机号码

    szMobileNo = ''
    szIdType = ''
    szIdNo = ''
    # BIZ_TYPE=6时 LOGIN_CODE字段可为空，其他必填
    if strBizType != '6':
        #查询客户预留的手机号码、证件类型和证件号码
        code, msg, rows = await adaptcommbiz(ref_request, '410321', jsonreq)
        if code != '0':
            return code, msg, rows

        for eachrow in rows:
            szMobileNo = eachrow['mobileno']
            szIdType = eachrow['idtype']
            szIdNo = eachrow['idno']

    # BIZ_TYPE校验
    # 0 - 若为修改密码 则通过之前获取到的手机号码直接发送
    # 1 - 若为重置密码前的校验
    # 2 - 新增银行卡
    # 3 - 修改手机号
    # 4 - 仅修改资金密码
    # 5 - 仅修改交易密码
    # 6 - 普通发送短信验证码
    if strBizType == '1' or strBizType == '2':
        #需要校验证件号码 和 证件类型
        if strCardId == '':
            return '-80001101', '身份号码不能为空', []
        if strIdType == '':
            return '-80001102', '身份类型不能为空', []
        if strCardId != szIdNo:
            return '-80001103', '身份验证失败:证件号码与预存不符', []
        if strIdType != szIdType:
            return '-80001104', '身份验证失败:证件类型与预存不符', []

        #需要校验手机号码
        if strTelNo == '':
            return '-80001002', '输入的手机号码不能为空', []
        elif strTelNo != szMobileNo:
            return '-80001105', '输入的手机号码与已预存手机号码不一致', []

    if strBizType == '3' or strBizType == '6':
        if strTelNo == '':
            return '-80001002', '输入的手机号码不能为空', []

    strMobileNo = '' #用于保存往哪个手机号码发送短信验证码
    if strTelNo != '':
        strMobileNo = strTelNo #往传入的手机号码发送
    else:
        strMobileNo = szMobileNo #往预留的手机号码发送

    # 入参为： 消息id， 请求串， 手机号码
    code, msg, rows = await MakeTecentCheckCode(ref_request, jsonreq, strMobileNo)
    if code != '0':
        return code, msg, rows

    return '0', '短信验证码发送成功：' + msg, rows

# 开通科创版权限
async def adapt600049(ref_request, serviced, jsonreq):
    # 资金密码校验
    jsonreq["LOGIN_CODE"] = jsonreq["ACCOUNT"]
    jsonreq['USER_PWD'] = jsonreq['TRD_PWD']
    code, msg, rows = await adaptcommbiz(ref_request, '600011', jsonreq)
    if code != '0':
        return code, msg, rows

    # 近20日资产校验
    code, msg, rows = await adaptcommbiz(ref_request, '********', jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        if float(eachrow['ASSERT_AVG_VAL']) < 500000:
            return '-********', '近20日均资产低于50万', []

    # 签署上交所科创板风险揭示书
    jsonreq['CONTRACT_CLS'] = 'ZY9'
    jsonreq['SIGN_TEXT'] = 'BCF594E9BE5AD34EE42739294BC2488A'
    jsonreq['SIGN_VER'] = '1.10'
    code, msg, rows = await adaptcommbiz(ref_request, '********', jsonreq)
    if code != '0':
        return code, msg, rows

    # 开通科创版权限
    code, msg, rows = await adaptcommbiz(ref_request, '********', jsonreq)
    if code != '0':
        return code, msg, rows

    return code, "开通科创版权限成功", []


# 委托交易
async def adapt600140(ref_request, serviced, jsonreq):
    if 'TRD_ID' not in jsonreq.keys():
        return '-100', '入参缺少必要参数', []

    trd_id = jsonreq['TRD_ID']

    if trd_id in ['3B','3S']:
        code, msg, rows = await adaptcommbiz(ref_request, '600140A', jsonreq)
    else:
        code, msg, rows = await adaptcommbiz(ref_request, '600140', jsonreq)
    return code, msg, rows

# 委托交易
async def adapt600141(ref_request, serviced, jsonreq):
    send_dict = {
        'client_id': WSGetCustid([jsonreq['SESSION_JSON']]),
        'app_id': g_GJ_token_config['app_id'],
        'account': jsonreq['ACCOUNT'],
        'password': WSMultiEncodeByChannel([WSGetTrdpwd([jsonreq['SESSION_JSON']]), jsonreq['USER_ID_CLS']]),
        'expired_time': g_redis_expiretime,
        'account_type': g_GJ_token_config['account_type'],
    }
    code, msg, ans = await http_post(dict(url=g_GJ_token_config['global_url'],payload=send_dict), ref_request, 0)
    if code != '0':
        return code, msg, ans
    ans = json.loads(ans)
    if ans['code'] != 0:
        return ans['code'], ans['message'], []

    send_dict = {
        'client_id': WSGetCustid([jsonreq['SESSION_JSON']]),
        'branch_no': WSGetOrgid([jsonreq['SESSION_JSON']]),
        'fund_account': jsonreq['ACCOUNT'],
        'cipher_token': ans['result']['cipherToken'],
        'product_code': jsonreq['BALANCE_CODE'],
    }
    # 调用佣金宝接口判断新客产品互斥条件，若客户已购买过新客理财产品，则不允许下单
    code, msg, ans = await http_post(dict(url=BJHG_CHECK_STATUS_URL,payload=send_dict), ref_request)
    if code != '0':
        return code, msg, ans
    ans = json.loads(ans)
    if ans['error_no'] != '0':
        return '-1200', ans['error_info'], []
    if str(ans['data']['checkStatus']) == '0':
        return '-1201', ans['data']['reason'], []

    code, msg, rows = await adaptcommbiz(ref_request, '600141', jsonreq)
    return code, msg, rows


# 委托撤单
async def adapt600150(ref_request, serviced, jsonreq):
    if 'TRD_ID' not in jsonreq.keys():
        return '-100', '入参缺少必要参数', []

    trd_id = jsonreq['TRD_ID']

    if trd_id in ['3B','3S']:
        code, msg, rows = await adaptcommbiz(ref_request, '600150A', jsonreq)
    else:
        code, msg, rows = await adaptcommbiz(ref_request, '600150', jsonreq)
    return code, msg, rows


# 用户区间资金信息查询
async def adapt600123(ref_request, serviced, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '********', jsonreq)
    if code != '0':
        return code, msg, rows

    for eachrow in rows:
        eachrow['ACCOUNT'] = jsonreq['ACCOUNT']

    return code, msg, rows


# 可转出资金信息查询 DH edit
async def adapt600124(ref_request, serviced, jsonreq):
    code, msg, rows = await adaptcommbiz(ref_request, '600124', jsonreq)
    if code != '0':
        return code, msg, rows

    new_stock_pay = 0
    ans = []

    for eachrow in rows:
        new_stock_pay += float(eachrow['lucky_balance'])

    ans.append({'ACCOUNT': jsonreq['ACCOUNT'], 'CURRENCY': jsonreq['CURRENCY'], 'NEW_STOCK_PAY': str(new_stock_pay)})

    return code, msg, ans


'''核身校验'''
async def adapt800012(ref_request, serviceid, jsonreq):
    if 'BIZ_TYPE' not in jsonreq:
        return -100, '入参有误：必要的入参[BIZ_TYPE]不存在', []
    if 'SMS_CODE' not in jsonreq:
        return -100, '入参有误：必要的入参[SMS_CODE]不存在', []
    strBizType = jsonreq['BIZ_TYPE']  # 获取业务类型字段的值
    strIdType = jsonreq['ID_TYPE']  # 获取证件类型
    strCardId = jsonreq['ID']  # 获取证件号码
    strTelNo = jsonreq['M_TEL']  # 获取手机号码
    strSmsCode = jsonreq['SMS_CODE'] # 短信验证码
    # strCustId = jsonreq['LOGIN_CODE'] # 手机号码
    strUserId = jsonreq['USER_ID'] # 获取USER_ID

    # 手机验证码为必填字段
    if strSmsCode == '':
        return '-10', '入参有误：必要的入参[SMS_CODE]不能为空', []

    szMobileNo = ''
    szIdType = ''
    szIdNo = ''
    # BIZ_TYPE=6时 LOGIN_CODE字段可为空，其他必填
    if strBizType != '6':
        # 查询客户预留的手机号码、证件类型和证件号码
        code, msg, rows = await adaptcommbiz(ref_request, '410321', jsonreq)
        if code != '0':
            return code, msg, rows

        for eachrow in rows:
            szMobileNo = eachrow['mobileno']
            szIdType = eachrow['idtype']
            szIdNo = eachrow['idno']

        # 需要校验证件号码 和 证件类型
        if strCardId == '':
            return '-80001101', '身份号码不能为空', []
        if strIdType == '':
            return '-80001102', '身份类型不能为空', []
        if strCardId != szIdNo:
            return '-80001103', '身份验证失败:证件号码与预存不符', []
        if strIdType != szIdType:
            return '-80001104', '身份验证失败:证件类型与预存不符', []

        if strBizType == '1':
            # 需要校验手机号码
            if strTelNo == '':
                return '-80001102', '输入的手机号码不能为空', []
            elif strTelNo != szMobileNo:
                return '-80001103', '输入的手机号码与已预存手机号码不一致', []

        # strBizType=02345 而且手机号码没送的情况
        if strTelNo == '':
            strTelNo = szMobileNo
    else:
        if strTelNo == '':
            return '-80011102', '输入的手机号码不能为空', []

    # 校验手机验证码的正确性
    # 根据环境配置参数控制是否启用万能验证码， test: 启用万能验证码; prod: 不启用万能验证码
    if g_environment != 'test':
        storecode = GetRedisKey(strTelNo)
        if storecode == None:
            return '-11', '验证码错误', []
        if storecode.decode("utf-8") != strSmsCode:  # 校验不通过
            return '-11', '验证码错误', []
    else:
        if strSmsCode != '600109':
            storecode = GetRedisKey(strTelNo)
            if storecode == None:
                return '-11', '验证码错误', []
            if storecode.decode("utf-8") != strSmsCode:  # 校验不通过
                return '-11', '验证码错误', []

    # 生成XID_SESSION
    strXID_SESSION = ''
    strXID_SESSION = MakeXIDSessionString(strUserId)
    if strXID_SESSION == '':
        return '-80001209', 'XID_SESSION生成失败', []

    return '0', '身份核对成功', [{'XID_SESSION':strXID_SESSION}]

# 更新客户资料（手机号码、职业和学历等信息）
async def adapt600047(ref_request, serviceid, jsonreq):
    if 'M_TEL' not in jsonreq:
        return -100, '入参有误：必要的入参[M_TEL]不存在', []
    if 'SMS_CODE' not in jsonreq:
        return -100, '入参有误：必要的入参[SMS_CODE]不存在', []
    strMtel = jsonreq['M_TEL']
    strSmsCode = jsonreq['SMS_CODE']
    strOccuType = jsonreq['OCCU_TYPE']
    strEducation = jsonreq['EDUCATION']

    if strMtel == '' and strEducation == '' and strOccuType == '':
        return '-6000470', '更新账户信息失败：没有需要更改的信息', []

    #如果手机号码不为空，则表示需要更新手机号码，则需要校验短信验证码
    if strMtel != '':
        if strSmsCode == '':
            return  '-6000471', '更新账户信息失败：验证码错误', []
        # 校验手机验证码的正确性
        storecode = GetRedisKey(strMtel)
        if storecode == None:
            return '-6000472', '更新账户信息失败：验证码错误', []
        if storecode.decode("utf-8") != strSmsCode:  # 校验不通过
            return '-6000472', '更新账户信息失败：验证码错误', []

    #如果电话号码或者学历需要更新
    if strMtel != '' or strEducation != '':
        code, msg, rows = await adaptcommbiz(ref_request, '600047A', jsonreq)
        if code != '0':
            return code, msg, rows

    #如果职业字段也需要更新
    if strOccuType != '':
        code, msg, rows = await adaptcommbiz(ref_request, '600047B', jsonreq)
        if code != '0':
            return code, msg, rows

    return '0', '更新账户信息成功', []

#修改密码，修改交易密码或者资金密码
async def adapt800013(ref_request, serviceid, jsonreq):
    if 'XID_SESSION' not in jsonreq:
        return -100, '入参有误：必要的入参[XID_SESSION]不存在', []
    strXidSession = jsonreq['XID_SESSION']
    strUserId = jsonreq['USER_ID']
    strUserCode = jsonreq['LOGIN_CODE']

    #检查XID_SESSION的正确性
    code, msg = CheckXIDSessionString(strUserId, strXidSession)
    if code != '0':
        return code, msg, []

    strNewTrdPwd = jsonreq['NEW_TRD_PWD'] #获取入参中新的交易密码
    strNewCapitialPwd = jsonreq['NEW_CAPITAL_PWD'] #获取入参中新的资金密码
    if strNewTrdPwd == '' and strNewCapitialPwd == '':
        return '-130103', '修改密码操作失败:没有需要修改的密码', []

    strOldTrdPwd = jsonreq['OLD_TRD_PWD']  # 获取入参中旧的交易密码
    strOldCapitialPwd = jsonreq['OLD_CAPITAL_PWD']  # 获取入参中旧的资金密码
    if strOldTrdPwd != '':
        if strNewTrdPwd == '':
            return '-130104', '修改密码操作失败:未送入新的交易密码', []
        #调用99000258接口修改交易密码
        jsonreq['OPERATION_TYPE'] = '1'
        jsonreq['USER_ROLE'] = '1'
        jsonreq['AUTH_TYPE'] = '0'
        jsonreq['OP_REMARK'] = ''
        jsonreq['USER_CODE'] = strUserCode
        jsonreq['USE_SCOPE'] = '0'
        jsonreq['OLD_AUTH_DATA'] = jsonreq['OLD_TRD_PWD']
        jsonreq['NEW_AUTH_DATA'] = jsonreq['NEW_TRD_PWD']
        code, msg, rows = await adaptcommbiz(ref_request, '99000258', jsonreq)
        if code != '0':
            return code, msg, rows

    if strOldCapitialPwd != '':
        if strNewCapitialPwd == '':
            return '-130105', '修改密码操作失败:未送入新的资金密码', []
        # 调用99000258接口修改资金密码
        jsonreq['OPERATION_TYPE'] = '1'
        jsonreq['USER_ROLE'] = '1'
        jsonreq['AUTH_TYPE'] = '0'
        jsonreq['OP_REMARK'] = ''
        jsonreq['USER_CODE'] = strUserCode
        jsonreq['USE_SCOPE'] = '1'
        jsonreq['OLD_AUTH_DATA'] = jsonreq['OLD_CAPITAL_PWD']
        jsonreq['NEW_AUTH_DATA'] = jsonreq['NEW_CAPITAL_PWD']
        code, msg, rows = await adaptcommbiz(ref_request, '99000258', jsonreq)
        if code != '0':
            return code, msg, rows

    return '0', '修改密码成功', []

#港股通委托撤单
async def adapt601150(ref_request, serviceid, jsonreq):
    #调用430012 查询可撤单委托
    code, msg, rows = await adaptcommbiz(ref_request, '601150A', jsonreq)
    if code != '0':
        return code, msg, rows

    ans = []
    ansrow = {}
    szOrderDate = ''
    for eachrow in rows:
        szOrderDate = eachrow['orderdate']

    if szOrderDate == '':
        return '-6011501', '港股通委托撤单失败：不存在该笔委托或该笔委托当前不可撤单', []

    jsonreq['orderdate'] = szOrderDate
    # 调用430011 进行撤单
    code, msg, rows = await adaptcommbiz(ref_request, '601150B', jsonreq)
    if code != '0':
        return code, msg, rows

    return '0', '港股委托撤单成功', []

async def queryIsHaveOpenAtCSDC(ref_request, jsonreq):
    # 查询中登客户是否签署了创业板协议
    # 99000384（代理中登业务）获取流水号
    code, msg, rows = await adaptcommbiz(ref_request, '99000384', jsonreq)
    if code != '0':
        return code, msg, rows

    szSerialNo = ''
    for eachrow in rows:
        szSerialNo = eachrow['SERIAL_NO']
    if szSerialNo == '':  # 获取的流水号为空
        return '-4000003', '调用99000384查询中登失败', []

    szAcctBizStatus = ''
    bIsHaveOpenAtCSDC = False  # 默认中登未签署创业板
    jsonreq['SERIAL_NO'] = szSerialNo  # 获取流水号查询中登的处理结果
    reqtimes = 0
    code = '-100'
    while reqtimes <= 35:
        reqtimes = reqtimes + 1
        code, msg, rows = await adaptcommbiz(ref_request, '99000385', jsonreq)
        if code != '0':
            # return '-4000004', '调用99000385查询中登失败', []
            time.sleep(0.2)
            continue
        for eachrow in rows:
            szAcctBizStatus = eachrow['ACCTBIZ_STATUS']
        if szAcctBizStatus == '3':
            bIsHaveOpenAtCSDC = False
            break
        if szAcctBizStatus != '2':
            time.sleep(0.2)
            continue
        bIsHaveOpenAtCSDC = True
        break

    if reqtimes > 35:  # 说明查询中登超过循环次数
        if code != '0':
            return '-4000004', '调用99000385查询中登失败', []
        strmsg = '取流水:%s的中登发送状态ACCTBIZ_STATUS为%s' % (szSerialNo, szAcctBizStatus)
        return '-4000016', strmsg, []

    strUserId = jsonreq['USER_ID']
    if bIsHaveOpenAtCSDC:  # 中登中该客户开通有创业板权限
        code, msg, rows = await adaptcommbiz(ref_request, '99000427', jsonreq)
        if code != '0':
            return code, msg, rows
        szSignCls = ''
        szSignDate = ''
        for eachrow in rows:
            szSignCls = eachrow['SIGN_CLS']
            szSignDate = eachrow['SIGN_DATE']
        if szSignCls == '':
            bIsHaveOpenAtCSDC = False
        else:
            strValue = strUserId + '|' + szSignCls
            # 向redis中存储签署日期和签署类型
            SetRedisKeyEx(strUserId, strValue, g_SignDate_time)

    return '0', '查询成功', [{'bIsHaveOpenAtCSDC':bIsHaveOpenAtCSDC}]

# 查询客户是否具有创业板转签权限
async def adapt600045(ref_request, serviceid, jsonreq):
    #查询客户是否有深A账号
    code, msg, rows = await adaptcommbiz(ref_request, '99000265', jsonreq)
    if code != '0':
        return code, msg, rows

    szSecuid = ''
    szIDType = ''
    szIDCode = ''
    szTrdacctName = ''
    for eachrow in rows:
        if eachrow['STKEX'] == '0':
            szSecuid = eachrow['TRDACCT']
            szIDType = eachrow['ID_TYPE']
            szIDCode = eachrow['ID_CODE']
            szTrdacctName = eachrow['TRDACCT_NAME']

    # 如果客户没有深A股东号，则没有创业板转签权限
    if szSecuid == '':
        # XGEM_STATUS 的值：'0'表示没有转签权限, '1'表示有转签权限
        return '0', '该客户没有深A股东号', [{'XGEM_STATUS':'0'}]

    #查询客户在本地是否开通了创业板权限
    jsonreq['CUST_AGMT_TYPE'] = '0A'
    code, msg, rows = await adaptcommbiz(ref_request, '********', jsonreq)
    if code != '0' and code != '100': #该接口返回的时候，如果没有数据，应答的code等于100
        return code, msg, rows

    bIsHaveOpenSB = False #默认没有创业板转签权限
    for eachrow in rows:
        if eachrow['CUST_AGMT_TYPE'] == '0A':
            bIsHaveOpenSB = True #有创业板权限

    if bIsHaveOpenSB:
        # XGEM_STATUS 的值：'0'表示没有转签权限, '1'表示有转签权限
        return '0', '该客户已经开通创业板，无需创业板转签', [{'XGEM_STATUS':'0'}]

    #查询客户在中登是否签署创业板协议
    jsonreq['CHK_STATUS'] = '2'
    jsonreq['ACCTBIZ_EXCODE'] = '13'
    jsonreq['ACCTBIZ_CLS'] = '03'
    jsonreq['OPERATOR_TYPE'] = '0'
    jsonreq['TRDACCT'] = szSecuid
    jsonreq['ACCT_TYPE'] = '21'
    jsonreq['ID_TYPE'] = szIDType
    jsonreq['ID_CODE'] = szIDCode
    jsonreq['CUST_FNAME'] = szTrdacctName
    bIsHaveOpenAtCSDC = False
    strUserId = jsonreq['USER_ID']
    code, msg, rows = await queryIsHaveOpenAtCSDC(ref_request, jsonreq)
    if code != '0':
        return code, msg, rows
    for eachrow in rows:
        bIsHaveOpenAtCSDC = eachrow['bIsHaveOpenAtCSDC']
    if bIsHaveOpenAtCSDC: #具有创业板转签权限
        strKey = strUserId + 'TAkey'
        # 向redis中存储存储T+A类型
        try:
            SetRedisKeyEx(strKey, "1", g_SignDate_time)
        except Exception as e:
            return '-40019', '查询中登失败:' + str(e), []
        return  '0', '权限查询成功', [{'XGEM_STATUS':'1'}]

    # 中登没有签署创业板协议，则要查询客户在其它券商是否签署创业板协议
    jsonreq['CHK_STATUS'] = '2'
    jsonreq['ACCTBIZ_EXCODE'] = '07'
    jsonreq['ACCTBIZ_CLS'] = '03'
    jsonreq['OPERATOR_TYPE'] = '0'
    jsonreq['ACCT_TYPE'] = '21'
    strCurSecuid = jsonreq['TRDACCT']
    code, msg, rows = await adaptcommbiz(ref_request, '99000384', jsonreq)
    if code != '0':
        return code, msg, rows

    szSerialNo = ''
    for eachrow in rows:
        szSerialNo = eachrow['SERIAL_NO']
    if szSerialNo == '':  # 获取的流水号为空
        return '-4000008', '调用99000384查询中登失败', []
    jsonreq['SERIAL_NO'] = szSerialNo  # 获取流水号查询中登的处理结果
    reqtimes = 0
    code = '-100'
    bIsHaveOpenAtCSDC = False
    vecStkCode = [] #用于存储在其它券商的资金账号
    while reqtimes <= 20:
        reqtimes = reqtimes + 1
        code, msg, rows = await adaptcommbiz(ref_request, '99000385', jsonreq)
        if code != '0':
            # return '-4000004', '调用99000385查询中登失败', []
            time.sleep(0.2)
            continue
        szSecuid = ''
        szAccTye = ''
        iscontinue = False #控制是否继续查询的标识
        for eachrow in rows:
            szAcctBizStatus = eachrow['ACCTBIZ_STATUS']
            if szAcctBizStatus == '3':
                bIsHaveOpenAtCSDC = False
                break
            if szAcctBizStatus != '2': #如果标识位为2，则跳出内循环，再次进行99000395
                time.sleep(0.3)
                iscontinue = True
                break
            szAccTye = eachrow['ACCT_TYPE']
            if szAccTye == '21':
                szSecuid = eachrow['TRDACCT']
                if szSecuid != strCurSecuid:
                    vecStkCode.append(szSecuid)
        if iscontinue:
            continue

    if reqtimes > 20 and len(vecStkCode) == 0:  # 说明查询中登超过循环次数，且没有查询到该客户有其它资金账号
        if code != '0':
            return '-4000009', '调用99000385查询中登失败', []
        strmsg = '取流水:%s的中登发送状态ACCTBIZ_STATUS为%s' % (szSerialNo, szAcctBizStatus)
        return '-4000006', strmsg, []

    #查询该客户在券商的资金账号是否具有创业板权限
    for acct in vecStkCode:
        jsonreq['ACCTBIZ_EXCODE'] = '13'
        jsonreq['TRDACCT'] = acct
        code, msg, rows = await queryIsHaveOpenAtCSDC(ref_request, jsonreq)
        if code != '0':
            return code, msg, rows
        for eachrow in rows:
            bIsHaveOpenAtCSDC = eachrow['bIsHaveOpenAtCSDC']
        if bIsHaveOpenAtCSDC:
            break

    if bIsHaveOpenAtCSDC:  # 具有创业板转签权限
        strKey = strUserId + 'TAkey'
        # 向redis中存储存储T+A类型
        SetRedisKeyEx(strKey, "1", g_SignDate_time)
        return '0', '权限查询成功', [{'XGEM_STATUS': '1'}]

    #返回没有创业板转签权限
    return '0', '权限查询成功', [{'XGEM_STATUS': '0'}]


# 开通新客理财账户
async def adapt800049(ref_request, serviceid, jsonreq):
    #调用430012 查询可撤单委托
    code, msg, rows = await adaptcommbiz(ref_request, '800049', jsonreq)
    if code != '0':
        if code == '144538':
            # 对于144538的错误是已开户的，查询理财账户并返回给腾讯
            code, msg, rows = await adaptcommbiz(ref_request, '800060', jsonreq)
            if code != '0':
                return code, msg, rows
        else:
            return code, msg, rows



    return '0', 'succ', rows


# 查询新客理财产品信息
async def adapt800410(ref_request, serviceid, jsonreq):
    #调用430012 查询可撤单委托
    code, msg, rows = await adaptcommbiz(ref_request, '337400', jsonreq)
    if code != '0':
        return code, msg, rows

    code, msg, rows2 = await adaptcommbiz(ref_request, '337455', jsonreq)
    if code != '0':
        return code, msg, rows2

    for eachrow in rows:
        if len(rows2) > 0 and rows2[0]['BALANCE_CODE'] == eachrow['BALANCE_CODE']:
            eachrow['QTY'] = rows2[0]['QTY']

    return '0', 'succ', rows



# 理财委托
async def adapt800140(ref_request, serviceid, jsonreq):
    # 先做基金电子合同签署
    code, msg, rows = await adaptcommbiz(ref_request, '331407', jsonreq)
    if code != '0' and code != '-61':
        return code, msg, rows

    # 再掉新客理财委托
    code, msg, rows = await adaptcommbiz(ref_request, '337411', jsonreq)

    return code, msg, rows


# 电子协议签署
async def adapt800061(ref_request, serviceid, jsonreq):
    #调用430012 查询可撤单委托
    todayDate = datetime.datetime.now()
    jsonreq['csfc_begin_date'] = todayDate.strftime("%Y%m%d")
    # 往后推50年算法，用日期根据天数往后推。可以避免非法日期，比如：2074.02.29
    jsonreq['csfc_end_date'] = (todayDate + datetime.timedelta(days=50*365+int(50/4))).strftime("%Y%m%d")
    if jsonreq['TYPE'] == '0':
        # 开户协议
        # 需要调两次331203接口，第一次是留痕须知，第二次是留痕协议
        neetKnowText = '%s;证券理财开户须知;%s;已签署'%(jsonreq['BALANCE_CODE'], jsonreq['TCC'])
        agreementText = '%s;证券理财开户协议;%s;已签署' % (jsonreq['BALANCE_CODE'], jsonreq['TCC'])
        jsonreq['sdc_account'] = jsonreq['ACCOUNT']
        jsonreq['acct_exch_type'] = 'Z01'
        jsonreq['agree_type'] = '!'
        jsonreq['trans_account'] = jsonreq['ACCOUNT']
        jsonreq['sub_risk_flag'] = 'a'
        jsonreq['risk_level'] = ''
        jsonreq['agree_model_no'] = '****************'
        jsonreq['prodta_no'] = ''
        jsonreq['prod_code'] = ''
        jsonreq['remark'] = neetKnowText
        code, msg, rows = await adaptcommbiz(ref_request, '331203', jsonreq)
        if code != '0':
            return code, msg, rows
        jsonreq['remark'] = agreementText
        code, msg, rows = await adaptcommbiz(ref_request, '331203', jsonreq)
        return code, msg, rows

    elif jsonreq['TYPE'] == '1':
        # 交易协议
        #交易协议的，要先调331201，获取acct_exch_type
        code, msg, rows = await adaptcommbiz(ref_request, '331201', jsonreq)
        if code != '0':
            return code, msg, rows

        remark = '%s;电子合同;%s;已签署'%(jsonreq['BALANCE_CODE'], jsonreq['TCC'])
        ans = []
        for row in rows:
            jsonreq['sdc_account'] = jsonreq['ACCOUNT']
            jsonreq['acct_exch_type'] = row['acct_exch_type']
            jsonreq['agree_type'] = '('
            jsonreq['trans_account'] = jsonreq['ACCOUNT']
            jsonreq['sub_risk_flag'] = 'a'
            jsonreq['risk_level'] = ''
            jsonreq['agree_model_no'] = row['agree_model_no']
            jsonreq['prodta_no'] = 'CZZ' # row['prodta_no']
            jsonreq['prod_code'] = row['prod_code']
            jsonreq['remark'] = remark
            code, msg, ans = await adaptcommbiz(ref_request, '331203', jsonreq)
            if code != '0':
                return code, msg, ans

        return code, msg, ans
    else:
        return '-1203', '无效TYPE', []


if __name__ == "__main__":
    #src_serviceid,reqDict,dst_reqdict
    #测试410301
    reqdict = {"inputtype": "Z", "inputid": "***********", "trdpwd":"0000000000000000000000000000000000000000000000000000000000008209bcbff97c2d05de7d106ff9ce10e3cc90541c224d458d311a93894c389f84c5858601ef96dd56a50807980f15b254580ea2aa990ee960423ed35981f13d0453ede849e575ea25a9f50b0766224c435c70c00dd9f5051423240cc1073ee7821f211407af6a3c355c63585491616fc3978411500aa5a798f3f680d03db18868"}
    serviceid = '410301'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试410301结束')

    SESSION_JSON = '{"1": [{"4": "***********", "7": "2017-03-14 09:17:53", "3": "***********", "6": "6560304d0e52aceea4c8fde6bab4b5418a258c2e4477bdc9682e23138ab0815e81dc43133f1acc71ab20f24311d3b913a04e3ee50f65760b52abe85d812dc84c682a8249efb02e140dc483fa7ca16e08d86ab98a9e668a6a62ac11fa7d577e833a631ef6d22f19711ceaf9d648d269b865c7f19abc233e668456e23f71af50b5", "8": [{"9": "0", "a": "**********"}, {"9": "1", "a": "A151442824"}], "2": 12900, "5": "0101"}], "0": *********, "Q": "test"}'
    #测试600010
    reqdict = {}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600010'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600010结束')

    #todo测试ACCOUNT字段
    reqdict = {}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600120'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600120结束')

    #测试600130
    reqdict = {}
    reqdict = {'MARKET':'0','SECU_CODE':'000002','TRD_ID':'0B','ORDER_PRICE':'12.00'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600130'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600130结束')

    #测试600140
    reqdict = {}
    reqdict = {'MARKET':'0','SECU_CODE':'000002','TRD_ID':'0B','ORDER_PRICE':'12.00','ORDER_QTY':'100'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600140'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600140结束')

    #测试600150
    reqdict = {}
    reqdict = {'ORDER_ID':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600150'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600150结束')

    #测试600160
    reqdict = {}
    reqdict = {'REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600160'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600160结束')

    #测试600170
    reqdict = {}
    reqdict = {'BEGIN_DATE':'20170303','END_DATE':'20170316','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600170'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600170结束')

    #测试600180
    reqdict = {}
    reqdict = {'REC_COUNT':'240'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600180'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600180结束')

    #测试600190
    reqdict = {}
    reqdict = {'BEGIN_DATE':'20170303','END_DATE':'********','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600190'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600190结束')

    #测试600200
    reqdict = {}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600200'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600200结束')

    #测试600210
    reqdict = {}
    reqdict = {'CPTL_AMT':'2000','END_DATE':'********','REC_COUNT':'24','EXT_INST': '1007'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600210'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600210结束')

    #测试600220
    reqdict = {}
    reqdict = {'CPTL_AMT':'10','END_DATE':'********','REC_COUNT':'24','EXT_INST': '1007'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600220'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600220结束')

    #测试600230
    reqdict = {}
    reqdict = {'CPTL_AMT':'10','END_DATE':'********','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600230'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600230结束')

    #测试600270
    reqdict = {}
    reqdict = {'BEGIN_DATE':'********','END_DATE':'********','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600270'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600270结束')

    #测试600360
    reqdict = {}
    reqdict = {'BEGIN_DATE':'********','END_DATE':'********','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600360'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600360结束')

    #测试600402
    reqdict = {}
    reqdict = {'BEGIN_DATE':'********','END_DATE':'********','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600402'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600402结束')

    #测试600403
    reqdict = {}
    reqdict = {'BEGIN_DATE':'********','END_DATE':'********','REC_COUNT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600403'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600403结束')

    #测试600405
    reqdict = {}
    reqdict = {'IN_ACCOUNT':'***********','OUT_ACCOUNT':'***********','CPTL_AMT':'24'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600405'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600405结束')

    #测试600406
    reqdict = {}
    reqdict = {'BEGIN_DATE':'********','END_DATE':'********','IN_ACCOUNT':'***********'}
    reqdict['SESSION_JSON'] = SESSION_JSON
    serviceid = '600406'
    print(adaptcommbiz(serviceid,reqdict))
    print('测试600406结束')
