
#logformat
#  1 : logging.Formatter('[%(asctime)s][file:%(filename)s][line:%(lineno)d][%(thread)d]: %(message)s')
#  2 : logging.Formatter('[%(asctime)s][line:%(lineno)d][%(thread)d]: %(message)s')
#  3 : logging.Formatter('[%(asctime)s][file:%(filename)s][line:%(lineno)d]: %(message)s')
#  4 : logging.Formatter('[%(asctime)s][file:%(filename)s]: %(message)s')
#  5 : logging.Formatter('[%(asctime)s]: %(message)s')
#  6 : logging.Formatter('%(message)s')


#loglevel
#  1 : logging.DEBUG
#  2 : logging.INFO
#  3 : logging.WARNING
#  4 : logging.ERROR
#  5 : logging.CRITICAL

g_kwl_log_config = {
    'loglevel' : 1 ,            #日志级别，阈值
    'logformat' : 2 ,           #日志格式
    'maxBytes' : 100*1024*1024,  #单个日志文件大小
    'backupCount' : 10000,      #保留日志文件个数
    'encoding' : 'utf-8',       #日志文件编码
    'filepath' : '..\pylogs'     #日志文件保存目录，支持相对路径和绝对路径
}

