# -*- coding: utf-8 -*-
from HS import *
import threading
import time
#from KWL_Nginx import *
bizid = 333101
reqJson =  "{\"biznum\" : \"333101\", \"op_branch_no\" : \"5010\",\"op_entrust_way\" : \"h\",\"op_station\" : \"127.0.0.1\",\"branch_no\" : \"5010\",\"client_id\" : \"********\",\"fund_account\" : \"********\",\"password\" : \"111111\",\"sysnode_id\":\"2\" }"



class AsynMultiComm:
    def __init__(self, configFileName):
        self.hs = CHST2Wrapper()
        self.hs.init(configFileName)
        self.hs.Connect(True)
        self.sendDict = {}
        self.recvThread = 0
        self.lock = threading.Lock()

    def isConnect(self):
        return self.hs.IsConnect()

    def threadStart(self, hSend):
        self.recvThread = threading.Thread(target=self.Recv,  args=(hSend,))
        self.recvThread.start()
        self.recvThread.join()


    def Send(self, bizid,reqJson):
        hSend = self.hs.AsynSend(bizid,reqJson)

        if hSend > 0:
            self.lock.acquire()
            self.sendDict[hSend] = ""
            self.lock.release()
            return hSend
        else:
            return -1

    def Recv(self, hSend):
        flag = True
        beforeTime = time.time()*1000

        while flag:
            self.lock.acquire()

            ansJson = self.hs.AsynRecv()
            if ansJson.find("No such index") != -1:
                pass
            elif ansJson == "":
                pass
            else:
                #print("################### ansJSon = %s"%(ansJson))
                self.sendDict[hSend] = ansJson
                flag = False

            if time.time()*1000 - beforeTime > 1000:
                self.sendDict[hSend] = "timeout"
                flag = False

            self.lock.release()


instance = AsynMultiComm('T2Config.ini')
print('connect is %s'%(instance.isConnect()))


def asyn(ref_key, bizid,request):

    hSend = instance.Send(bizid,request)
    if hSend <= 0:
        #print("send error")
        #SendREsponse(ref_key, "send error, hSend < 0")
        return(0, "error", "send error, hSend < 0")

    instance.threadStart(hSend)

    ans = instance.sendDict[hSend]
    if ans == "":
        print("None")

    print(ans)
    #SendResponse(ref_key, ans)


    return(0, "", "hello world!")

req = '''{
    "registe_sure_flag":"",
    "custid":"",
    "client_id":"********",
    "passwd_tmp":"356b6b76bea5154f",
    "password_type":"2",
    "entrust_no":"1",
    "branch_no":"33",
    "op_entrust_way":"h",
    "orderdate":"0",
    "op_station":"127.0.0.1",
    "COMPANY_ID":"12900",
    "password":"111111",
    "fund_account":"********",
    "SESSION":"e8e52a3da0bf1647ef3a0c797732e35aa83412682668062fd7ff89305bb0631e92727d9e78fa6e2581dc6bc33aad1d0cffc00dc496bc549aec8853b8f8781517af8c00568c566345295817c7ccd776ab01d054d866c5374a22e81533de2e814094793a6270aa7b02e26935e1500177ac1bc35d894c27fb266a0a2a21c687de01",
    "user_token":"",
    "batch_flag":"",
    "entrust_date":"********",
    "sysnode_id":"2",
    "exchange_type":"2",
    "op_branch_no":"5010"
}'''

req1 = '''{
    "biznum":"331460",
    "op_branch_no":"5010",
    "op_entrust_way":"h",
    "op_station":"127.0.0.1",
    "branch_no":"5010",
    "client_id":"********",
    "fund_account":"********",
	"mobile_tel":"***********",
    "password":"111111",
    "sysnode_id":"2"
}'''

req_331100 = '''{"biznum" : "331100", "op_branch_no" : "5010","op_entrust_way" : "h","op_station" : "127.0.0.1","password" : "259800","input_content" : "1","account_content" : "********","content_type" : "0" }'''
if __name__ == '__main__':
    asyn("",331100, req_331100)
