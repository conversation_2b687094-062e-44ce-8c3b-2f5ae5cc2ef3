#!/usr/bin/python
# -*- coding: utf-8 -*-
from aiokafka import AIOKafkaProducer
from aiokafka.conn import create_conn
import KinginxSys
import asyncio
from common_config import *
import time
import json
from kwlpyfunction import *
from kwl_py_log import *


# kafka生产者
g_kafka_producer = {}

def write_msg(topic, msg):
    # 发送失败的kafka写日志，在别的地方起个线程去轮询，等kafka启动后再把日志内容发往kafka
    write_dict = {}
    write_dict['time'] = time.strftime('%Y-%m-%d %H:%M:%S')
    write_dict['topic'] = topic
    write_dict['msg'] = msg
    with open(g_kafka_msg_log_path, 'a', encoding = 'utf8') as file_object:
        file_object.write(json.dumps(write_dict) + '\n')



async def try_connect_kafka(host, port, loop):
    try:
        # 由于kafka直接使用producer初始化链接不了的异常捕获不了
        # 所以在这里做一个尝试链接，链接失败就会抛出，证明kafka服务不可用
        bootstrap_conn = await create_conn(host, port, loop=loop)
        bootstrap_conn.close()
        # 尝试链接成功后，关掉此链接，使用aiokafkaProducer做后续的操作

        host_port = '%s:%s' % (host, port)
        product = AIOKafkaProducer(loop=loop, bootstrap_servers=host_port)
        g_kafka_producer['producer'] = product
        await g_kafka_producer['producer'].start()
    except (OSError, asyncio.TimeoutError) as err:
        kwl_py_write_log('try_connect_kafka链接超时失败:' + repr(err), 'try_connect_kafka', ZTLOG_ERROR, msgid='send_kafka')
    except Exception as e:
        kwl_py_write_log('try_connect_kafka异常:' + repr(e), 'try_connect_kafka', ZTLOG_ERROR, msgid='send_kafka')


def init_kafka():
    asyncio.run_coroutine_threadsafe(try_connect_kafka(g_kafka_config['host'], g_kafka_config['port'],
                                                       KinginxSys.loopInstance),  KinginxSys.loopInstance)



async def send_kafka(topic, msg):
    if g_kafka_producer:
        try:
            # 异步发送交易数据到kafka
            await g_kafka_producer['producer'].send_and_wait(topic, msg.encode('utf8'))
            kwl_py_write_log('发送kafka数据:topic:[%s], msg:[%s]'%(topic, msg) , 'send_kafka', ZTLOG_INFO, msgid='send_kafka')
        except Exception as e:
            kwl_py_write_log('发送kafka失败:' + repr(e), 'send_kafka', ZTLOG_ERROR, msgid='send_kafka')
            write_msg(topic, msg)
            # aiokafka会自动重连，所以这里就不需要再手动重连了
            #init_kafka()
    else:
        write_msg(topic, msg)
        init_kafka()
