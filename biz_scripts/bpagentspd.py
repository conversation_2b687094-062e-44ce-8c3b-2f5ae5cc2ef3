
from bpagentBiz import *
# 功能号对应的脚本函数
g_reqBpagentFunctionDict = {
    # '600001':pyreqbpagent600001_hl,
    # '600002':pyreqbpagent600002_hl,
    '600001': newpyreqbpagent600001_hl,
    '600002': newpyreqbpagent600002_hl,
    '600003': newpyreqbpagent600003_hl,
    '600010': pyreqbpagent600010_hl,
    '900010': pyreqbpagent900010_hl,
    '600011': pyreqbpagent600011_hl,
    '600140': pyreqbpagentCommquery,
    '600141': pyreqbpagentCommquery,
    '600410': pyreqbpagentCommquery,
    '600143': pyreqbpagentCommquery,
    '600150': pyreqbpagentCommquery,
    '600043': pyreqbpagentCommquery,
    '600045': pyreqbpagentCommquery,
    '600046': pyreqbpagentCommquery,
    '600020': pyreqbpagentCommquery,
    '600049': pyreqbpagentCommquery,
    '600050': pyreqbpagentCommquery,
    '600060': pyreqbpagentCommquery,
    '600070': pyreqbpagentCommquery,
    '600080': pyreqbpagentCommquery,
    '600200': pyreqbpagentCommquery,
    '600120': pyreqbpagentCommquery,
    '600123': pyreqbpagentCommquery,
    '600124': pyreqbpagentCommquery,
    '600160': pyreqbpagentCommquery,
    '600161': pyreqbpagentCommquery,
    '600170': pyreqbpagentCommquery,
    '600172': pyreqbpagentCommquery,
    '600174': pyreqbpagentCommquery,
    '600180': pyreqbpagentCommquery,
    '600041': pyreqbpagentCommquery,
    '600042': pyreqbpagentCommquery,
    '600230': pyreqbpagentCommquery,
    '600030': pyreqbpagentCommquery,
    '600270': pyreqbpagentCommquery,
    '600320': pyreqbpagentCommquery,
    '600340': pyreqbpagentCommquery,
    '600380': pyreqbpagentCommquery,
    '600390': pyreqbpagentCommquery,
    '600391': pyreqbpagentCommquery,
    '600190': pyreqbpagentCommquery,
    '600040': pyreqbpagent600040,
    '600055': pyreqbpagentCommquery,
    '600061': pyreqbpagentCommquery,
    '600063': pyreqbpagentCommquery,
    '600064': pyreqbpagentCommquery,
    '600330': pyreqbpagentCommquery,
    '600360': pyreqbpagentCommquery,
    '700713': pyreqbpagentCommquery,
    '700714': pyreqbpagentCommquery,
    '601160': pyreqbpagentCommquery,
    '601170': pyreqbpagentCommquery,
    '601180': pyreqbpagentCommquery,
    '601190': pyreqbpagentCommquery,
    '500401': pyreqbpagentCommquery,
    '500402': pyreqbpagentCommquery,
    '600121': pyreqbpagentCommquery,
    '500403': pyreqbpagentCommquery,
    '500062': pyreqbpagentCommquery,
    '500060': pyreqbpagentCommquery,
    '500061': pyreqbpagentCommquery,
    '500063': pyreqbpagentCommquery,
    '800011': pyreqbpagentCommquery,
    '800012': pyreqbpagentCommquery,
    '800013': pyreqbpagentCommquery,
    '800014': pyreqbpagentCommquery,
    '800015': pyreqbpagentCommquery,
    '600044': pyreqbpagentCommquery,
    '600047': pyreqbpagentCommquery,
    '601140': pyreqbpagentCommquery,
    '601150': pyreqbpagentCommquery,
    '600210': pyreqbpagentCommquery,
    '600220': pyreqbpagentCommquery,
    '600052': pyreqbpagentCommquery,
    '600026': pyreqbpagentCommquery,
    '600401': pyrlengthenSession,

    '600090': pyreqInsertNotice,  # 插入券商公告
    '600091': pyreqSelectNotice,  # 查公告
    '600400': pyreqSelectNotice_tx,  # 腾讯查公告
    '600098': pyreqDeleteNotice,  # 删除券商公告
    '600099': pyreqUpdateNotice,  # 修改券商公告
    '700001': sessionExchangeToken,  # session换国金网厅token
    'testforsession': pyreqtest,
    'getclientip': pyreqgetclientip,  # 获取客户ip信息
    'GetNginxInfo': pyreqgetNginxInfo,  # 获取应答字典残留应答
    'ClearNginxXpAns': pyreqClearNginxXpAns,  # 清空残留应答

    '400001': pyreqQueryAccount,      # 查询客户号 400001 测试环境专用
    '400002': pyreqQueryUserId,       # 查询腾讯id 400002 测试环境专用
    '400020': pyreqCheckSmsCode,       # 查询验证码 400020 测试环境专用

    # 新客理财
    '800060': pyreqbpagentCommquery,       # 查询TA账户信息
    '800049': pyreqbpagentCommquery,       # 开通TA账户
    '800063': pyreqbpagentCommquery,       # 理财产品适当性匹配查询
    '800064': pyreqbpagentCommquery,       # 签署接口
    '800410': pyreqbpagentCommquery,       # 查询新客理财产品信息
    '600171': pyreqbpagentCommquery,       # 持仓记录查询
    '600173': pyreqbpagentCommquery,       # 持仓记录查询
    '800140': pyreqbpagentCommquery,       # 新增理财委托接口
    '800061': pyreqbpagentCommquery,       # 协议留痕接口
    '800160': pyreqbpagentCommquery,       # 委托记录查询接口

    '600034': pyreqbpagentCommquery,       # 查询风险股票提示信息

}
