import aiohttp
import asyncio
from asyncio import *
import json
from urlconfig import *
from kwlpyfunction import *
from xml.dom.minidom import parseString
import traceback
import random
from msgmodel import *
from tyrzconfig import SetRedisKeyEx
import requests
from kwl_py_log import *

if isLinuxSystem():
    from KWL_Nginx import *
else:
    def GetKWLParam(sKey):
        return '99999'
    def IsNginxOn():
        return True

currenttasknum = 0

#控制并发数
@coroutine
def controltask():
    global currenttasknum
    while True:
        # todo根据端口是否有可用来决定
        if currenttasknum < 10:
            return
        yield

#普通调Kow业务
@coroutine
def kowcommbiz(ref_request, serviceid, jsonreq):
    global currenttasknum
    #InitJYThread(msgRecverWinConnect)
    strjsonreq = json.dumps(jsonreq, ensure_ascii=False)

    code = '-100'
    msg = '接口%s调用失败' % serviceid
    ans = []

    #构造提交至Kow的数据
    post_content_template = '''
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soap="http://soap.szkingdom.com/">
	<soapenv:Header/>
	<soapenv:Body>
		<soap:%s>
		    <REQDATA>%s</REQDATA>
		</soap:%s>
	</soapenv:Body>
   </soapenv:Envelope>
    '''

    try:
        with aiohttp.Timeout(20):
            with aiohttp.ClientSession() as session:
                currenttasknum = currenttasknum + 1
                # assert response.status == 200
                content = ''
                method = g_kow_urlconfig[serviceid]['method']
                try:
                    headers = {'SOAPAction': '', 'Content-Type':'text/xml; charset=UTF-8'}
                    #构造post提交的数据
                    postcontent = post_content_template % (method, strjsonreq, method)
                    #获取应答
                    response = yield from session.post(url=g_kow_urlconfig[serviceid]['url'], data=postcontent, headers=headers)
                    #取出应答数据
                    content = yield from response.text()
                    yield from response.release()
                finally:
                    session.close()

                if not content:
                    return  '-1001', '调用Kow服务器异常：返回内容为空', []
                dom = parseString(content) #将应答解析成dom
                jsonstr = dom.getElementsByTagName(method+'Return')
                if not jsonstr:
                    return '-1002', '调用Kow服务器异常：应答解析错误', []

                jsonans = json.loads(jsonstr[0].childNodes[0].data)
                if not jsonans['ANSWERS'][0]['ANS_COMM_DATA']:
                    jsonans['ANSWERS'][0]['ANS_COMM_DATA'] = []

                code = jsonans['ANSWERS'][0]['ANS_MSG_HDR']['MSG_CODE']
                msg = jsonans['ANSWERS'][0]['ANS_MSG_HDR']['MSG_TEXT']
                ans = jsonans['ANSWERS'][0]['ANS_COMM_DATA']
                if code == '1': #KOW服务器返回的code为'1'表示成功，所以返回给前端需要转换成'0'
                    code = '0'
                elif code == '0':
                    code = '1'

                currenttasknum = currenttasknum - 1
                return  code, msg, ans
    except TimeoutError as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'kowcommbiz', ZTLOG_ERROR, msgid='')
        currenttasknum = currenttasknum - 1
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "bpagentlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return '-1111', '转发nginx:调用服务器超时', repr(e)
    except Exception as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'kowcommbiz', ZTLOG_ERROR, msgid='')
        currenttasknum = currenttasknum - 1
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "bpagentlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return '-1112', '调用Kow服务器异常：'+repr(e), repr(e)

#600020
async def kowcommbiz600020(ref_request, serviceid, jsonreq):
    jsondstreq = {}
    try:
        code,msg,dstserviceid = translateReq(serviceid,jsonreq,jsondstreq)
        if code != 0:
            return '-3002','kow功能%s入参转换失败:%s'%(serviceid,msg),[]
    except Exception as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'kowcommbiz600020', ZTLOG_ERROR, msgid='')
        return '-3002','kow功能%s入参转换失败:%s'%(serviceid,repr(e)),[]
   
    code,msg,rows = await kowcommbiz(ref_request,serviceid,jsondstreq)

    return code,msg,rows

#发送手机短信验证码
async def MakeTecentCheckCode(ref_request, jsonreq, mtel):
    # 发送短信验证码
    if mtel == '':
        return '-1000', '手机号码为空，短信验证码发送失败', []

    biztype = jsonreq['BIZ_TYPE']

    #检查业务类型是否正确
    if biztype not in g_sms_dict:
        return '-800011011', '业务类型入参[BIZ_TYPE]不正确：未配置的业务类型', []

    # 随机生成6位数的短信验证码
    strCheckCode = ''
    for i in range(6):
        strCheckCode += str(9 - (random.randint(1, 1000) % 10))

    # 往redis中缓存手机号码和验证码
    SetRedisKeyEx(mtel, strCheckCode, g_efftive_time)

    #构造短信内容，往短信模版中填入 业务信息 和 短信验证码
    strSmsContent = g_sms_content_template % (g_sms_dict[biztype], strCheckCode)

    dict_sms_data = {}
    dict_sms_data['mobiles'] = mtel
    dict_sms_data['messageContent'] = strSmsContent
    dict_sms_data['productId'] = 1


    new_dict_sms_data = {}
    new_dict_sms_data['providerId'] = providerId
    new_dict_sms_data['auditorId'] = auditorId
    new_dict_sms_data['bizType'] = bizType
    new_dict_sms_data['content'] = strSmsContent
    new_dict_sms_data['destType'] = 100
    new_dict_sms_data['destIds'] = [mtel]

    ext = {}
    ext['isCheckBlackList'] = '1'
    new_dict_sms_data['ext'] = ext
    dict_ans = []

    if g_sms_switch == 'new_sms':
        url = new_g_sms_url
        ans_requests = requests.post(url, json=new_dict_sms_data)
        dict_ans = ans_requests.json()
    else:
        url = g_sms_url + str(dict_sms_data)
        # 调用webservice发送短信验证码
        ans_requests = requests.post(url)
        ans = ans_requests.text
        dict_ans = eval(ans)


    if dict_ans['code'] != 0:
        return dict_ans['code'], dict_ans['message'], []

    return '0', strCheckCode, []
    
    
if __name__ == "__main__":
    reqdict = {"COMPANY_ID":"12900","USER_ID":"zzl20170215test","USER_ID_CLS":"1","SESSION":"000000" }
    serviceid = 'KOW009'
    code, msg, ans = kowcommbiz('bbbb', serviceid, reqdict)
    print(code, msg, ans)
    print('测试KOW009结束')

