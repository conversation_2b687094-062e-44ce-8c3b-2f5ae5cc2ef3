#------------------------------------------------------------------------------
# Name:        log.py
# Purpose:因为很多代码的日志模块都是这个，所以只能在这个文件上改了
#注意事项 1 新的日志模块使用C++，kwl_py_log_config。py的配置基本废弃
#注意事项 2 因为是采用缓存形式写日志,当时间达到条件或待写日志字符达到长度才会写日志,所以要等待一阵才会写完日志!
#注意事项 3 因为是C++,一条日志暂时只支持40k长度
# Author:      Administrator
#
# Created:     
# Copyright:   (c) Administrator
# Licence:

#-------------------------------------------------------------------------------
from KWL import *
import platform
if 'Linux' in platform.system():
    from KWL_Nginx import *
else:
    def GetKWLParam(sKey):
        return '99999'
    def SendResponse(relativeReq, ansConetent, iLenth):
        print('main Send Ans back:' + ansConetent)
    def IsNginxOn():
        return True
import sys,os
import platform
import KwlTools
from kwl_py_log_config import *

ZTLOG_DEBUG = 1
ZTLOG_INFO = 2
ZTLOG_WARNING = 3
ZTLOG_ERROR = 4
ZTLOG_EXCEPTION = ZTLOG_ERROR
ZTLOG_CRITICAL = 5

#初始化日志
KwlTools.LogInit('pylogs','kinginx',g_kwl_log_config['loglevel'])
##############################################################################################
#通用日志函数
#入参:
#     msg    日志信息
#     msgcls 消息类别  str: req ans error
#     level  int 1 debug 2 info 3 warn 4 error 5 critical 
#     msgid  请求ID 送nginx 分配的消息ID
#       
#   
##############################################################################################
is_windows = False
if 'Windows' in platform.system():
    is_windows = True
    def GetClientIp(msgid): 
        return '0.0.0.0'

def kwl_py_write_log(msg,msgcls = 'error',level = 2,msgid = 'kwl_err_99999'):
    """
    日志打印方法
    :param msg: 消息内容
    :param msgcls: 消息分类，一般写方法名
    :param level: 消息级别
    :param msgid: 消息id，用于单个请求全链路日志追踪，可以是线程id，或者请求id
    """
    #格式化日志信息
    ip = GetClientIp(msgid)
    msg = 'IP=' + ip + ' msgid=' + str(msgid).ljust(32) +   msgcls.ljust(8) + ':' + msg
    #分级别打印日志
    KwlTools.WriteLog(level,msg)
    if is_windows:
        print(msg)

def log_debug(msg, msgcls='log_debug', msgid='kwl_err_99999'):
    kwl_py_write_log(msg, msgcls, 1, msgid)

def log_info(msg, msgcls='log_info', msgid='kwl_err_99999'):
    kwl_py_write_log(msg, msgcls, 2, msgid)

def log_warning(msg, msgcls='log_warning', msgid='kwl_err_99999'):
    kwl_py_write_log(msg, msgcls, 3, msgid)

def log_error(msg, msgcls='log_error', msgid='kwl_err_99999'):
    kwl_py_write_log(msg, msgcls, 4, msgid)


if __name__ == '__main__':
    print("%s" % 'req:'.ljust(8) + 'msgid='+ "%s" % str(1111).ljust(32) + '\t\t\t\t' + 'aaaa')
          
    

