# -*- coding: utf-8 -*-
#把bpagent的业务加入其中
from kwlpyfunction import *
import KwlTools
from kcxpconfig import *
from tyrzconfig import *
import json
import pymysql
import os
import time
import traceback
import pyDes
import binascii
from common_config import *
from kwl_py_log import *
if isLinuxSystem():
    from KWL_Nginx import *
else:
    def GetKWLParam(sKey):
        return '99999'
    def IsNginxOn():
        return True

session_deskey = pyDes.des("410301\0\0",pyDes.ECB, "\0\0\0\0\0\0\0\0", pad=None, padmode=pyDes.PAD_PKCS5)

#多久检测下数据库连接,秒
g_CheckConnTime = 10
#上回的检测时间
g_LastTyrzCheckTime = 0
g_LastReportCheckTime = 0
try:
    g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)
    g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'], db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)
except Exception as e:
    todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
    exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
    strerr = traceback.format_exc()
    nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
    exceptlogfile.write(nowtime + ':' + strerr)
    exceptlogfile.flush()
    exceptlogfile.close()


#同步-统一认证签约,返回三元组code,msg,data数组
def T0005005_sign(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
             #断开的连接异常也打印下为什么,很可能是g_ConnTyrzDB未定义ping方法，原因是启动nginx的时候木有连上mysql
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005005_sign',strerr)
            try: #此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID' not in jsonreq:
            return -200,'统一认证[T0005005]:入参第三方用户ID[USER_ID]为空',[]
        if 'CUSTID' not in jsonreq:
            return -200,'统一认证[T0005005]:入参券商处客户代码[CUSTID]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            return -200,'统一认证[T0005005]:入参券商代码[COMPANY_ID]为空',[]
        if 'ORG_CODE' not in jsonreq:
            return -200,'统一认证[T0005005]:入参客户机构号[ORG_CODE]为空',[]
        if 'USER_ID_CLS' not in jsonreq:
            return -200,'统一认证[T0005005]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        DataAcid = jsonreq['USER_ID'] + str(jsonreq['COMPANY_ID']) + jsonreq['USER_ID_CLS']
        Acid = KwlTools.GetAcid(DataAcid,len(DataAcid))
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            if jsonreq['USER_ID_CLS'] == '2': #腾讯渠道
                cur.callproc('P_KBSS_DEL_BY_QQCODE',args=(jsonreq['USER_ID'],jsonreq['COMPANY_ID']))
                cur.callproc('P_KBSS_DEL_BY_CUSTID',args=(jsonreq['CUSTID'],jsonreq['COMPANY_ID']))
            else:
                # 非腾讯渠道
                cur.callproc('P_KBSS_DEL_BY_USERID_TYPE',args=(jsonreq['USER_ID'],jsonreq['COMPANY_ID'],jsonreq['USER_ID_CLS']))
            g_ConnTyrzDB.commit()

            strsql = "select user_id_info from user_identity where  USER_ID_INFO='%s' and USER_ID_TYPE = '0';"%(jsonreq['USER_ID'])
            rows = cur.execute(strsql)
            if rows != 0:
                datalist=cur.fetchall()
                return -200,'该第三方帐号已经签约了券商客户号:%s'%(datalist[0]['user_id_info']),[]

            listInfo = []    #对应统一认证的多行记录 类型,帐号,市场,状态
            custidinfo = ['0',jsonreq['CUSTID'],'-','0']
            funidinfo = ['3',jsonreq['ACCOUNT'],'-','0']
            userid_info = [jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],'-','0']

            sza_info = ['4','','0','0']
            szb_info = ['4','','2','0']
            sha_info = ['4','','1','0']
            shb_info = ['4','','3','0']
            if jsonreq['LOGIN_TYPE'] ==  '1':
                custidinfo[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '2': #签约的是 LOGIN_TYPE 1客户代码，2资金账户，3深A，4深B，5沪A，6沪B
                funidinfo[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '3':
                sza_info[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '4':
                szb_info[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '5':
                sha_info[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '6':
                shb_info[3] = '1'

            listInfo.append(custidinfo)
            listInfo.append(funidinfo)
            listInfo.append(userid_info)
            if 'SECUSZA' in jsonreq and jsonreq['SECUSZA'] != '' :
                sza_info[1] = jsonreq['SECUSZA']
                listInfo.append(sza_info)
            if 'SECUSZB' in jsonreq and jsonreq['SECUSZB'] != '':
                szb_info[1] = jsonreq['SECUSZB']
                listInfo.append(szb_info)
            if 'SECUSHA' in jsonreq and jsonreq['SECUSHA'] != '':
                sha_info[1] = jsonreq['SECUSHA']
                listInfo.append(sha_info)
            if 'SECUSHB' in jsonreq and jsonreq['SECUSHB'] != '':
                shb_info[1] = jsonreq['SECUSHB']
                listInfo.append(shb_info)
            for eachinfo in listInfo:
                cur.callproc('P_KBSS_UPDATE_USER_IDENTITY_EX',args=(Acid,'1',jsonreq['ORG_CODE'],eachinfo[2],eachinfo[0],eachinfo[1],nowtime,eachinfo[3],jsonreq['COMPANY_ID'],'1'))
            cur.callproc('P_KBSS_UPDATE_USER_INFO',args=(Acid,'1',jsonreq['USER_NAME'],'********','********',jsonreq['COMPANY_ID'],'1','0','','',jsonreq['ID'],'0',''))
            g_ConnTyrzDB.commit() #一次性提交
            #组json票据
            jsonsession = {}
            jsonsession['0'] = Acid
            jsonsession['1'] = []
            jsonuser = {}
            jsonuser['2'] = jsonreq['COMPANY_ID']
            jsonuser['3'] = jsonreq['CUSTID']
            jsonuser['4'] = jsonreq['ACCOUNT']
            jsonuser['5'] = jsonreq['ORG_CODE']
            jsonuser['6'] = jsonreq['TRD_PWD']
            jsonuser['7'] = nowtime
            jsonuser['8'] = []
            #组TCC信息到票据
            if 'TCC' in jsonreq:
                if jsonreq['USER_ID_CLS'] in g_session_extension_TCC:
                    if str(jsonreq['COMPANY_ID']) in g_session_extension_TCC[jsonreq['USER_ID_CLS']]:
                        for tcckey,tccfun in g_session_extension_TCC[jsonreq['USER_ID_CLS']][str(jsonreq['COMPANY_ID'])].items():
                            jsonuser[tcckey] = tccfun(jsonreq['TCC'])
            #取拓展信息
            cur.callproc('P_KBSS_GET_USEREXTENSIONINFO',args=(jsonreq['USER_ID'],jsonreq['COMPANY_ID'],jsonreq['USER_ID_CLS']))
            ansCommData = cur.fetchall()
            for eachinfo in ansCommData:
                if eachinfo['type_key'] in g_session_extension_info:
                    jsonuser[eachinfo['type_key']] = eachinfo['type_value']

            #记录签约操作
            if jsonreq['USER_ID_CLS'] in g_sign_and_unsign_channel:
                strsql = "INSERT INTO `user_sign_and_unsign` (USER_CODE,USER_ID,USER_ID_CLS,ACCOUNT,COMPANY_ID,TIME,TYPE) VALUES(%s,'%s','%s','%s','%s','%s','0');"%(
                                                                                Acid,jsonreq['USER_ID'],jsonreq['USER_ID_CLS'],jsonreq['ACCOUNT'],jsonreq['COMPANY_ID'],nowtime)
                cur.execute(strsql)
            g_ConnTyrzDB.commit()

            if 'SECUSZA' in jsonreq and jsonreq['SECUSZA']:
                 jsonuser['8'].append({'9':'0','a':jsonreq['SECUSZA']})
            if 'SECUSZB' in jsonreq and jsonreq['SECUSZB']:
                 jsonuser['8'].append({'9':'2','a':jsonreq['SECUSZB']})
            if 'SECUSHA' in jsonreq and jsonreq['SECUSHA']:
                 jsonuser['8'].append({'9':'1','a':jsonreq['SECUSHA']})
            if 'SECUSHB' in jsonreq and jsonreq['SECUSHB']:
                 jsonuser['8'].append({'9':'3','a':jsonreq['SECUSHB']})
            jsonuser['c'] = ''
            jsonsession['1'].append(jsonuser)
            jsonsession[jsonreq['USER_ID_CLS']] = jsonreq['USER_ID']
            session = json.dumps(jsonsession,ensure_ascii=False)
            #对json票据进行压缩加标准des加密
            # encryptsession = binascii.hexlify(session_deskey.encrypt(session.encode('utf-8'))).decode('utf-8')
            encryptsession = session_encrypt(session, g_session_des_key)
            g_LastTyrzCheckTime = nowtime_s
            return 0,'签约插入成功',[{'SESSION_JSON':jsonsession,'SESSION':encryptsession}]

    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]


#签约留痕
def user_sign_mark(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
             #断开的连接异常也打印下为什么,很可能是g_ConnTyrzDB未定义ping方法，原因是启动nginx的时候木有连上mysql
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('user_sign_mark',strerr)
            try: #此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        with open('./disclaimer.txt', encoding='utf-8') as f:
            disclaimer = f.read()
        with open('./no_secret.txt', encoding='utf-8') as f:
            no_secret = f.read()

        tcc = jsonreq['TCC']
        if not tcc:
            op_station = ''
        elif tcc[0:2] == 'MA' or tcc[0:2] == 'MI':
            op_station = tcc
        else:
            op = tcc.split('|')
            op_station = op[2] + ';' + op[1] + ';' + op[0]

        # op_station = jsonreq['TCC'].split('|')
        sign_agreement = str(nowtime) + '|' + jsonreq['ACCOUNT'] + '|' + op_station + '|' \
                         + '签署免责协议' + '|' + disclaimer + '|' + '签署无密协议' + '|' + no_secret + '|' + '已签署'

        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            # str_exit_sql = "select USER_ID from user_sign_mark where USER_ID = '%s'; "  % (jsonreq['USER_ID'])
            # rows = cur.execute(str_exit_sql)
            # if rows:
            #     # 已存在先删除
            #     str_del_sql = "delete from user_sign_mark where USER_ID = '%s'; " % (jsonreq['USER_ID'])
            #     cur.execute(str_del_sql)

            strsql = "INSERT INTO `user_sign_mark` (USER_ID,USER_ID_CLS,ACCOUNT,COMPANY_ID,TIME,SIGN_AGREEMENT) VALUES('%s','%s','%s','%s','%s','%s');" % (
                jsonreq['USER_ID'], jsonreq['USER_ID_CLS'], jsonreq['ACCOUNT'], jsonreq['COMPANY_ID'], nowtime, sign_agreement)
            cur.execute(strsql)
            g_ConnTyrzDB.commit()

        return 0, '签约留痕成功', []
    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []

def encrypt_pwd(pwd):
    wzq_pwd = cl_openssl.RSADecryptBCD(g_masterprivatekey, pwd)
    return des_encrypt(wzq_pwd[-6:], '410301_DesPwd')

#华林专用-统一认证签约,返回三元组code,msg,data数组
def T0005005_sign_hlzq(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
             #断开的连接异常也打印下为什么,很可能是g_ConnTyrzDB未定义ping方法，原因是启动nginx的时候木有连上mysql
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005005_sign',strerr)
            try: #此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_INFO' not in jsonreq:
            return 200001,'统一认证[T0005005]:入参第三方用户ID[USER_ID_INFO]为空',[]
        if 'USER_CODE' not in jsonreq:
            return 200001,'统一认证[T0005005]入参券商处客户代码[USER_CODE]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            return 200001,'统一认证[T0005005]:入参券商代码[COMPANY_ID]为空',[]
        if 'USER_ID' not in jsonreq:
            return 200001,'统一认证[T0005005]:入参客户Acid[USER_ID]为空',[]
        if 'ORG_CODE' not in jsonreq:
            return 200001,'统一认证[T0005005]:入参客户机构号[ORG_CODE]为空',[]

        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            #判断是否已经有该签约数据
	        #针对自选股的要求，同一个客户代码被绑定多次时,先清掉前面绑定的数据,存入最新的绑定数据
            if jsonreq['USER_ID_TYPE'] == '2': #腾讯渠道
                cur.callproc('P_KBSS_DEL_BY_QQCODE',args=(jsonreq['USER_ID_INFO'],jsonreq['COMPANY_ID']))
                cur.callproc('P_KBSS_DEL_BY_CUSTID',args=(jsonreq['USER_CODE'],jsonreq['COMPANY_ID']))
            else:
                # 非腾讯渠道
                cur.callproc('P_KBSS_DEL_BY_USERID_TYPE',args=(jsonreq['USER_ID_INFO'],jsonreq['COMPANY_ID'],jsonreq['USER_ID_TYPE']))
            g_ConnTyrzDB.commit()

            strsql = "select user_id_info from user_identity where  USER_ID_INFO='%s' and USER_ID_TYPE = '0';"%(jsonreq['USER_ID_INFO'])
            rows = cur.execute(strsql)
            if rows != 0:
                datalist=cur.fetchall()
                return 60008768,'该第三方帐号已经签约了券商客户号:%s'%(datalist[0]['user_id_info']),[]

            listInfo = []    #对应统一认证的多行记录 类型,帐号,市场,状态
            custidinfo = ['0',jsonreq['USER_CODE'],'-','0']
            funidinfo = ['3',jsonreq['CUACCT_CODE'],'-','0']
            userid_info = [jsonreq['USER_ID_CLS'],jsonreq['USER_ID_INFO'],'-','0']

            sza_info = ['4','','0','0']     #深A
            szb_info = ['4','','2','0']     #深B
            szg_info = ['4','','S','0']     #深G
            sha_info = ['4','','1','0']     #沪A
            shb_info = ['4','','3','0']     #沪B
            shg_info = ['4','','5','0']     #沪G
            if jsonreq['LOGIN_TYPE'] ==  '1':
                custidinfo[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '2': #签约的是 LOGIN_TYPE 1客户代码，2资金账户，3深A，4深B，5沪A，6沪B
                funidinfo[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '3':
                sza_info[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '4':
                szb_info[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '5':
                sha_info[3] = '1'
            elif  jsonreq['LOGIN_TYPE'] ==  '6':
                shb_info[3] = '1'

            listInfo.append(custidinfo)
            listInfo.append(funidinfo)
            listInfo.append(userid_info)
            if 'SECUSZA' in jsonreq and jsonreq['SECUSZA'] != '' :
                sza_info[1] = jsonreq['SECUSZA']
                listInfo.append(sza_info)
            if 'SECUSZB' in jsonreq and jsonreq['SECUSZB'] != '':
                szb_info[1] = jsonreq['SECUSZB']
                listInfo.append(szb_info)
            if 'SECUSGT' in jsonreq and jsonreq['SECUSGT'] != '':
                szg_info[1] = jsonreq['SECUSGT']
                listInfo.append(szg_info)
            if 'SECUSHA' in jsonreq and jsonreq['SECUSHA'] != '':
                sha_info[1] = jsonreq['SECUSHA']
                listInfo.append(sha_info)
            if 'SECUSHB' in jsonreq and jsonreq['SECUSHB'] != '':
                shb_info[1] = jsonreq['SECUSHB']
                listInfo.append(shb_info)
            if 'SECUHGT' in jsonreq and jsonreq['SECUHGT'] != '':
                shg_info[1] = jsonreq['SECUHGT']
                listInfo.append(shg_info)
            for eachinfo in listInfo:
                #参见表user_identity，参数分别是第三方客户号、角色(1)、机构、市场、信息类型、用户信息、创建日期、状态(1、0,关系到T0005010)、券商号、服务id(1)
                cur.callproc('P_KBSS_UPDATE_USER_IDENTITY_EX',args=(jsonreq['USER_ID'],'1',jsonreq['ORG_CODE'],eachinfo[2],eachinfo[0],eachinfo[1],nowtime,eachinfo[3],jsonreq['COMPANY_ID'],'1'))
            # 插user_info表
            cur.callproc('P_KBSS_UPDATE_USER_INFO',args=(jsonreq['USER_ID'],'1',jsonreq['USER_NAME'],'********','********',jsonreq['COMPANY_ID'],'1','0','','',jsonreq['ID'],'0',''))
            g_ConnTyrzDB.commit() #一次性提交
            #组json票据
            jsonsession = {}
            jsonsession['0'] = jsonreq['USER_ID']
            jsonsession['1'] = []
            jsonuser = {}
            jsonuser['2'] = jsonreq['COMPANY_ID']
            jsonuser['3'] = jsonreq['USER_CODE']
            jsonuser['4'] = jsonreq['CUACCT_CODE']
            jsonuser['5'] = jsonreq['ORG_CODE']
            jsonuser['6'] = encrypt_pwd(jsonreq['TRD_PWD'])
            jsonuser['7'] = nowtime
            jsonuser['8'] = []

            #记录签约操作
            strsql = "INSERT INTO `user_sign_and_unsign` (USER_CODE,USER_ID,USER_ID_CLS,ACCOUNT,COMPANY_ID,TIME,TYPE) VALUES(%s,'%s','%s','%s','%s','%s','0');"%(
                                                                                jsonreq['USER_ID'],jsonreq['USER_ID_INFO'],jsonreq['USER_ID_CLS'],jsonreq['CUACCT_CODE'],jsonreq['COMPANY_ID'],nowtime)
            cur.execute(strsql)
            g_ConnTyrzDB.commit()

            if 'SECUSZA' in jsonreq and jsonreq['SECUSZA']:
                 jsonuser['8'].append({'9':'0','a':jsonreq['SECUSZA']})
            if 'SECUSZB' in jsonreq and jsonreq['SECUSZB']:
                 jsonuser['8'].append({'9':'2','a':jsonreq['SECUSZB']})
            if 'SECUSGT' in jsonreq and jsonreq['SECUSGT']:
                 jsonuser['8'].append({'9':'S','a':jsonreq['SECUSGT']})
            if 'SECUSHA' in jsonreq and jsonreq['SECUSHA']:
                 jsonuser['8'].append({'9':'1','a':jsonreq['SECUSHA']})
            if 'SECUSHB' in jsonreq and jsonreq['SECUSHB']:
                 jsonuser['8'].append({'9':'3','a':jsonreq['SECUSHB']})
            if 'SECUHGT' in jsonreq and jsonreq['SECUHGT']:
                 jsonuser['8'].append({'9':'5','a':jsonreq['SECUHGT']})
            jsonuser['c'] = ''
            jsonsession['1'].append(jsonuser)
            jsonsession['user_id'] = jsonreq['USER_ID_INFO']
            session = json.dumps(jsonsession,ensure_ascii=False)
            #对json票据进行标准des加密
            # encryptsession = binascii.hexlify(session_deskey.encrypt(session.encode('utf-8'))).decode('utf-8')
            encryptsession = session_encrypt(session, g_session_des_key)
            g_LastTyrzCheckTime = nowtime_s
            return 0,'签约插入成功',[{'SESSION_JSON':jsonsession,'SESSION':encryptsession}]

    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]


#生成票据
def T0005001_makesession(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005001_makesession',strerr)
            try:
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_CLS' not in jsonreq:
            return -200,'统一认证[T0005001]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        if 'USER_ID' not in jsonreq:
            return -200,'统一认证[T0005001]:入参第三方用户ID[USER_ID]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            return -200,'统一认证[T0005001]:入参券商代码[COMPANY_ID]为空',[]

        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        jsonsession = {}
        jsonsession['0'] = ''
        jsonsession['1'] = []
        jsonuser = {}
        list_secuid = [] #股东帐号
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            cur.callproc('P_KBSS_GET_USERIDENINFO_EX',args=('1',jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],jsonreq['COMPANY_ID']))
            g_ConnTyrzDB.commit()
            ansCommData = cur.fetchall()
            for eachinfo in ansCommData:
                jsonsession['0'] = str(eachinfo['USER_CODE']) #int也没关系(你妹没关系，int老架构C++取不出来)
                jsonuser['2'] = str(eachinfo['SYSTEM_ID'])
                jsonuser['5'] = eachinfo['ORG_CODE']

                if  eachinfo['USER_ID_TYPE'] == jsonreq['USER_ID_CLS']:   #渠道的帐号
                    jsonsession['user_id'] = eachinfo['USER_ID_INFO']
                elif eachinfo['USER_ID_TYPE'] == '0':  #客户代码
                    jsonuser['3'] = eachinfo['USER_ID_INFO']
                elif eachinfo['USER_ID_TYPE'] == '3':  #资金帐号
                    jsonuser['4'] = eachinfo['USER_ID_INFO']
                elif eachinfo['USER_ID_TYPE'] == '4':  #股东代码
                     list_secuid.append({'9':eachinfo['MARKET'],'a':eachinfo['USER_ID_INFO']})

            if jsonsession['0'] == '':
                err_msg = '统一认证[T0005001]:不存在该用户签约信息,第三方客户:%s,客户渠道:%s'%(jsonreq['USER_ID'],jsonreq['USER_ID_CLS'])
                kwl_py_write_log(err_msg, 'T0005001_makesession', ZTLOG_INFO, msgid='')
                return -200, err_msg, []
            if 'TRD_PWD' in jsonreq:
                #以此判断为产生登录票据
                #组TCC信息到票据
                if 'TCC' in jsonreq:
                    if jsonreq['USER_ID_CLS'] in g_session_extension_TCC:
                        if str(jsonreq['COMPANY_ID']) in g_session_extension_TCC[jsonreq['USER_ID_CLS']]:
                            for tcckey,tccfun in g_session_extension_TCC[jsonreq['USER_ID_CLS']][str(jsonreq['COMPANY_ID'])].items():
                                jsonuser[tcckey] = tccfun(jsonreq['TCC'])
                # 密码解成明文再des加密
                wzq_pwd = cl_openssl.RSADecryptBCD(g_masterprivatekey, jsonreq['TRD_PWD'])
                jsonuser['6'] = des_encrypt(wzq_pwd[-6:], '410301_DesPwd')
            jsonuser['7'] = nowtime
            if len(list_secuid) != 0:
                jsonuser['8'] = list_secuid
            else:
                jsonuser['8'] = []

            #取拓展信息
            cur.callproc('P_KBSS_GET_USEREXTENSIONINFO',args=(jsonreq['USER_ID'],jsonreq['COMPANY_ID'],jsonreq['USER_ID_CLS']))
            g_ConnTyrzDB.commit()
            ansCommData = cur.fetchall()
            for eachinfo in ansCommData:
                if eachinfo['type_key'] in g_session_extension_info:
                    jsonuser[eachinfo['type_key']] = eachinfo['type_value']

            jsonsession['1'].append(jsonuser)
            #对json票据进行标准des加密
            if 'SESSION_TYPE' in jsonreq and jsonreq['SESSION_TYPE'] == 'Login':
                session = json.dumps(jsonsession,ensure_ascii=False)
                # encryptsession = binascii.hexlify(session_deskey.encrypt(session.encode('utf-8'))).decode('utf-8')
                encryptsession = session_encrypt(session, g_session_des_key)
            else:
                encryptsession = '000000'
            g_LastTyrzCheckTime = nowtime_s
            return 0,'生成票据成功',[{'SESSION_JSON':jsonsession,'SESSION':encryptsession}]

    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#解约T0005009
def T0005009_unsign(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005009_unsign',strerr)
            try:
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_CLS' not in jsonreq:
            return -200,'统一认证[T0005009]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        if 'USER_ID' not in jsonreq:
            return -200,'统一认证[T0005009]:入参第三方用户ID[USER_ID]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            return -200,'统一认证[T0005009]:入参券商代码[COMPANY_ID]为空',[]

        jsonreq['COMPANY_ID'] = int(jsonreq['COMPANY_ID'])
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            cur.callproc('P_KBSS_DEL_USERIDENINFO_EX',args=(jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],jsonreq['COMPANY_ID']))
            #记录解约操作
            if jsonreq['USER_ID_CLS'] in g_sign_and_unsign_channel:
                nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
                Acid = str(jsonreq['SESSION_JSON']['0'])
                ACCOUNT = jsonreq['SESSION_JSON']['1'][0]['4']
                strsql = "INSERT INTO `user_sign_and_unsign` (USER_CODE,USER_ID,USER_ID_CLS,ACCOUNT,COMPANY_ID,TIME,TYPE) VALUES(%s,'%s','%s','%s','%s','%s','1');"%(
                                                                                Acid,jsonreq['USER_ID'],jsonreq['USER_ID_CLS'],ACCOUNT,jsonreq['COMPANY_ID'],nowtime)
                cur.execute(strsql)
            g_ConnTyrzDB.commit()
            g_LastTyrzCheckTime = nowtime_s
            return 0,'统一认证解约成功',[]
    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#解约T0005009 华林
def T0005009_unsign_hlzq(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005009_unsign_hlzq',strerr)
            try:
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_CLS' not in jsonreq:
            return 2000,'统一认证[T0005009]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        if 'USER_ID' not in jsonreq:
            return 2000,'统一认证[T0005009]:入参第三方用户ID[USER_ID]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            return 2000,'统一认证[T0005009]:入参券商代码[COMPANY_ID]为空',[]

        jsonreq['COMPANY_ID'] = int(jsonreq['COMPANY_ID'])
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            bQuerryok = False
            #先查下客户的基本数据
            cur.callproc('P_KBSS_GET_USERIDENINFO_EX',args=('1',jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],jsonreq['COMPANY_ID']))
            g_ConnTyrzDB.commit()
            ansCommData = cur.fetchall()
            for eachinfo in ansCommData:
                Acid = eachinfo['USER_CODE'] #int也没关系
                if eachinfo['USER_ID_TYPE'] == '3':  #资金帐号
                    ACCOUNT = eachinfo['USER_ID_INFO']
                    bQuerryok = True

            #执行删除，查找客户资料信息，包括客户代码、股东代码、资金账户、市场、节点编号等信息
            cur.callproc('P_KBSS_DEL_USERIDENINFO_EX',args=(jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],jsonreq['COMPANY_ID']))

            #记录解约操作
            if bQuerryok:
                nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
                strsql = "INSERT INTO `user_sign_and_unsign` (USER_CODE,USER_ID,USER_ID_CLS,ACCOUNT,COMPANY_ID,TIME,TYPE) VALUES(%s,'%s','%s','%s','%s','%s','1');"%(
                                                                                Acid,jsonreq['USER_ID'],jsonreq['USER_ID_CLS'],ACCOUNT,jsonreq['COMPANY_ID'],nowtime)
                cur.execute(strsql)
            g_ConnTyrzDB.commit()
            g_LastTyrzCheckTime = nowtime_s
            return 0,'统一认证解约成功',[]
    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#查询客户签约信息T0005010
def T0005010_querrysigninfo(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005010_querrysigninfo',strerr)
            try:
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_CLS' not in jsonreq:
            return -200,'统一认证[T0005010]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        if 'USER_ID' not in jsonreq:
            return -200,'统一认证[T0005010]:入参第三方用户ID[USER_ID]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            COMPANY_ID = ''
        else:
            COMPANY_ID = jsonreq['COMPANY_ID']
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            cur.callproc('P_KBSS_GET_ACCOUNT_BY_WX',args=(jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],COMPANY_ID))
            ansCommData = cur.fetchall()
            for eachrow in ansCommData:
                eachrow['OPEN_DATE'] = str(eachrow['CREATE_DATE'])
                del(eachrow['CREATE_DATE'])
                eachrow['COMPANY_ID'] = eachrow['SYSTEM_ID']
                del(eachrow['SYSTEM_ID'])
                eachrow['LOGIN_TYPE'] = eachrow['USER_ID_TYPE']
                del(eachrow['USER_ID_TYPE'])
                eachrow['LOGIN_CODE'] = eachrow['USER_ID_INFO']
                del(eachrow['USER_ID_INFO'])
                eachrow['BRANCH_CODE'] = eachrow['ORG_CODE']
                del(eachrow['ORG_CODE'])
                eachrow['USER_ID'] = jsonreq['USER_ID']
                eachrow['USER_ID_CLS'] = jsonreq['USER_ID_CLS']
            g_ConnTyrzDB.commit()
            if len(ansCommData) == 0:
                ansCommData = []
            g_LastTyrzCheckTime = nowtime_s
            return 0,'查询客户签约信息成功',ansCommData
    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#查询客户最新签约信息
def T0005020_querrylastsigninfo(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005020_querrylastsigninfo',strerr)
            try:
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_CLS' not in jsonreq:
            return -200,'统一认证[T0005020]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        if 'USER_ID' not in jsonreq:
            return -200,'统一认证[T0005020]:入参第三方用户ID[USER_ID]为空',[]

        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            cur.callproc('P_KBSS_GET_ACCOUNT_BY_WX',args=(jsonreq['USER_ID_CLS'],jsonreq['USER_ID'],''))
            ansCommData = cur.fetchall()
            lastTime = '' #最新签约时间
            dstrow = {}
            for eachrow in ansCommData:
                eachrow['OPEN_DATE'] = str(eachrow['CREATE_DATE'])
                eachrow['COMPANY_ID'] = eachrow['SYSTEM_ID']
                eachrow['LOGIN_TYPE'] = eachrow['USER_ID_TYPE']
                eachrow['LOGIN_CODE'] = eachrow['USER_ID_INFO']
                eachrow['BRANCH_CODE'] = eachrow['ORG_CODE']
                eachrow['USER_ID'] = jsonreq['USER_ID']
                eachrow['USER_ID_CLS'] = jsonreq['USER_ID_CLS']
                del(eachrow['CREATE_DATE'])
                del(eachrow['SYSTEM_ID'])
                del(eachrow['USER_ID_TYPE'])
                del(eachrow['USER_ID_INFO'])
                del(eachrow['ORG_CODE'])
                if lastTime < eachrow['OPEN_DATE']:
                    lastTime = eachrow['OPEN_DATE']
                    dstrow = eachrow
            g_ConnTyrzDB.commit()
            g_LastTyrzCheckTime = nowtime_s
            if len(ansCommData) == 0:
                return 0,'查询客户最新签约信息成功',[]
            else:
                return 0,'查询客户最新签约信息成功',[dstrow]
    #except pymysql.OperationalError as e:
        #mysql被手动关闭
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#拓展信息插入
def T0005006_insertExinfo(jsonreq):
    global g_ConnTyrzDB,g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('T0005006_insertExinfo',strerr)
            try:
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID_CLS' not in jsonreq:
            return -200,'统一认证[T0005006]:入参第三方渠道类型[USER_ID_CLS]为空',[]
        if 'USER_ID' not in jsonreq:
            return -200,'统一认证[T0005006]:入参第三方用户ID[USER_ID]为空',[]
        if 'COMPANY_ID' not in jsonreq:
            return -200,'统一认证[T0005006]:入参券商号[COMPANY_ID]为空',[]
        if 'TYPE_VALUE' not in jsonreq:
            return -200,'统一认证[T0005006]:入参[TYPE_VALUE]为空',[]
        if 'TYPE_KEY' not in jsonreq:
            return -200,'统一认证[T0005006]:入参[TYPE_KEY]为空',[]
        DataAcid = jsonreq['USER_ID'] + str(jsonreq['COMPANY_ID']) + jsonreq['USER_ID_CLS']
        Acid = KwlTools.GetAcid(DataAcid,len(DataAcid))
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            cur.callproc('P_KBSS_SAVE_USEREXTENSIONINFO',args=(Acid,jsonreq['USER_ID'],jsonreq['COMPANY_ID'],jsonreq['USER_ID_CLS'],jsonreq['TYPE_VALUE'],jsonreq['TYPE_KEY']))
            g_ConnTyrzDB.commit()
            g_LastTyrzCheckTime = nowtime_s
            return 0,'插入客户拓展信息成功',[]
    #except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#查询某银行银证业务是否需要密码信息
def SelectBankPwdInfo(jsonreq):
    global g_ConnTyrzReportDB,g_LastReportCheckTime
    try:
        try:  #判断连接是否有效
            nowtime = time.time()
            if nowtime - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
                g_LastReportCheckTime = nowtime
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('SelectBankPwdInfo',strerr)
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'], db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'EXT_INST' not in jsonreq or not jsonreq['EXT_INST']:
            return -200,'统一认证[SelectBankPwdInfo]:入参银行编码[EXT_INST]为空',[]
        if 'COMPANY_ID' not in jsonreq or not jsonreq['COMPANY_ID']:
            return -200,'统一认证[SelectBankPwdInfo]:入参[COMPANY_ID]为空',[]
        if 'BIZ_TYPE' not in jsonreq or not jsonreq['BIZ_TYPE']:
            return -200,'统一认证[SelectBankPwdInfo]:入参银证业务类型[BIZ_TYPE]为空',[]
        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = 'select BANK_CODE,BIZ_TYPE,IF_SUPPORT,CHECK_PWD_ZJ as CHECK_ACC_PWD,CHECK_PWD_YH  as CHECK_BANK_PWD from t_bank_info where COMPANY_ID=%s and (BANK_CODE=%s or %s=0) and (BIZ_TYPE = %s or %s = 0);'%(jsonreq['COMPANY_ID'],jsonreq['EXT_INST'],jsonreq['EXT_INST'],jsonreq['BIZ_TYPE'],jsonreq['BIZ_TYPE'])
            rows = cur.execute(strsql)
            datalist=cur.fetchall()
            for eachrow in datalist:
                for eachkey in eachrow:
                    eachrow[eachkey] = str(eachrow[eachkey])
            g_ConnTyrzReportDB.commit()
            return 0,'银行信息查询成功',datalist
    #except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#券商插入公告信息
def InsertNotice(jsonreq):
    global g_ConnTyrzReportDB,g_LastReportCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('InsertNotice',strerr)
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'], db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'COMPANY_ID' not in jsonreq:
            return -200,'统一认证[InsertNotice]:入参银行编码[COMPANY_ID]为空',[]
        if 'MSG_ID' not in jsonreq:
            return -200,'统一认证[InsertNotice]:入参[MSG_ID]为空',[]
        if 'BGN_DATE' not in jsonreq:
            jsonreq['BGN_DATE'] = ''
        if 'END_DATE' not in jsonreq:
            jsonreq['END_DATE'] = ''
        if 'TEXT' not in jsonreq:
            jsonreq['TEXT'] = ''
        if 'TYPE' not in jsonreq:
            jsonreq['TYPE'] = ''
        if 'TYPEVALUE' not in jsonreq:
            jsonreq['TYPEVALUE'] = ''
        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            cur.callproc('procInsertStockReportData',args=(jsonreq['COMPANY_ID'],jsonreq['MSG_ID'],jsonreq['BGN_DATE'],jsonreq['END_DATE'],jsonreq['TEXT'],jsonreq['TYPE'],jsonreq['TYPEVALUE']))
            g_ConnTyrzReportDB.commit()
            g_LastReportCheckTime = nowtime_s
            return 0,'发布公告成功',[]
    #except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#券商查询公告信息
def SelectNotice(jsonreq):
    global g_ConnTyrzReportDB,g_LastReportCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('SelectNotice',strerr)
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'], db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'COMPANY_ID' not in jsonreq:
            return -200,'统一认证[SelectNotice]:入参银行编码[COMPANY_ID]为空',[]
        if 'TRD_DATE' not in jsonreq:
            return -200,'统一认证[SelectNotice]:入参[TRD_DATE]为空',[]
        datelen = len(jsonreq['TRD_DATE'])
        if datelen != 8 and datelen != 10 and datelen != 14 and datelen != 19:
            return -200,'统一认证[SelectNotice]:入参[TRD_DATE]日期格式非法',[]
        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            #strsql = "select COMPANY_ID,MSG_ID,BGN_DATE,END_DATE,TEXT,IsNew,TYPE,TYPE_VALUE from t_stock_report where COMPANY_ID=%s and END_DATE >= DATE_FORMAT('%s','%s');"%(str(jsonreq['COMPANY_ID']),jsonreq['TRD_DATE'],'%Y-%m-%d %H:%i:%s')
            pose = jsonreq['TRD_DATE'].find('-')
            if pose != -1:
                strsql = "select * from t_stock_report where COMPANY_ID=%s and END_DATE >= DATE_FORMAT('%s','%s');"%(str(jsonreq['COMPANY_ID']),jsonreq['TRD_DATE'],'%Y-%m-%d %H:%i:%s')
            else:
                strsql = "select * from t_stock_report where COMPANY_ID=%s and END_DATE >= DATE_FORMAT('%s','%s');"%(str(jsonreq['COMPANY_ID']),jsonreq['TRD_DATE'],'%Y%m%d%H%i%s')
            rows = cur.execute(strsql)
            datalist=cur.fetchall()
            for eachrow in datalist:
                for eachkey in eachrow:
                    eachrow[eachkey] = str(eachrow[eachkey])
            g_ConnTyrzReportDB.commit()
            g_LastReportCheckTime = nowtime_s
            return 0,'查询公告成功',datalist
    #except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]

#校验票据模块移植到redis里面

#判断该资金账号的签约信息是否已经存在于统一认证数据库中
def isExistInTyrzDb(jsonreq):
    global g_ConnTyrzDB, g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('isExistInTyrzDb',strerr)
            try: #此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        # 获取资金账号
        account = ''
        if 'LOGIN_CODE' in jsonreq:
            account = jsonreq['LOGIN_CODE']
        if len(account) == 0:
            return -100, '入参有误：资金账号不能为空', []
        if jsonreq.get('USER_ID','') == '':
            return -1210, '入参有误：USER_ID不能为空', []
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = "select user_id_info from user_identity where user_id_info = '%s';" % (account)
            rows = cur.execute(strsql)
            g_ConnTyrzDB.commit()
            # 虽然有点多此一举，但是有时数据库的更改可能还没有生效，这时查询的结果可能与实际不符
            result = cur.fetchall()

            strsql_uid = "select user_id_info from user_identity where user_id_info = '%s';" % (jsonreq['USER_ID'])
            rows = cur.execute(strsql_uid)
            g_ConnTyrzDB.commit()
            # 虽然有点多此一举，但是有时数据库的更改可能还没有生效，这时查询的结果可能与实际不符
            resultu=_uid = cur.fetchall()

            if len(result) > 0:
                if len(resultu) > 0:    #相同资金账号和相同用户，重复绑定600001接口，支持可重入
                    return 0, '该资金账号可以重复进行签约', []
                return -101, '该资金账号已经绑定了另一个微信号，无法与当前微信号进行绑定', []
            return 0, '该资金账号可以进行签约', []

    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []

#判断客户的资金账号绑定有多少微信号
def bandNum(userId):
    global g_ConnTyrzDB, g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('isExistInTyrzDb',strerr)
            try: #此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            #如果查询的USER_CODE不唯一，会抛异常：“Subquery returns more than 1 row”。这样刚好可以记录
            strsql = '''select * from user_identity where
            USER_ID_INFO = (select USER_ID_INFO  from user_identity where USER_ID_INFO  = '%s')
            and USER_ID_TYPE = 1;''' % (userId)
            rows = cur.execute(strsql)
            g_ConnTyrzDB.commit()
            result = cur.fetchall()
            #返回资金账号绑定的微信号的个数
            return len(result)

    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        #return -2000, repr(e), []
        raise Exception(repr(e))

#删除统一认证数据库中资金账号所对应的
def deleteDbUserID(userId):
    global g_ConnTyrzDB, g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('isExistInTyrzDb',strerr)
            try: #此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'], passwd=g_TyrzDbDict['passwd'],port=g_TyrzDbDict['port'], charset=g_TyrzDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        #从数据库中删除该userId的签约数据
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = '''DELETE FROM user_identity WHERE
            USER_ID_INFO = (SELECT a.USER_ID_INFO from (SELECT USER_ID_INFO FROM user_identity WHERE USER_ID_INFO = '%s') a)
            AND USER_ID_INFO = '%s';''' % (userId, userId)
            rows = cur.execute(strsql)
            g_ConnTyrzDB.commit()
            return 0, '解约成功', []

    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []

# 查询港股优先账户
def queryPriorMarket(account):
    global g_ConnTyrzDB, g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  # 判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('isExistInTyrzDb', strerr)
            try:  # 此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'],
                                           db=g_TyrzDbDict['db'],
                                           passwd=g_TyrzDbDict['passwd'], port=g_TyrzDbDict['port'],
                                           charset=g_TyrzDbDict['charset'], cursorclass=pymysql.cursors.DictCursor)

        # 从数据库中删除该userId的签约数据
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = '''select ACCOUNT, PRIOR_MARKET from hkstock_prior_market_set where ACCOUNT = '%s';''' % (account)
            rows = cur.execute(strsql)
            g_ConnTyrzDB.commit()
            result = cur.fetchall()

        return 0, '查询股东卡港股优先账户成功', result

    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []

#设置股东卡港股优先账户, 第三个参数：0-表示新增设置，1-表示更新设置
def setPriorMarket(account, market, operflag):
    global g_ConnTyrzDB, g_LastTyrzCheckTime
    try:
        nowtime_s = time.time()
        try:  # 判断连接是否有效
            if nowtime_s - g_LastTyrzCheckTime > g_CheckConnTime:
                g_ConnTyrzDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('isExistInTyrzDb', strerr)
            try:  # 此异常需要捕获
                g_ConnTyrzDB.cursor.close()
                g_ConnTyrzDB.close()
            except Exception:
                pass
            g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'], user=g_TyrzDbDict['user'], db=g_TyrzDbDict['db'],
                                           passwd=g_TyrzDbDict['passwd'], port=g_TyrzDbDict['port'],
                                           charset=g_TyrzDbDict['charset'], cursorclass=pymysql.cursors.DictCursor)

        # 从数据库中删除该userId的签约数据
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            if operflag == 1:
                #如果有记录则更新
                strsql = '''update hkstock_prior_market_set set PRIOR_MARKET = '%s' where ACCOUNT = '%s';''' % (market, account)
            else:
                #没有记录则插入新数据
                strsql = '''insert into hkstock_prior_market_set (ACCOUNT, PRIOR_MARKET) values ('%s', '%s');''' % (account, market)
            rows = cur.execute(strsql)
            g_ConnTyrzDB.commit()

            return 0, '设置股东卡港股优先账户成功', []

    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []


# 券商删除公告信息
def DeleteNotice(jsonreq):
    global g_ConnTyrzReportDB, g_LastReportCheckTime
    try:
        nowtime_s = time.time()
        try:  # 判断连接是否有效
            if nowtime_s - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
        except Exception as e:
            todaydate = time.strftime('%Y-%m-%d', time.localtime())
            exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
            strerr = traceback.format_exc()
            nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
            exceptlogfile.write(nowtime + ':' + strerr)
            exceptlogfile.flush()
            exceptlogfile.close()
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'],
                                                 db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],
                                                 port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],
                                                 cursorclass=pymysql.cursors.DictCursor)

        if 'COMPANY_ID' not in jsonreq:
            return -200, '统一认证[DeleteNotice]:入参银行编码[COMPANY_ID]为空', []
        if 'MSG_ID' not in jsonreq:
            return -200, '统一认证[DeleteNotice]:入参[MSG_ID]为空', []

        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            # strsql = "select COMPANY_ID,MSG_ID,BGN_DATE,END_DATE,TEXT,IsNew,TYPE,TYPE_VALUE from t_stock_report where COMPANY_ID=%s and END_DATE >= DATE_FORMAT('%s','%s');"%(str(jsonreq['COMPANY_ID']),jsonreq['TRD_DATE'],'%Y-%m-%d %H:%i:%s')

            cur.execute('''
            DELETE FROM t_stock_report WHERE MSG_ID = %s AND COMPANY_ID = %s;
            ''' % (jsonreq['MSG_ID'], str(jsonreq['COMPANY_ID'])))

            g_ConnTyrzReportDB.commit()
            g_LastReportCheckTime = nowtime_s
            return 0, '删除公告成功', []
    # except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []


# 券商修改公告信息
def UpdateNotice(jsonreq):
    global g_ConnTyrzReportDB, g_LastReportCheckTime
    try:
        nowtime_s = time.time()
        try:  # 判断连接是否有效
            if nowtime_s - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
        except Exception as e:
            todaydate = time.strftime('%Y-%m-%d', time.localtime())
            exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
            strerr = traceback.format_exc()
            nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
            exceptlogfile.write(nowtime + ':' + strerr)
            exceptlogfile.flush()
            exceptlogfile.close()
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'],
                                                 db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],
                                                 port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],
                                                 cursorclass=pymysql.cursors.DictCursor)

        if 'COMPANY_ID' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参银行编码[COMPANY_ID]为空', []
        if 'MSG_ID' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参[MSG_ID]为空', []
        if 'BGN_DATE' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参[BGN_DATE]为空', []
        if 'END_DATE' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参[END_DATE]为空', []
        if 'TEXT' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参[TEXT]为空', []
        if 'TYPE' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参[TYPE]为空', []
        if 'TYPE_VALUE' not in jsonreq:
            return -200, '统一认证[UpdateNotice]:入参[TYPE_VALUE]为空', []
        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:

            updateSql = '''
                UPDATE t_stock_report t
                SET t.BGN_DATE = %s,
                 t.END_DATE = %s,
                 t.TEXT = %s,
                 t.TYPE = %s,
                 t.TYPE_VALUE = %s,
                 t.Updatetime = NOW()
                WHERE
                    t.COMPANY_ID = %s
                AND t.MSG_ID = %s;
            '''
            cur.execute(updateSql, (
            jsonreq['BGN_DATE'], jsonreq['END_DATE'], jsonreq['TEXT'], jsonreq['TYPE'], jsonreq['TYPE_VALUE'],
            str(jsonreq['COMPANY_ID']), jsonreq['MSG_ID']))
            g_ConnTyrzReportDB.commit()
            g_LastReportCheckTime = nowtime_s
            return 0, '修改公告成功', []
    # except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "tyrzlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000, repr(e), []

# 查询客户号 400001 测试环境专用
def queryAccount(jsonreq):
    global g_ConnTyrzReportDB,g_LastReportCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('queryAccount',strerr)
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'], db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'USER_ID' not in jsonreq:
            return -200,'客户号查询[queryAccount]:用户标识入参[USER_ID]为空',[]

        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = "SELECT USER_ID_INFO ACCOUNT FROM user_identity WHERE USER_ID_TYPE = '3' AND USER_ID_INFO = (SELECT DISTINCT USER_ID_INFO FROM user_identity WHERE USER_ID_INFO = '%s');"%(str(jsonreq['USER_ID']))

            rows = cur.execute(strsql)
            datalist=cur.fetchall()
            for eachrow in datalist:
                for eachkey in eachrow:
                    eachrow[eachkey] = str(eachrow[eachkey])
            g_ConnTyrzReportDB.commit()
            g_LastReportCheckTime = nowtime_s
            return 0,'客户号查询成功',datalist
    #except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]


# 查询腾讯id 400002 测试环境专用
def queryUserId(jsonreq):
    global g_ConnTyrzReportDB,g_LastReportCheckTime
    try:
        nowtime_s = time.time()
        try:  #判断连接是否有效
            if nowtime_s - g_LastReportCheckTime > g_CheckConnTime:
                g_ConnTyrzReportDB.ping()
        except Exception as e:
            strerr = traceback.format_exc()
            kwlpyfunction.WriteLocalLog('queryUserId',strerr)
            try:
                g_ConnTyrzReportDB.cursor.close()
                g_ConnTyrzReportDB.close()
            except Exception:
                pass
            g_ConnTyrzReportDB = pymysql.connect(host=g_TyrzReportDbDict['host'], user=g_TyrzReportDbDict['user'], db=g_TyrzReportDbDict['db'], passwd=g_TyrzReportDbDict['passwd'],port=g_TyrzReportDbDict['port'], charset=g_TyrzReportDbDict['charset'],cursorclass=pymysql.cursors.DictCursor)

        if 'ACCOUNT' not in jsonreq:
            return -200,'腾讯id查询[queryUserId]:客户号入参[ACCOUNT]为空',[]

        with g_ConnTyrzReportDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = "SELECT USER_ID_INFO USER_ID FROM user_identity WHERE USER_ID_TYPE = '1' AND USER_ID_INFO = (SELECT DISTINCT USER_ID_INFO FROM user_identity WHERE USER_ID_INFO = '%s');"%(str(jsonreq['ACCOUNT']))

            rows = cur.execute(strsql)
            datalist=cur.fetchall()
            for eachrow in datalist:
                for eachkey in eachrow:
                    eachrow[eachkey] = str(eachrow[eachkey])
            g_ConnTyrzReportDB.commit()
            g_LastReportCheckTime = nowtime_s
            return 0,'腾讯id查询成功',datalist
    #except pymysql.OperationalError as e:
    except Exception as e:
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"tyrzlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return -2000,repr(e),[]


if __name__ == '__main__':
    # reqjson =  {"COMPANY_ID":"12900","TRD_PWD":"e259e4b3585cdc9f","USER_ID_CLS":"Q","USER_ID":"jwl_test"}
    # try:
    #     print(T0005001_makesession(reqjson))
    # except Exception as e:
    #     print('捕获到异常'+repr(e))


    new_session = '18a0b7acc9a96478a7753d4ef5b447605ae9334b900bdca0850e70cdd3fdae76e5ae1ff56254738c715695be458057432833850eb9e93502ecf8846b13c1a92a1acc6a55859c996909dec1ef24df517d339b02713fe643ccf2756839cfc17faf91cf9123d73e373c5ceef2a7f81c12ae504821472ae68be4ca6e410c0bb163586d4199a28307a813514bd92bc0c2f68c29a8c1f891f81a578572327a30d357c10d0aacf63dff21f3cffc7ca1d455b32601635d17b9c0a4fa9a12afe18595a0afc9d9ff62d2403a964de27f75370f9e4c65c7efc39c707362195bfeca9d4f4a77aa4ec66929da17213d6d8148f314cdd2e19caba7bbfd73ecdba1a9dd70e1ec7dfdc1d5cc41454df3541e906d264eacd90ec8d61ac3cf3462863542a8f4582d57a7a22c8ee9c9395921dbe82df244453112b1b3d9aaae0514c867b4339cc10ecb'
    ddd = des_decrypt(new_session, '410301')
    print('666666', ddd)

    sess = '''{"1":[{"2":"12900","3":"*********03","4":"*********03","5":"0891","6":"********************************************************00005ca6ef91783f26a857c52157562ce89e842db519a56edce01155d9853c0e9b013cc53504d79fd5c0d48ba573126093aca780afced61ce48fe670a4ca905cdee7031d43d66f3dda0e1c92205d0d678b91f9fd9d4367da045281b10f5877058976547e1a552539cbc537f11f3c806718cee4225767db095c3fcd8b15dc73b8c2f1","7":"7d790a1cf5b2f4a067db1f7e77fbdb90","8":[{"9":"0","a":"0207774277"},{"9":"1","a":"A561940764"}],"c":""}],"b":"os-ppuJeJ6I0YWUNUVzuyqAiY3M6","0":"719133"}'''
    session = des_encrypt(sess, '410301')
    print('session', session)