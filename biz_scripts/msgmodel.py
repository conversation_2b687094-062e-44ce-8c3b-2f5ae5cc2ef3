# 短信验证码的有效时间，单位：秒
g_efftive_time = 60

# g_sms_url = 'http://180.169.107.62:9599/msg/send?sourceId=10&receiverId=8999&msgBody='
#g_sms_url = 'http://180.169.107.19:9030/sms-notice/msg/send?sourceId=10&receiverId=8999&msgBody='
g_sms_url = 'http://192.168.61.227:9599/sms-notice/msg/send?sourceId=10&receiverId=8999&msgBody='

# 发送短信验证码的webservice

#短信内容配置
g_sms_dict = {
    '0' : '修改密码',
    '1' : '重置密码',
    '2' : '新增银行卡',
    '3' : '修改手机号',
    '4' : '修改资金密码',
    '5' : '修改交易密码',
    '6' : '绑定证券账户'
}

#g_sms_content_template = '您正在使用腾讯自选股客户端%s，验证码是：%s，请勿透露给其他人。若非本人操作，请尽快联系客服：95310'
g_sms_content_template = '您正在使用国金证券服务%s，验证码是：%s，请勿透露给其他人。'

g_sms_switch = 'new_sms'
#g_sms_switch = 'old_sms'


new_g_sms_url = 'http://nginx-yjbapi.yjbyw.gj:36000/yjbapi/upush/source'
providerId = 'jwl-txzxg'
auditorId = 'jwl-txzxg'
#bizType = 'TYDX'
bizType = 10084

