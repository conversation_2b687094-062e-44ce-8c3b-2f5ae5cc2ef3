#-------------------------------------------------------------------------------
# Name:        log.py
# Purpose:
#
# Author:      Administrator
#
# Created:     
# Copyright:   (c) Administrator
# Licence:     
#-------------------------------------------------------------------------------

import logging
import sys,os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir)))

from anttestpython.iLoggerHandle import ILoggerHandler

format_dict = {
   1 : logging.Formatter('[%(asctime)s][file:%(filename)s][line:%(lineno)d][%(thread)d]: %(message)s'),
   2 : logging.Formatter('[%(asctime)s][line:%(lineno)d][%(thread)d]: %(message)s'),
   3 : logging.Formatter('[%(asctime)s][file:%(filename)s][line:%(lineno)d]: %(message)s'),
   4 : logging.Formatter('[%(asctime)s][file:%(filename)s]: %(message)s'),
   5 : logging.Formatter('[%(asctime)s]: %(message)s'),
   6 : logging.Formatter('%(message)s')
}

loglevel_dict = {
   1 : logging.DEBUG,
   2 : logging.INFO,
   3 : logging.WARNING,
   4 : logging.ERROR,
   5 : logging.CRITICAL
}

class Logger():
    def __init__(self, loglevel = 1, logformat = 1):
        '''
           指定保存日志的文件路径，日志级别，以及调用文件
           将日志存入到指定的文件中
        '''
        level = loglevel_dict.get(int(loglevel), logging.WARNING)
        # 创建一个logger
        self.logger = logging.getLogger()
        self.logger.setLevel(logging.DEBUG)

        # 创建一个handler，用于写入日志文件
        fh = logging.FileHandler('antlog.log')
        fh.setLevel(logging.DEBUG)

        # 再创建一个handler，用于输出到控制台
        #ch = logging.StreamHandler()
        #ch.setLevel(level)

        ih = ILoggerHandler(maxBytes=10*1024*1024,backupCount=100)
        ih.setLevel(level)

        # 定义handler的输出格式
        #formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        formatter = format_dict[int(logformat)]
        fh.setFormatter(formatter)
        #ch.setFormatter(formatter)
        ih.setFormatter(formatter)

        # 给logger添加handler
        self.logger.addHandler(fh)
        #self.logger.addHandler(ch)
        self.logger.addHandler(ih)

    def getlog(self):
        return self.logger


