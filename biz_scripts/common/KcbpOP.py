##############################################################################################
# KCBP API封装类（python）
#
# Author : pengzh
# Date   : 20160323
#
##############################################################################################

import ctypes
import re
import os
import sys
from json import *

#加入上层目录
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir)))
from common.Kesb import KesbClient
from bin.context import Logger

szErrCode = ctypes.create_string_buffer(1024)
szErrMsg = ctypes.create_string_buffer(1024)
nErrCode = ctypes.c_int()

class KCBPCli():
	"""docstring for KCBPCli"""
	def __init__(self):
		self.kesbCli = None
		Logger.info("加载KesbClient.dll开始")
		try:
			self.kesbCli = KesbClient()
		except OSError as oserr:
			Logger.error("加载KesbClient.dll出错:" + str(oserr))
		
	def	__del__(self):
		if self.kesbCli is not None:
			iRetCode = self.kesbCli.KESBCLI_Exit()
			if iRetCode != 0:
				Logger.error("KESBCLI_Exit Fail:")
			else:
				Logger.info("KESBCLI_Exit Ok")

	# 建立连接
	def connectServer(self, connectIp, connectPort, req, ans):
		iRetCode = self.kesbCli.KESBCLI_ConnectServer(connectIp, connectPort, req, ans)
		if iRetCode != 0:
			Logger.error("KCBPCLI_ConnectServer fail:" + str(iRetCode))
		else:
			Logger.error("KCBPCLI_ConnectServer OK:")

		return iRetCode

	# 发起请求
	def CallProgramAndCommit(self, strLbm, reqDataMap={}):
		resultMap = {}
		reqDataStr = JSONEncoder().encode(reqDataMap)
		#print("["+ strLbm +"]请求入参:" + reqDataStr)
		Logger.info("CallProgramAndCommit:["+ strLbm +"]请求入参:")
		Logger.info(reqDataStr)
		self.kesbCli.KESBCLI_BeginWrite()
		for key in reqDataMap:
			self.kesbCli.KESBCLI_SetValue(str(key), str(reqDataMap.get(key)))
			pass

		iRetCode = self.kesbCli.KESBCLI_CallProgramAndCommit(strLbm)
		if iRetCode != 0:
			Logger.error("KESBCLI_CallProgramAndCommit fail ERR_CODE[" + str(iRetCode) + "]")
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = "KESBCLI_GetErr fail"
		else:
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = ""

		return resultMap;

	# 大文件传输
	def CallProgramAndCommitBigData(self, strLbm, reqDataMap={}, reqBigDataMap={}):
		resultMap = {}
		reqDataStr = JSONEncoder().encode(reqDataMap)
		Logger.info("CallProgramAndCommitBigData:["+ strLbm +"]请求入参:")
		Logger.info(reqDataStr)

		self.kesbCli.KESBCLI_BeginWrite()
		#define KESB_PARAM_RESERVED	 7
		self.kesbCli.KESBCLI_SetSystemParam(7, reqDataMap.get('COMPANY_ID'));

		for key in reqDataMap:
			self.kesbCli.KESBCLI_SetValue(str(key), str(reqDataMap.get(key)))
			pass

		#for key in reqBigDataMap:
		#	print(str(reqBigDataMap.get(key)))
		#	print(len(reqBigDataMap.get(key)))
		#	self.kesbCli.KESBCLI_RsSetValByName(str(key), str(reqBigDataMap.get(key)), len(reqBigDataMap.get(key)))
		#	pass
		data = str(reqBigDataMap.get('IMG_DATA'))

		self.kesbCli.KESBCLI_SetVal('IMG_DATA', data, len(data))

		iRetCode = self.kesbCli.KESBCLI_CallProgramAndCommit(strLbm)
		if iRetCode != 0:
			Logger.error("KESBCLI_CallProgramAndCommit fail ERR_CODE[" + str(iRetCode) + "]")
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = "KESBCLI_GetErr fail"
		else:
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = ""

		return resultMap;

	# 获取返回数据，返回数据结构：{'RET_CODE':'','RET_MSG':'','SEC_RST':[{},{}...]}
	def GetResultSet(self, strLbm):
		resultMap = {}

		iRetCode = self.kesbCli.KESBCLI_RsOpen();
		if iRetCode != 0:
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = "KESBCLI_RsOpen Fail"
			resultMap['SEC_RST'] = {}
			Logger.error("["+ strLbm +"],RET_CODE[" + str(iRetCode) + "]KESBCLI_RsOpen Fail:")
			return resultMap
		iRetCode = self.kesbCli.KESBCLI_RsFetchRow();
		if iRetCode != 0:
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = "KESBCLI_RsFetchRow Fail"
			resultMap['SEC_RST'] = {}
			Logger.error("["+ strLbm +"]:" + resultMap)
			return resultMap

		szColNames = ctypes.create_string_buffer(2048)
		iRetCode = self.kesbCli.KESBCLI_RsGetColNames(szColNames,len(szColNames))
		if iRetCode != 0:
			resultMap['RET_CODE'] = iRetCode
			resultMap['RET_MSG'] = "KESBCLI_RsGetColNames Fail"
			resultMap['SEC_RST'] = {}
			Logger.error("["+ strLbm +"],RET_CODE[" + str(iRetCode) + "]KESBCLI_RsGetColNames Fail:")
			return resultMap

		#取第一结果集
		szColNames = str(szColNames,'utf-8') #bytes转str最好加utf-8加密或者value进行gbk解密,就会去掉前面的b
		iRetCode = szColNames.find("MSG_CODE")
		if iRetCode != -1:
			iRetCode = self.kesbCli.KESBCLI_RsGetColByName("MSG_CODE",szErrCode)
			iRetCode = self.kesbCli.KESBCLI_RsGetColByName("MSG_TEXT",szErrMsg)
		else:
			iRetCode = self.kesbCli.KESBCLI_RsGetColByName("CODE",szErrCode)
			iRetCode = self.kesbCli.KESBCLI_RsGetColByName("MSG",szErrMsg)

		resultMap['RET_CODE'] = int(szErrCode.value.decode('gbk'))
		resultMap['RET_MSG'] =  szErrMsg.value.decode('gbk')

		Logger.info("["+ strLbm +"],取第一结果集:RET_CODE[" + str(resultMap.get('RET_CODE')) + "]" + resultMap.get('RET_MSG'))

		#取第二结果集
		iRetCode = self.kesbCli.KESBCLI_RsMore()
		if iRetCode != 0:
			resultMap['SEC_RST'] = {}
			return resultMap


		iColNum = ctypes.c_int()
		iRetCode = self.kesbCli.KESBCLI_RsGetColNum(ctypes.byref(iColNum))
		if iRetCode != 0:
			resultMap['SEC_RST'] = {}
			Logger.error("["+ strLbm +"],RET_CODE[" + str(iRetCode) + "]KESBCLI_RsGetColNum Fail:")
			return resultMap

		szColName = ctypes.create_string_buffer(256)
		szColValueT = ctypes.create_string_buffer(2048)
		#szColValue = ctypes.create_string_buffer(1024 * 1024 * 10)
		secRstLst = []
		while(self.kesbCli.KESBCLI_RsFetchRow() == 0):
			secRstMap = {}
			iCurrentCol = 1
			while(iCurrentCol <= iColNum.value):
				self.kesbCli.KESBCLI_RsGetColName(iCurrentCol,szColName,len(szColName))
				#self.kesbCli.KESBCLI_RsGetColByName(str(szColName.value,"utf-8"),szColValue)
				if(szColName.value.decode('gbk') == 'FILE_BASE64'): #若是要用GetVal
					#pszBigValue = ctypes.create_string_buffer(1024 * 1024 * 10)	#最大支持10M
					szColValue = ctypes.c_char_p()
					nLen= ctypes.c_int()
					self.kesbCli.KESBCLI_RsGetValByName(str(szColName.value,"utf-8"), ctypes.pointer(szColValue), ctypes.byref(nLen))
					#测试时打开
					#file_objectout = open('imgresult.txt', 'w')
					#file_objectout.write(szColValue.value.decode())
					#file_objectout.close()

					secRstMap[str(szColName.value,"utf-8")] = szColValue.value.decode("gbk")
					secRstMap['IMG_DATA_LEN'] = nLen.value
				else:
					self.kesbCli.KESBCLI_RsGetColByName(str(szColName.value,"utf-8"), szColValueT)
					secRstMap[str(szColName.value,"utf-8")] = szColValueT.value.decode("gbk")

				#secRstMap[str(szColName.value,"utf-8")] = szColValue.value.decode('gbk')
				iCurrentCol = iCurrentCol + 1
			
			secRstLst.append(secRstMap)

		resultMap['SEC_RST'] = secRstLst
		#Logger.info("[" + strLbm +"]:" + "第二结果集：" + "".join(secRstLst))

		return resultMap

	# 退出释放资源
	def	exit(self):
		iRetCode = self.kesbCli.KESBCLI_Exit()
		if iRetCode != 0:
			Logger.error("KESBCLI_Exit Fail:")
			return iRetCode

		kesbCli = None
		Logger.info("KESBCLI_Exit Successful:")
		return iRetCode

