##############################################################################################
# KCBP API Python版本
#
# Author : pengzh
# Date   : 20160323
#
##############################################################################################
from ctypes import *
import ctypes
from json import *
import struct
import sys,os
import json
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir)))
from common.log import Logger   


#define KESB_OPTION_CONNECT         0
#define KESB_OPTION_TIMEOUT         1
#define KESB_OPTION_TRACE           2
#define KESB_OPTION_CRYPT           3
#define KESB_OPTION_COMPRESS        4
#define KESB_OPTION_PARITY          5
#define KESB_OPTION_SEQUENCE        6
#define KESB_OPTION_CURRENT_CONNECT 7
#define KESB_OPTION_CONNECT_HANDLE  8
#define KESB_OPTION_CONFIRM         9
#define KESB_OPTION_NULL_PASS       10
#define KESB_OPTION_TIME_COST       11
#define KESB_OPTION_CONNECT_EX      12
#define KESB_OPTION_CURRENT_CONNECT_EX 13
#define KESB_OPTION_AUTHENTICATION  14
#define KESB_OPTION_GROUPID         15
#define KESB_OPTION_USE_GROUPID     16
#define KESB_OPTION_RECEIVE_PACKET_BY_GROUP 17
#define KESB_OPTION_XA              18
#define KESB_OPTION_CURRENT_XA      19
#define KESB_OPTION_CONNECTION_POOL 20
#define KESB_OPTION_WRITELOGFILE 21

#typedef struct
#{
#    char szServerName[KESB_SERVERNAME_MAX + 1];
#    int nProtocal;
#    char szAddress[KESB_DESCRIPTION_MAX + 1];
#    int nPort;
#    char szSendQName[KESB_DESCRIPTION_MAX + 1];
#    char szReceiveQName[KESB_DESCRIPTION_MAX + 1];
#    char szReserved[KESB_DESCRIPTION_MAX + 1];
#}
#tagKESBConnectOption;

szErrCode = ctypes.create_string_buffer(1024)
szErrMsg = ctypes.create_string_buffer(1024)

class tagKESBConnectOption(Structure):
    """docstring for tagKESBConnectOption"""
    _fields_ = [('szServerName', c_char * 33),('nProtocal', c_int),('szAddress',  c_char * 33),('nPort', c_int),
            ('szSendQName',  c_char * 33),('szReceiveQName',  c_char * 33),('szReserved',  c_char * 33)] 
    _pack_ = 1


class KesbClient:
    def __init__(self):
        self.handle = c_void_p(None)
        self.kesbcli = cdll.LoadLibrary('libKESBCLI.so')
        self.kesbcli.KESBCLI_Init(pointer(self.handle))

        #self.kesbcli = cdll.LoadLibrary('KesbClient.dll')
        #self.kesbcli.Py_KESBCLI_Init()

    def KESBCLI_ConnectServer(self,strServerIp,strServerPort,strReqName,strAnsName):
        szAddress =bytes(strServerIp, encoding = "utf-8")
        szSendQName =bytes(strReqName, encoding = "utf-8")
        szReceiveQName =bytes(strAnsName, encoding = "utf-8")

        print(str(szAddress) + ";" + str(szSendQName) + ";" + str(szReceiveQName))

        stKESBConnectOption = tagKESBConnectOption(b'KCBP01',c_int(0),szAddress,c_int(strServerPort),szSendQName,szReceiveQName,b'');
        iRetCode = self.kesbcli.KESBCLI_SetOptions(self.handle, 0, byref(stKESBConnectOption), 173);
        if iRetCode != 0:
            Logger.error("KESBCLI_SetOptions fail, ERROR_CODE[" + str(iRetCode) + "]")
            return iRetCode

        confirm = c_int(1)
        iRetCode = self.kesbcli.KESBCLI_SetOptions(self.handle, 9, byref(confirm), 4);
        if iRetCode != 0:
            Logger.error("KESBCLI_SetOptions fail, ERROR_CODE[" + str(iRetCode) + "]")
            return iRetCode

        iRetCode = self.kesbcli.KESBCLI_ConnectServer(self.handle, b'KCBP01', b'KCXP00', b'888888');
        if iRetCode != 0:
            #Logger.error("KESBCLI_ConnectServer fail, ERROR_CODE[" + str(iRetCode) + "]")
            return iRetCode
        return iRetCode
    def KESBCLI_SetOptions(self, iOption, pOption, iLen):
        iRetCode = self.kesbcli.KESBCLI_SetOptions(self.handle, iOption, pOption, iLen)
        return iRetCode

    def SetTimeout(self,iSecond):
        iTimeout = c_int(iSecond)
        return self.kesbcli.KESBCLI_SetOptions(self.handle, c_int(1), byref(iTimeout), 4)

    def KESBCLI_BeginWrite(self):
        return self.kesbcli.KESBCLI_BeginWrite(self.handle)

    def KESBCLI_SetValue(self, strKey, strValue):
        #strKey =bytes(strKey, encoding = "utf-8")
        #strValue =bytes(strValue, encoding = "utf-8")
        iRetCode = self.kesbcli.KESBCLI_SetValue(self.handle, strKey, strValue)
        return iRetCode

    def KESBCLI_RsSetValByName(self, strKey, strValue, iSize):
        #strKey =bytes(strKey, encoding = "utf-8")
        iRetCode = self.kesbcli.KESBCLI_RsSetValByName(self.handle, strKey, strValue, iSize)
        return iRetCode

    def KESBCLI_SetVal(self, strKey, strValue, iSize):
       # strKey =bytes(strKey, encoding = "utf-8")
        iSize = len(strValue)
        #strValue =bytes(strValue, encoding = "utf-8")
        iRetCode = self.kesbcli.KESBCLI_SetVal(self.handle, strKey, strValue, iSize)
        return iRetCode

    def KESBCLI_GetValue(self, strKey, szValue):
        #strKey =bytes(strKey, encoding = "utf-8")
        iRetCode = self.kesbcli.KESBCLI_GetValue(self.handle, strKey, szValue, len(szValue))
        return iRetCode

    def KESBCLI_RsGetValByName(self, strKey, szValue, iSize):
        #strKey =bytes(strKey, encoding = "utf-8")
        iRetCode = self.kesbcli.KESBCLI_RsGetValByName(self.handle, strKey, szValue, iSize)
        return iRetCode

    def KESBCLI_SetSystemParam(self, iIndex, szValue):
        #szValue =bytes(szValue, encoding = "utf-8")
        iRetCode = self.kesbcli.KESBCLI_SetSystemParam(self.handle, iIndex, szValue)
        return iRetCode

    def KESBCLI_CallProgramAndCommit(self,strLbm):
        strLbm = strLbm.encode('utf-8')
        strLbm = c_char_p(strLbm)
        iRetCode = self.kesbcli.KESBCLI_CallProgramAndCommit(self.handle, strLbm, None)
        return iRetCode

    def KESBCLI_RsOpen(self):
        return self.kesbcli.KESBCLI_RsOpen(self.handle)

    def KESBCLI_RsFetchRow(self):
        return self.kesbcli.KESBCLI_RsFetchRow(self.handle)

    def KESBCLI_RsGetColNames(self,szInfo,nLen):
        return self.kesbcli.KESBCLI_RsGetColNames(self.handle, szInfo,nLen)

    def KESBCLI_RsGetColName(self,nCol,szName,nLen):
        return self.kesbcli.KESBCLI_RsGetColName(self.handle, nCol,szName,nLen)

    def KESBCLI_RsGetColByName(self,strKeyName,szVlu):
        #strKeyName =bytes(strKeyName, encoding = "utf-8")
        return self.kesbcli.KESBCLI_RsGetColByName(self.handle, strKeyName,szVlu)

    def KESBCLI_RsMore(self):
        return self.kesbcli.KESBCLI_RsMore(self.handle);

    def KESBCLI_RsGetRowNum(self,pnRows):
        return self.kesbcli.KESBCLI_RsGetRowNum(self.handle, pnRows);

    def KESBCLI_RsGetColNum(self,pnCols):
        return self.kesbcli.KESBCLI_RsGetColNum(self.handle, pnCols);

    def KESBCLI_GetErr(self,pnErrCode,pszErrMsg):
        return self.kesbcli.KESBCLI_GetErr(self.handle, pnErrCode,pszErrMsg);

    def KESBCLI_GetErrorCode(self,pnErrno):
        return self.kesbcli.KESBCLI_GetErrorCode(self.handle, pnErrno);

    def KESBCLI_GetErrorMsg(self,pszErrMsg):
        return self.kesbcli.KESBCLI_GetErrorMsg(self.handle, pszErrMsg);

    def KESBCLI_Exit(self):
        return self.kesbcli.KESBCLI_Exit(self.handle);

    def KESBCLI_RsGetCursorName(self, pszCursorName, iLen):
        return self.kesbcli.KESBCLI_RsGetCursorName(self.handle, pszCursorName, iLen) ;

    def KESBCLI_DisConnect(self):
        return self.kesbcli.KESBCLI_DisConnect(self.handle) ;

    def KESBCLI_DisConnectForce(self):
        return self.kesbcli.KESBCLI_DisConnectForce(self.handle) ;
    # 获取返回数据，返回数据结构：{'RET_CODE':'','RET_MSG':'','json_db_rowsets':[{},{}...]}
    def GetResultSet(self, strLbm = 'undefined'):
        resultJson = {}
        iRetCode = self.kesbcli.KESBCLI_RsOpen(self.handle);
        if iRetCode != 0:
            resultJson['json_db_rowsets'] = {}
            return resultJson

        pszTemp = create_string_buffer(256)
        szColNames = ctypes.create_string_buffer(2048)
        szColName = ctypes.create_string_buffer(256)
        szColValueT = ctypes.create_string_buffer(2048)

        while True:  
              self.kesbcli.KESBCLI_RsGetCursorName(self.handle, pszTemp, len(pszTemp))
              #print('cursor:' + str(pszTemp.value))

              iRetCode = self.kesbcli.KESBCLI_RsGetColNames(self.handle,szColNames,len(szColNames))
              if iRetCode != 0:
                  resultJson['json_db_rowsets'] = {}
                  return (iRetCode, "KESBCLI_RsGetColNames Fail", resultJson)
              
              #szColNames = str(szColNames,'utf-8') #bytes转str最好加utf-8加密或者value进行gbk解密,就会去掉前面的b
              
              iColNum = ctypes.c_int()
              iRetCode = self.kesbcli.KESBCLI_RsGetColNum(self.handle, ctypes.byref(iColNum))
              if iRetCode != 0:
                  resultJson['json_db_rowsets'] = {}
                  return (iRetCode, "KESBCLI_RsGetColNum Fail", resultJson)

              #print('iColNum:' + str(iColNum))
              #szColValue = ctypes.create_string_buffer(1024 * 1024 * 10)
              jsonRowsTemp = []
              listColNames = str(szColNames.value,'utf-8').split(',') ;
              while(self.kesbcli.KESBCLI_RsFetchRow(self.handle) == 0):
                  jsonSingleRow = {}
                  #print('begin row copy')
                  for sColNameTemp in listColNames:
                      #print('col name:' + str(szColName.value))
                      #self.kesbcli.KESBCLI_RsGetColByName(str(szColName.value,"utf-8"),szColValue)
                      sColNameByte = sColNameTemp.encode('utf-8')
                      if(sColNameTemp == 'FILE_BASE64'): #若是要用GetVal
                          #pszBigValue = ctypes.create_string_buffer(1024 * 1024 * 10)    #最大支持10M
                          szColValue = ctypes.c_char_p()
                          nLen= ctypes.c_int()
                          self.kesbcli.KESBCLI_RsGetValByName(self.handle, sColNameByte, ctypes.pointer(szColValue), ctypes.byref(nLen))
                          #测试时打开
                          #file_objectout = open('imgresult.txt', 'w')
                          #file_objectout.write(szColValue.value.decode())
                          #file_objectout.close()
              
                          jsonSingleRow[sColNameTemp] = szColValue.value.decode("gbk")
                          jsonSingleRow['IMG_DATA_LEN'] = nLen.value
                      else:
                          self.kesbcli.KESBCLI_RsGetColByName(self.handle, sColNameByte, szColValueT)
                          #print('col value:' + str(szColValueT.value))
                          jsonSingleRow[sColNameTemp] = szColValueT.value.decode("gbk")
              
                  jsonRowsTemp.append(jsonSingleRow)
              
              resultJson[pszTemp.value.decode('gbk')] = jsonRowsTemp

              iRet= self.kesbcli.KESBCLI_RsMore(self.handle);
              if iRet != 0:
                  break ;
              #Logger.info("[" + strLbm +"]:" + "第二结果集：" + "".join(jsonRowsTemp))
        #realResult = {} ;
        #realResult['json_db_rowsets'] = resultJson ;

        return (0, '业务运行成功', resultJson)

    def SetReqWithJsonFormat(self, reqJson):
        try:
            jsonObj = json.loads(reqJson)
        except Exception as e:
            return (-1, 'json parse failed:' + str(reqJson) + str(e))
        for key in jsonObj.keys():
        #使用ensure_ascii=False来避免对UTF-8中文的转义
            self.kesbcli.KESBCLI_SetValue(self.handle, key.encode(), json.dumps(jsonObj[key],ensure_ascii=False).encode('gbk'))
        return 0 ;

    #获取完整的KCBP应答包,可以用re去解析
    #def KCBPCLI_GetAns(self):
    #    pans = self.kesbcli.KCBPCLI_GetAns(self.kesbcliHandle)
    #    pans = c_char_p(pans)
    #    pans = pans.value    #bytes
    #    return pans.decode('utf-8')   #bytes是utf-8解码

    if __name__ == "__main__":
        import Kesb
        cli = Kesb.KesbClient()
        iRet = cli.KESBCLI_ConnectServer('192.168.196.131', 21000, 'req_simu', 'ans_simu')
        if iRet != 0:
            print('Connect Failed:' + iRet) 
        pBuffer = create_string_buffer(2048)

        for iLoopIndex in range(1,1000):
            
            cli.KESBCLI_SetValue('a', '0000')
            cli.KESBCLI_SetValue('b', '1111')
            cli.KESBCLI_GetValue('', pBuffer) 
            #print(pBuffer.value)

            iRet = cli.KESBCLI_CallProgramAndCommit('01019701')
            if iRet != 0:
                print('CallProgram Failed:' + str(iRet)) 

            result = cli.GetResultSet("aaa")
            #print('result:' + str(result[2]))

        cli.KESBCLI_GetValue('', pBuffer)
        print(pBuffer.value)

        iLen = c_int(1024)
        print(iLen)
        cli.KESBCLI_RsOpen()
        cli.KESBCLI_RsGetCursorName(pBuffer, 1024)
        print(pBuffer.value)

        cli.KESBCLI_RsGetColNames(pBuffer, 1024)
        print(pBuffer.value)
        print('len =' + str(len(pBuffer)))

        cli.KESBCLI_RsFetchRow() ;
        cli.KESBCLI_RsGetColByName('MSG_CODE', pBuffer)
        print(pBuffer.value)
        cli.KESBCLI_RsGetColByName('MSG_TEXT', pBuffer)
        print(str(pBuffer.value.decode('gbk')))

        iRet = cli.KESBCLI_RsMore()
        if iRet != 0:
            print('KESBCLI_RsMore failed' + str(iRet))

        cli.KESBCLI_RsGetColNames(pBuffer, 1024)
        print(pBuffer.value)