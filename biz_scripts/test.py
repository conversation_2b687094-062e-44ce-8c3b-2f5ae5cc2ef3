import KwlEncrypt
import kwlpyfunction

# DESCli = KwlEncrypt.CDes()
# #SESSION 加密  入参原文 密钥
# def session_encrypt(plain_text,des_key):
#     return DESCli.SessionEnCrypt(plain_text,des_key)
#
# #SESSION 解密  入参原文 密钥
# def session_decrypt(cipher_text,des_key):
#     return DESCli.SessionDeCrypt(cipher_text, des_key)
#
# #DES 加密  入参原文 密钥
# def des_encrypt(plain_text,des_key):
#     return DESCli.DESEnCrypt(plain_text,des_key)
#
# #DES 解密  入参原文 密钥
# def des_decrypt(cipher_text,des_key):
#     return DESCli.DESDeCrypt(cipher_text, des_key)

a = '''{"0": "719039", "user_id": "os-33002400", "1": [{"5": "33", "4": "33002400", "3": "33002400", "2": "12900", "7": "2019-11-26 20:00:30", "8": [], "6": "356b6b76bea5154f"}]}'''
str1 = kwlpyfunction.session_encrypt(a, '410301')
print(str1)
str1 = '1177179dae3df9e9c8c3ccfebce44f3e0068bf8ca1e08420ed26056f3070979fe63df4c2ce1d315e922b9dddc3d712ef9213b56a7109935410af958ba46dbeffc78a0204264fce280cfeac1ebdec736bbf7cef565813d4f2d5ad9834d4667488067fad20a62d0c1aa89ed206f78a7075a3269dfcb4e72d5d'
str2 = kwlpyfunction.session_decrypt(str1, '410301')
print(str2)
