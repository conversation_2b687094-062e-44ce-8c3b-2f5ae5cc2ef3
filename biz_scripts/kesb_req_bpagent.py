#把bpagent的业务加入其中
from KWL import *
import kwlpyfunction
from kcxpconfig import *
if kwlpyfunction.isLinuxSystem():
    from KWL_Nginx import *
else:
    def GetKWLParam(sKey):
        return '99999'
import time
from bpagentspd import * #bpagent配置项
import json
import traceback
import os
import threading
import asyncio
import CHST2Connector
import KinginxSys
import kafka_work
from kwl_py_log import *

#协程
def loopWorker(loopInput):
    set_event_loop(loopInput)
    loopInput.run_forever()

kwl_py_write_log('init thread', 'kesb_req_bpagent', ZTLOG_INFO)
#起协程推动线程
loopInstance = KinginxSys.loopInstance
threadInstanse = threading.Thread(target=loopWorker, args=(loopInstance,))
threadInstanse.start()

CHST2Connector.init_CHST_instant(loopInstance)
kwl_py_write_log('init_CHST_instant succ ', 'kesb_req_bpagent', ZTLOG_INFO)
kafka_work.init_kafka()

#异常日志存放地方
if not os.path.isdir('logs'):
    os.makedirs('logs')

#制造应答字符串
def MakeAnsStr(code,msg,debug,session):
    anstext = {"ANSWERS":[{"ANS_COMM_DATA":None,"ANS_MSG_HDR":{"MSG_CODE":str(code),"MSG_LEVEL":"0","MSG_TEXT":msg,"DEBUG_MSG":debug,"SESSION":session}}]}
    return json.dumps(anstext,ensure_ascii=False)

def kesb_req(ref_request, req_content):
    try:
        kwl_py_write_log('请求串:%s'%(req_content), 'send_from_wzq', ZTLOG_INFO, msgid=ref_request)
        #根据reqFunctionDict，按功能号调不同的接口
        jsonkwlreq = json.loads(req_content)
        serviceid = jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']['SERVICE_ID']
        asyncio.run_coroutine_threadsafe(g_reqBpagentFunctionDict[serviceid](ref_request,jsonkwlreq), loopInstance)

        return(0, 'succ', '')
    except Exception as e:
        anstext = MakeAnsStr(-100,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:抛出异常应答:%s,对应请求为:%s'%(ref_request, anstext,req_content), 'SetErrMsg', ZTLOG_INFO, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return(0, 'failed', 'nginx发生异常')

def IsInTest():
    return __name__ == '__main__'
    
def PrintInTest(logContent):
    if IsInTest():
        #global g_logger
        print(logContent)
        #g_logger.info(logContent)

if __name__ == '__main__':
    main_On_flag = True
    def SendResponse(relativeReq, ansConetent, iLenth):
        PrintInTest('main Send Ans back:' + ansConetent)
    def IsNginxOn():
        return main_On_flag

    import random
    reqContext = str(random.randrange(0, 10001, 2))
    iLoopStep = 0
    reqtxt = '''
    {"REQUESTS":[{"REQ_COMM_DATA":{"SERVICE_ID":"600300","COMPANY_ID":"19900","SECU_CODE":"600004","MARKET":"1"},"REQ_MSG_HDR":{"SERVICE_ID":"600300"}}]}
    '''
    reqtxt = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600120","TIME_STAMP":"**********","SIGN":"A52F5A86B70C018502C366C5F86B5A8C"},"REQ_COMM_DATA":{"CURRENCY":"0","USER_ID":"jwl_test_mwb22","ACCOUNT":"************","COMPANY_ID":"15900","AdapterDebugInfo":"0","USER_ID_CLS":"Q","SESSION":"000000"}}]}

    '''
    req_600001 =  '''
 {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600001",
                "TIME_STAMP":"**********",
                "SIGN":"80D5A19FE849C9B0C99251C91ACB4A2D"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "LOGIN_CODE":"********",
                "LOGIN_TYPE":"2",
                "TCC":"MA;IIP=************;IPORT=49867;LIP=NA;MAC=NA;IMEI=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=Andriod;IMSI=NA;@ZXG;NA;1629023E0E98E8FC39A9689CF1A9E199.QQ",
                "TRD_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}
         '''
    reqtxt1 = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600170","TIME_STAMP":"**********","SIGN":"A52F5A86B70C018502C366C5F86B5A8C"},"REQ_COMM_DATA":{"CHANNEL":"","ORDER_ID":"","USER_PWD":"0000000000000000000000000000000000000000000000000000000000008209bcbff97c2d05de7d106ff9ce10e3cc90541c224d458d311a93894c389f84c5858601ef96dd56a50807980f15b254580ea2aa990ee960423ed35981f13d0453ede849e575ea25a9f50b0766224c435c70c00dd9f5051423240cc1073ee7821f211407af6a3c355c63585491616fc3978411500aa5a798f3f680d03db18868","SESSION":"000000","BEGIN_DATE":"20170327","COMPANY_ID":"15900","END_DATE":"20170411","USER_ID":"jwl_test_mwb22","KEY_STR":"","TRD_ID":"","REC_COUNT":"","MARKET":"","AdapterDebugInfo":"0","SECU_CODE":"","USER_ID_CLS":"Q"}}]}

    '''
    reqtxt600310 = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600310","TIME_STAMP":"**********","SIGN":"A52F5A86B70C018502C366C5F86B5A8C"},"REQ_COMM_DATA":{"CPTL_AMT":"11","EXT_SERIAL_NO":"0001","EXT_ACC_PWD":"","CHARACTER_TER":"","SESSION":"81d100beb94731169673e02cc5e22d3717db7417ad8baabb28c738df49f25f99a7a0314b7a028904101eaa9338fb31520192974e08ad160c04bee1fbb4d7c7ec3b43f83c4db758a8e7534c0b8ca07867e729a0b2b2958161f80abcefac980833c6b3fcf4a88d11defd7e8af8ea395db17daa1812037c2934516061fff03f911aed3c71c0d1dc19c9d495a38ced04a2a2f013d88117906d5a8beb184466865e1c61d151a888cc6a39b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99e2c6bf9d73848b88c16fc0f8a27563d8131993757c487f862657aafb48312032fdd155831e23371e5d509060578b3d68ddd3e62db30eaeb91031f056b38113702aa5d6dea70f8dea864e8458a3be583cb9225eaeeeb4563d3f39e1471953720a3b2236b0fe2f9ed085469d76943730960a99f6425b8c32173f70357f18cbf740f240c90eee3cda9e32b2a54e49983e22c6cfb4db44b94a79c959a0fa04c1aa3ed1d3dfcea8d469bd7dbdb7f4012af8bcc5469db698174424195adc68b512d1d7b6691e91a829c17cf3008c9297b02d3a0987af237baf17c0cdf943a63ee54a0e2b01ed53c04a2721ad59e5d09c1e855d2696db2f1c024ef81eb20e8cfdb2550d155765505f4b63867f65b26a9c919d353e4eca096060be2acc2e5fd85b0030f272760329ca29ee72312204afb98cd82f3f2585feaeec0b6afcac023c8cfaacc9","COMPANY_ID":"15900","USER_ID_CLS":"Q","AdapterDebugInfo":"0","REMARK":"","CURRENCY":"0","USER_ID":"jwl_test_mwb22","EXT_INST":"1007","ACC_PWD":"0000000000000000000000000000000000000000000000000000000000008209bcbff97c2d05de7d106ff9ce10e3cc90541c224d458d311a93894c389f84c5858601ef96dd56a50807980f15b254580ea2aa990ee960423ed35981f13d0453ede849e575ea25a9f50b0766224c435c70c00dd9f5051423240cc1073ee7821f211407af6a3c355c63585491616fc3978411500aa5a798f3f680d03db18868"}}]}
    '''
    reqtxt600200 = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600200"},"REQ_COMM_DATA":{"USER_ID":"jwl_test_mwb22","SESSION":"000000","SECU_CODE":"","USER_ID_CLS":"Q","AdapterDebugInfo":"0","COMPANY_ID":"15900","MARKET":""}}]}

    '''
    reqtxt600092 =  '''
     {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600092"},"REQ_COMM_DATA":{"EXT_INST":1001,"COMPANY_ID":11600,"BIZ_TYPE":"1000"}}]}

         '''
    reqtxt600090 =  '''
     {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600090"},"REQ_COMM_DATA":{"MSG_ID":1002,"COMPANY_ID":11600,"BIZ_TYPE":"1000","TEXT":"这是一个测试公告"}}]}

         '''
    reqtxt600091 =  '''
     {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600091"},"REQ_COMM_DATA":{"COMPANY_ID":15900,"TRD_DATE":"20210602112233"}}]}

         '''
    reqtxt600097 =  '''
     {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600097"},"REQ_COMM_DATA":{"COMPANY_ID":11600,"CUR_TIME":"20170501092233"}}]}

         '''
    reqtxt600002 = '''
     {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600002"},"REQ_COMM_DATA":{"USER_ID":"jwl_test_mwb22","USER_ID_CLS":"L","COMPANY_ID":"15900","SESSION":"764aaffd9778e1ad26e2fb440a7f39455f47344b3acb00349faa8e28b0b712e8fa35fed6c07f924df8d8d5de5304772419e8f41e8b4f174e5ef75bf1407978ba8deddffcec9d9d385cb2e74dcf9b30bab8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99b8f140b6e799ef99628b0a411651e5fc97d3e0c2de08cf4c36194a182e92e2db65e2a9c2d0f2704dda089f235aa1552923a2a659b7b3eb4548cc82fe92c56ccdfc44c07c8e91d1fe1d856855cd2a81c786830f0a97f388929968de8aaa10a71897914348aa10b0ece22157767beb45298ba16bbc56d7590dea683484a24c22358b03a013fd92280d02581780251c78357b01e642ecf6508b77f8eedc88933bc6c8ae450adaf490a0b80a4ecbf556aee1e622f725c3a520842041cc048750685c065755ddbc3f8d4699e3e71c989b3df1ae5ab592ded3cf0bc6da5dd2a5f234f23a59ee38cd554d469ee68afe12af9d1728c679846ce9129b8250f1fe38643663ae9427b81125a31204bee1fbb4d7c7ecd405a92c52e6b5484415f25ff58759ff0ba8a0b9088462fb6a4d6010f6d6bc4f65ded15827e442e691d82de34b480adbf145b077dae22840f627b50b3ff7a558ba0ae20b419a0a298617e96a6a57794a735f50d2956adb94f013d88117906d5a8beb184466865e1c535ec9431a3ff465523762e14b5a1273843a83a07c84053b84ebc1ffe6fe14fd036e0ac450883fdb00fb493e75a75dce"}}]}

    '''
    reqtxtJBTOrder = '''
    {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "600891"}, "REQ_COMM_DATA": {"USER_ID": "73"}}]}

    '''

    req_600010 = '''
{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600010"
            },
            "REQ_COMM_DATA":{
                "USER_ID_CLS":"1",
                "COMPANY_ID":"15900",
                "AdapterDebugInfo":"0",
                "USER_ID":"os-********",
                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                "USER_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                "LOGIN_CODE":"********",
                "LOGIN_TYPE":"1"
            }
        }
    ]
}'''

    req_600010a = '''
    {
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600010"
                },
                "REQ_COMM_DATA":{
                    "USER_ID_CLS":"1",
                    "COMPANY_ID":"15900",
                    "AdapterDebugInfo":"0",
                    "USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG",
                    "TCC":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG|***********|10.14.87.218",
                    "USER_PWD":"83a71a1dbbefed00000000000000000000000000000000000000000000002E117571DE5F3EF5F149A09DF1B9CF7E1B08BE1FF64E804B6829233F8F6FB79C9F16547C631D6E9596CD1F749BC7330F1833D6339433B8F204DB9258F6BE1D982F25519FD46200CB0EE9C5EBCDF58EF45DE5A823C22313338663658E4D7743167387313A72E7C8E5EA31A93A725833CDA86296882E9B33CC4EDE6C0682904F25",
                    "LOGIN_CODE":"********",
                    "LOGIN_TYPE":"1"
                }
            }
        ]
    }'''

    req_600140 = '''
     {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600140",
                "TIME_STAMP":"**********",
                "SIGN":"B3B8D9CBAFC1CCE7857F00040DCE747A"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "EXT_SERIAL_NO":"0307632019112709172953c37307",
                "MARKET":"1",
                "ORDER_PRICE":"14.07",
                "ORDER_QTY":"100",
                "SECU_ACC":"A551387402",
                "SECU_CODE":"110083",
                "SESSION":"ee9d38be1c6bb5291a936be3893d0f4ae5b113daf1253e55c94d3618c73d6a051d19a23ba6c3a09da261c90b2550b957e0ce9b671c2026c194eea1c060e0d0985a7bd99369c411941eadfa4ef282a378117836bb1fa98598aaae2112061484a5f47f919fc8645bcb5504d099c544b99533bef02a14c2b775",
                "TCC":"os-ppuOSi1wPDei9JbObGUlBUOXk|***********|10.40.162.39",
                "TRD_ID":"0B",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1",
                "SIGN_CONTRACT":""
            }
        }
    ]
}
'''
    req_600120 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600120"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1",
                "CURRENCY":"0",
                "SESSION":"432ca4cd3a76f11738e3d6f3e062500592f69f8d4f4efba539b63222a52c4a69eb0c7444ea9b07320a84ce173db49a50782cb532286ba3777d11fd4b8bf55f5321eb6d895c58b78cd3f5ac99b7f372b0682509931441f0a6f46c2409788443f191eeee0849513d7dd19e0bf6e424eb2deebaab65a0bcd7d6"
            }
        }
    ]
}'''
    req_600124 = '''
    {
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600124"
                },
                "REQ_COMM_DATA":{
                    "ACCOUNT":"********",
                    "COMPANY_ID":"15900",
                    "USER_ID":"os-********",
                    "CURRENCY":"0",
                    "SESSION":"6a0a523bc5910bb1e9089039d8291c727b8bc550740da101b4fb98519573a32ef472005fd7ee40c4c2cee2822c8394fdfcc4e752b9ca5034aa092f5cd195c382e0a5e03a7b258a3e6d62599aecb2a296ba17f5235b8c828539421abd9e8a6414af09628a3d7cb5845024670f338308828f500c993e2115f5"
                }
            }
        ]
    }'''
    req_600160 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600160"
            },
            "REQ_COMM_DATA":{
                "USER_ID_CLS" : "1",
                "USER_ID" : "os-********",
                "SESSION" : "8f9be03f0d9a592e67934db5a8fa06a9a9b77bc7ac9f87f8e50cb525c48a80d80facda7ef5b674a1dc6b8ba1853cc7d146ef8075ed7ba0eff5e1fd5ca4337057f6b35c8354fcd9684e2c9404ad85cea8f718b7ab1dd3f7be1c5bcfad5a8bae51962415aa35ddec7c0d6295a8b9dc890b44ad36fdea3c4e9a",
                "COMPANY_ID" : "15900",
                "ACCOUNT" : "********"
            }
        }
    ]
}
'''
    req_600161 = '''{
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600161"
                },
                "REQ_COMM_DATA":{
                    "USER_ID_CLS" : "1",
                    "USER_ID" : "os-********",
                    "SESSION" : "000000",
                    "COMPANY_ID" : "15900",
                    "ACCOUNT" : "********"
                }
            }
        ]
    }
    '''
    req_600200 = '''
            {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600200",
                "TIME_STAMP":"**********",
                "SIGN":"24101CAC85941840EF4878858474EC53"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "SESSION":"8f9be03f0d9a592e67934db5a8fa06a9a9b77bc7ac9f87f8e50cb525c48a80d80facda7ef5b674a1dc6b8ba1853cc7d146ef8075ed7ba0eff5e1fd5ca4337057f6b35c8354fcd9684e2c9404ad85cea8f718b7ab1dd3f7be1c5bcfad5a8bae51962415aa35ddec7c0d6295a8b9dc890b44ad36fdea3c4e9a",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1",
                "KEY_STR":"",
                "REC_COUNT":"20"
            }
        }
    ]
}
    '''

    req_600180 = '''{
    "REQUESTS": [
        {
            "REQ_MSG_HDR": {
                "SERVICE_ID": "600180",
                "TIME_STAMP": "**********",
                "SIGN": "B2BB3AB98C1D75EC9FBC40607E45A0CD"
            },
            "REQ_COMM_DATA": {
                "ACCOUNT": "********",
                "COMPANY_ID": "15900",
                "CURRENCY": "0",
                "USER_ID": "B06CE3C7A5879FD7976EF20E081199D5.QQ.ZXG",
                "USER_ID_CLS": "1",
                "ORDER_ID": "",
                "KEY_STR": "",
                "REC_COUNT": "5",
				"SESSION": "000000"
            }
        }
    ]
}'''
    req_600040 = '''{
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600040"
                },
                "REQ_COMM_DATA":{
                    "USER_ID_CLS" : "1",
                    "USER_ID" : "os-********",
                    "SESSION" : "000000",
                    "COMPANY_ID" : "15900",
                    "ACCOUNT" : "********"
                }
            }
        ]
    }
    '''
    req_600150 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600150"
            },
            "REQ_COMM_DATA":{
                "SESSION":"b4c052738a18b2ef82cb97bf281591d8226f037a0f7be20c5569baf68ecbc7d0a91ebd429c606fa92c6f9376760b4e5204dca0092cc5478a6441ec9f3de37f94ebe42b787bc5c4870f2fdc63db8502384dc54d359c32b912db239eb72d1448408341f8691624d0d4476f21c5f60cc7b4faa94054fcad021b5a1a827a0289b888",
                "MARKET":"1",
                "ORDER_ID":"1",
                "TRD_ID":"3B",
                "USER_ID_CLS":"1",
                "USER_ID":"os-********",
                "COMPANY_ID":"15900"
            }
        }
    ]
}'''
    req_600002 = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600002","TIME_STAMP":"**********","SIGN":"B86C3B70ADCB33B84BE3415E771C7E1C"},"REQ_COMM_DATA":{"ACCOUNT":"********","COMPANY_ID":"15900","LOGIN_CODE":"********","SESSION":"000000","USER_ID":"os-********","USER_ID_CLS":"1"}}]}'''

    req_600170 = '''{
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600170"
                },
                "REQ_COMM_DATA":{
                    "USER_ID_CLS" : "1",
                    "USER_ID" : "os-********",
                    "SESSION" : "000000",
                    "REC_COUNT" : "1",
                    "COMPANY_ID" : "15900",
                    "BEGIN_DATE":"********",
                    "END_DATE":"********",
                    "ACCOUNT" : "********"
                }
            }
        ]
    }
    '''
    req_600172 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600172"
            },
            "REQ_COMM_DATA":{
                "USER_ID_CLS" : "1",
                "USER_ID" : "os-********",
                "SESSION" : "000000",
                "REC_COUNT" : "100",
                "COMPANY_ID" : "15900",
                "BEGIN_DATE":"********",
                "END_DATE":"********",
                "ACCOUNT" : "********"
            }
        }
    ]
}
        '''
    req_600190 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600190"
            },
            "REQ_COMM_DATA":{
               "ACCOUNT" : "********",
               "BEGIN_DATE" : "********",
               "COMPANY_ID" : "15900",
               "END_DATE" : "********",
               "KEY_STR" : "",
               "REC_COUNT" : "10",
               "SESSION" : "000000",
               "USER_ID" : "os-********",
               "USER_ID_CLS" : "1"
            }
        }
    ]
}'''
    req_600210 = '''
{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600210",
                "TIME_STAMP":"**********",
                "SIGN":"CDEBA61417F249736BD0441116EDD7BB"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "ACC_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                "COMPANY_ID":"15900",
                "CPTL_AMT":"1400000.00",
                "CURRENCY":"0",
                "EXT_ACC_PWD":"83a71a1dbbefed00000000000000000000000000000000000000000000002E117571DE5F3EF5F149A09DF1B9CF7E1B08BE1FF64E804B6829233F8F6FB79C9F16547C631D6E9596CD1F749BC7330F1833D6339433B8F204DB9258F6BE1D982F25519FD46200CB0EE9C5EBCDF58EF45DE5A823C22313338663658E4D7743167387313A72E7C8E5EA31A93A725833CDA86296882E9B33CC4EDE6C0682904F25",
                "EXT_INST":"1001",
                "EXT_SERIAL_NO":"**************",
                "SESSION":"be8fc8c68484ebc6dede590d3404cb018e560df4fdebd5876de33859f3d0fedb74bb5c41fc3b0bd907e5b1ddbd1b47c4c9f3b3e476bb6dc437acaa5430c32145671c9e64ad911aa0ad381db5d3676dd9eab8928d9d0549789c91ee044f18324a87a1ed893d7cef40e98838b82d858ea51edc4d30b279f54b5a1a827a0289b888",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''

    req_600220 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600220",
                "TIME_STAMP":"**********",
                "SIGN":"4628FD323CA91A60037124B39F3D7878"
            },
            "REQ_COMM_DATA":{
                "ACC_PWD":"83a71a1dbbefed00000000000000000000000000000000000000000000002E117571DE5F3EF5F149A09DF1B9CF7E1B08BE1FF64E804B6829233F8F6FB79C9F16547C631D6E9596CD1F749BC7330F1833D6339433B8F204DB9258F6BE1D982F25519FD46200CB0EE9C5EBCDF58EF45DE5A823C22313338663658E4D7743167387313A72E7C8E5EA31A93A725833CDA86296882E9B33CC4EDE6C0682904F25",
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "CPTL_AMT":"1",
                "CURRENCY":"0",
                "EXT_INST":"1004",
                "EXT_SERIAL_NO":"********",
                "SESSION":"670f32f3729233934debc03228cf91d1aa3ca20db05af38ba52a82d3c880f8d8ebf5b87bf5d07544505ba427becfd75aa38f2242ded76c412fbb901532031ded74da5968119311786b1fa0d19a70d9faf62d08f0763fb7390df8e883aaa5b82ff7279b7de305f7b0e870d05f0d69158e5857ffed5085181567b49ca4712cade8a3883a4e3a2db2d1",
                "TCC":"MI;IIP=**************;IPORT=50361;LIP=***************;MAC=28A02B1C4E9C;IDFV=53479C00-849C-615C-9464-A971101F8370;RMPN=***********;UMPN=NA;OSV=iOS10.0.2;@ZXG;V0.0.0.1",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''
    req_600360 = '''{
            "REQUESTS":[
                {
                    "REQ_MSG_HDR":{
                        "SERVICE_ID":"600360"
                    },
                    "REQ_COMM_DATA":{
                        "USER_ID_CLS" : "1",
                        "USER_ID" : "os-********",
                        "SESSION" : "000000",
                        "COMPANY_ID" : "15900",
                        "ACCOUNT" : "********"
                    }
                }
            ]
        }
        '''

    req_600380 = '''{
                "REQUESTS":[
                    {
                        "REQ_MSG_HDR":{
                            "SERVICE_ID":"600380"
                        },
                        "REQ_COMM_DATA":{
                            "ACCOUNT":"********",
                            "COMPANY_ID":"15900",
                            "MARKET":"1",
                            "SESSION":"8f9be03f0d9a592e67934db5a8fa06a9a9b77bc7ac9f87f8e50cb525c48a80d80facda7ef5b674a1dc6b8ba1853cc7d146ef8075ed7ba0eff5e1fd5ca4337057f6b35c8354fcd9684e2c9404ad85cea8f718b7ab1dd3f7be1c5bcfad5a8bae51962415aa35ddec7c0d6295a8b9dc890b44ad36fdea3c4e9a",
                            "USER_ID":"os-********",
                            "USER_ID_CLS":"1"
                        }
                    }
                ]
            }
            '''

    req_600390 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600390",
                "TIME_STAMP":"**********",
                "SIGN":"F9148136311CF2AA7983F525E3BB82B8"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "BEGIN_DATE":"********",
                "COMPANY_ID":"15900",
                "END_DATE":"********",
                "REC_COUNT":"3",
                "SESSION":"000000",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''

    req_600320 = '''{
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600320",
                    "TIME_STAMP":"**********",
                    "SIGN":"E1E9E01473346906F86D45ED03AFCC42"
                },
                "REQ_COMM_DATA":{
                    "COMPANY_ID":"15900",
                    "TRD_DATE":"********",
                    "USER_ID_CLS":"1",
                    "ACCOUNT":"********",
                    "USER_ID":"os-********"
                }
            }
        ]
    }'''

    req_600330 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600330",
                "TIME_STAMP":"**********",
                "SIGN":"F9148136311CF2AA7983F525E3BB82B8"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "BEGIN_DATE":"********",
                "COMPANY_ID":"15900",
                "END_DATE":"********",
                "REC_COUNT":"50",
                "SESSION":"000000",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''

    req_600340 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600340",
                "TIME_STAMP":"**********",
                "SIGN":"F9148136311CF2AA7983F525E3BB82B8"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "BEGIN_DATE":"********",
                "COMPANY_ID":"15900",
                "END_DATE":"********",
                "REC_COUNT":"40",
                "SESSION":"000000",
                "USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''
    req_800011 = '''
    {
        "REQUESTS":
            [
                {
                    "REQ_MSG_HDR":
                        {
                            "SERVICE_ID":"800011",
                            "TIME_STAMP":"**********",
                            "SIGN":"C0F752F2C6F3AB777B82F7305F343681"
                        },
                    "REQ_COMM_DATA":
                        {
                            "BIZ_TYPE":"6",
                            "COMPANY_ID":"15900",
                            "ID":"","ID_TYPE":"00",
                            "M_TEL":"***********",
                            "SESSION":"",
                            "USER_ID":"os-********",
                            "USER_ID_CLS":"1"
                        }
                }
            ]
    }'''

    r_600001 = '''
            {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600001",
                "TIME_STAMP":"**********",
                "SIGN":"80D5A19FE849C9B0C99251C91ACB4A2D"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "LOGIN_CODE":"********",
                "LOGIN_TYPE":"2",
                "TCC":"MA;IIP=************;IPORT=49867;LIP=NA;MAC=NA;IMEI=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=Andriod;IMSI=NA;@ZXG;NA;1629023E0E98E8FC39A9689CF1A9E199.QQ",
                "TRD_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}
        '''

    r_600010 = '''
                {
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600010",
                    "TIME_STAMP":"**********",
                    "SIGN":"80D5A19FE849C9B0C99251C91ACB4A2D"
                },
                "REQ_COMM_DATA":{
                        "USER_ID_CLS":"1",
                        "COMPANY_ID":"15900",
                        "AdapterDebugInfo":"0",
                        "USER_ID":"os-********",
                        "LOGIN_CODE":"********",
                        "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                        "USER_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                        "LOGIN_TYPE":"1"
                }
            }
        ]
    }
            '''

    req_600030 = '''{
                "REQUESTS":[
                    {
                        "REQ_MSG_HDR":{
                            "SERVICE_ID":"600030"
                        },
                        "REQ_COMM_DATA":{
                            "USER_ID_CLS" : "1",
                            "USER_ID" : "os-********",
                            "SESSION" : "000000",
                            "COMPANY_ID" : "15900",
                            "ACCOUNT" : "********"
                        }
                    }
                ]
            }
            '''
    req_600121 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600121"
            },
            "REQ_COMM_DATA":{
                "USER_ID_CLS" : "1",
                "USER_ID" : "os-********",
                "SESSION" : "8f9be03f0d9a592e67934db5a8fa06a9a9b77bc7ac9f87f8e50cb525c48a80d80facda7ef5b674a1dc6b8ba1853cc7d146ef8075ed7ba0eff5e1fd5ca4337057f6b35c8354fcd9684e2c9404ad85cea8f718b7ab1dd3f7be1c5bcfad5a8bae51962415aa35ddec7c0d6295a8b9dc890b44ad36fdea3c4e9a",
                "SECU_CODE" : "600446",
                "MARKET" : "1",
                "TRD_ID" : "0B",
                "COMPANY_ID" : "15900",
                "ORDER_PRICE" : "20.15",
                "ACCOUNT" : "********"
            }
        }
    ]
}
                '''
    req_600270 = '''{
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600270",
                    "TIME_STAMP":"**********",
                    "SIGN":"4628FD323CA91A60037124B39F3D7878"
                },
                "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "CHANNEL":"",
                "COMPANY_ID":"15900",
                "CURRENCY":"0",
                "KEY_STR":"",
                "REC_COUNT":"0",
                "SESSION":"000000",
                "USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG",
                "USER_ID_CLS":"1"
            }
            }
        ]
    }'''
    req_600011 = '''
    {
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600011"
                },
                "REQ_COMM_DATA":{
                    "USER_ID_CLS":"1",
                    "COMPANY_ID":"15900",
                    "AdapterDebugInfo":"0",
                    "USER_ID":"os-********",
                    "USER_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                    "LOGIN_CODE":"********",
                    "LOGIN_TYPE":"1",
                    "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY"
                }
            }
        ]
    }'''
    req_600041 = '''{
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"600041"
                },
                "REQ_COMM_DATA":{
                   "ACCOUNT" : "********",
                   "COMPANY_ID" : "15900",
                   "USER_ID" : "os-********",
                   "SESSION":"8f9be03f0d9a592e67934db5a8fa06a9a9b77bc7ac9f87f8e50cb525c48a80d80facda7ef5b674a1dc6b8ba1853cc7d146ef8075ed7ba0eff5e1fd5ca4337057f6b35c8354fcd9684e2c9404ad85cea8f718b7ab1dd3f7be1c5bcfad5a8bae51962415aa35ddec7c0d6295a8b9dc890b44ad36fdea3c4e9a",
                   "USER_ID_CLS" : "1"
                }
            }
        ]
    }'''
    req_600042 = '''{
            "REQUESTS":[
                {
                    "REQ_MSG_HDR":{
                        "SERVICE_ID":"600042"
                    },
                    "REQ_COMM_DATA":{
                       "ACCOUNT" : "********",
                       "COMPANY_ID" : "15900",
                       "USER_ID" : "os-********",
                       "SESSION":"000000",
                       "MARKET" : "1",
                       "USER_ID_CLS" : "1"
                    }
                }
            ]
        }'''
    req_600042_new = '''{
                "REQUESTS":[
                    {
                        "REQ_MSG_HDR":{
                            "SERVICE_ID":"600042"
                        },
                        "REQ_COMM_DATA":{
                           "ACCOUNT" : "********",
                           "COMPANY_ID" : "15900",
                           "USER_ID" : "os-ppuEMmKirRt5jPgwznDWD-MHE.ZXG",
                           "SESSION":"000000",
                           "MARKET" : "",
                           "USER_ID_CLS" : "1"
                        }
                    }
                ]
            }'''
    req_600061 = '''{
                "REQUESTS":[
                    {
                        "REQ_MSG_HDR":{
                            "SERVICE_ID":"600061"
                        },
                        "REQ_COMM_DATA":{
                           "ACCOUNT" : "********",
                           "COMPANY_ID" : "15900",
                           "USER_ID" : "os-********",
                           "SESSION":"000000",
                           "USER_ID_CLS" : "1"
                        }
                    }
                ]
            }'''
    req_600391 = '''{
                    "REQUESTS":[
                        {
                            "REQ_MSG_HDR":{
                                "SERVICE_ID":"600391"
                            },
                            "REQ_COMM_DATA":{
                               "ACCOUNT" : "********",
                               "COMPANY_ID" : "15900",
                               "REC_COUNT":"2",
                               "KEY_STR":"",
                               "USER_ID" : "os-********",
                               "SESSION":"000000",
                               "USER_ID_CLS" : "1"
                            }
                        }
                    ]
                }'''

    req_600060 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600060",
                "TIME_STAMP":"**********",
                "SIGN":"DDF15D29A77B292DF12A039953416B07"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "MARKET":"",
                "SESSION":"fd01bda1203e482dabd74b49e25597bafa8daf7cd765b5f826239a47d987422f010c707f369733d99f0113001d8eae898cfe58690fb10a10b41857c6fe9143ec68194d2a7a9c60e1caadac8e53d6ae4e6c087a6ca5059b1ba93ae2249b001a70ba8ba1f8469df380bf7683f5d6f4762acf586b87feafd00ab7c1462d66dd897b9768f814cf76d55079698363c987921705d37142165b5977",
                "USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''

    req_800012 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"800012",
                "TIME_STAMP":"**********",
                "SIGN":"421E2F19B9011E7D34CC10A67720BF83"
            },
            "REQ_COMM_DATA":{
                "BIZ_TYPE":"6",
                "COMPANY_ID":"15900",
                "ID":"",
                "ID_TYPE":"00",
                "M_TEL":"***********",
                "SESSION":"",
                "SMS_CODE":"600109",
                "USER_ID":"os-ppuPUsmO-xgbI0HeN2reOXNGc",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''
    req_600090 = '''
        {
    "REQUESTS":[
        {
            "REQ_COMM_DATA":{
                "MSG_ID":"1",
                "TYPE":"Q",
                "TYPE_VALUE":"全部交易业务",
                "BGN_DATE":"2022-12-23 10:55:55",
                "END_DATE":"2022-12-23 19:55:55",
                "USER_ID_CLS":"1",
                "TEXT":"鬼斧神工他",
                "COMPANY_ID":"15900"
            },
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600090"
            }
        }
    ]
}
    '''
    req_600091 = '''
    {"REQUESTS": [{"REQ_MSG_HDR": {"SERVICE_ID": "600091"}, "REQ_COMM_DATA": {"COMPANY_ID": "15900", "TRD_DATE": "2021-06-03 00:00:00", "USER_ID_CLS": "1"}}]}
    '''

    req_600390a = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600390","TIME_STAMP":"**********","SIGN":"B363991805CE0D939432DCAFE900F8A3"},"REQ_COMM_DATA":{"ACCOUNT":"********","BEGIN_DATE":"********","COMPANY_ID":"15900","END_DATE":"********","REC_COUNT":"40","SESSION":"000000","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}'''

    req_600400 = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600400","TIME_STAMP":"**********","SIGN":"8B1805BDA2FC0512A1D3EC7F689ED578"},"REQ_COMM_DATA":{"COMPANY_ID":"15900","TRD_DATE":"********","USER_ID":"os-ppuKcEAnlM8ZaehfxUsInAwds"}}]}'''

    req_600280a = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600180","TIME_STAMP":"**********","SIGN":"03A55F7B7B240D775BDD9CAE888349DA"},"REQ_COMM_DATA":{"ACCOUNT":"********","COMPANY_ID":"15900","KEY_STR":"","REC_COUNT":"40","SESSION":"0315d8a8d4acebb231851a8bece8eddd051d13f579310d9b71f870267d83ce6203813c0e85da233743818d1ce2db6e226a3c5f79b5fc65f32c338c0f90d84deee7f1225f01ba72e94d388883f52b1b44040de2c9e92bab631d0911d3b836cf92cd955c9948825d57fa6fca7e43afb14fe227cd99094caa6e2e35935606d221c4f04fa3b55e036c9810a26aaaecc93f6ea1b6e5c370a0dff111091bac4e1e41a1","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}'''

    req_600140a = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600140","TIME_STAMP":"**********","SIGN":"********************************"},"REQ_COMM_DATA":{"ACCOUNT":"********","COMPANY_ID":"15900","EXT_SERIAL_NO":"0304c5201912121640303db158a3","MARKET":"1","ORDER_PRICE":"3.61","ORDER_QTY":"80","SECU_ACC":"A224436597","SECU_CODE":"601288","SESSION":"e2cf1017a83effd630a38a8c15331fd1bcff0c050e364690447f6a35e7fc7e5994a4cce317f2777c2940c34d36ddcfaaf32f9942bcea57db733b8024f29f3f367e6fcaa57ce532d13a41e2c052ea64e44b10047b318f2ced4885d74c0d43d01f38ff0497b957502205de4f4603a03a7a0454c74fad84836744493d48d645ac741fce62230f4bb9eecd710c19f6e20c97fcfd9c08a8909000","TCC":"os-ppuGfYWS7n2yR5GtbdBeuRQCM|***********|10.41.58.40","TRD_ID":"0S","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1","SIGN_CONTRACT":""}}]}'''

    req_600160a = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600160","TIME_STAMP":"**********","SIGN":"********************************"},"REQ_COMM_DATA":{"ACCOUNT":"********","COMPANY_ID":"15900","KEY_STR":"","REC_COUNT":"20","SESSION":"4ffe51a46675acdc1bd4e87fdb69362979d4b4b17d464a64d326763908bbdf18b5217604791da779ab424a456ee17736e482e3c063f30f5820251634e4ac95cf07d93bdbdc29af633df1e6f6e1ca01b921901a42de0440dcb9e30c755f041655c5fa6fce50dd6cf95b0315572c97e9e4f620912fa0c3ae3e2a36afa908732e53815e389b5e4f698ddc41d8e9cd34f58e63f29c250f6e0f3f5b15a76c4ee7df6a","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}
    '''

    req_600180a = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600180","TIME_STAMP":"**********","SIGN":"81FE435AFCB79F7ABCC84B59EC4CB831"},"REQ_COMM_DATA":{"ACCOUNT":"********","COMPANY_ID":"15900","KEY_STR":"","REC_COUNT":"40","SESSION":"77d3e9de5c7e80c91bd4e87fdb693629333e75d335e6f5c86c82c81f2e855cfc15c8cc277ed23cf43572197126d6c84483be5ba194760672941377d0fed30a838f9b1f61031076bcc22b1d2e1546e15791a41c19ee9009416da1b2605a722a8d3dd118862b495c7088fff5151ccb5c7298208ba4261919c642352d4351d7328f135f771e06afcfcc1b358425a613eb89b8c7be896a6a2cf2f5b6973a0b24aa9c","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}
    '''
    req_600401 = '''
        {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600401",
                "TIME_STAMP":"**********",
                "SIGN":"8B1805BDA2FC0512A1D3EC7F689ED578"
            },
            "REQ_COMM_DATA":{
                "COMPANY_ID":"15900",
                "ACCOUNT":"********",
                "USER_ID":"os-********",
                "SESSION":"a2100c79e0ad5be2e1aa171ada91e26be7555a7aa7bfb50e5a1831c93bca9be63766540a38d86af17569d139d5764738bffe25307abda11e0d11eb5d6017b4f01e1647a04f8a5fc76d59f74bac2234efaec0efb52e331caabcbd35861bf603ffa717e62495d939c23dc3fab4ea56fdd160843af2ea795ef8"
            }
        }
    ]
}
        '''

    req_600043 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600043",
                "TIME_STAMP":"**********",
                "SIGN":"7409986D6A4726F02A55D4A7BE1CF043"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "RIST_ASSESSMENT":"1-80-1:D:2,2:D:15,3:D:10,4:A:15,5:D:8,6:D:15,7:C:15,8:B:2,9:E:15,10:A:10,11:D:15,12:B:8,13:D:15,14:B:15,15:D:15,16:D:15,17:C:15",
                "SESSION":"59dd4fd5971e90ebe7e7b167946e859dd2b3d7eb9a794f88d877d7e342dcc1000b80aaabc2afc53ff8cbc072ae575d591c1d98a2a569e44dae35c322fdd51971bad3fb2046a570b2cf0bb95c0437e5a77046acf71656da4625e0228900107bbfa94f1eac8446f5b795d063a5144a5ccde8424a8b480c6ef50fede5062812a5c7efdfa04210af8c57",
                "TRD_PWD":"b0a8da0fa84754000000000000000000000000000000000000000000000079FB958B3B0B3F8BE18E9DBB7793436D6582A08E5E1CD83470096BBB12A80F848B8A6D296D0EB67BC4ED1E90BF2475E5FE6073FA3D48AFAA8C1ABE90876D1C8BBB71303537E83E2137EA7F96B947DA68B4EBB51C304AED83C7C9BDCDE0631AC061168BA84F53378F590412368FD360A76367B276CC9E6F279DA7F01C11851AE3",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''

    r600210 = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600210",
                "TIME_STAMP":"**********",
                "SIGN":"217355FDD9054468A8B4EFA3F91CCB33"
            },
            "REQ_COMM_DATA":{
                "ACC_PWD":"50d91a8155a3a20000000000000000000000000000000000000000000000D8B246A416E2613024744BD0C64A5906CBE815196F03CEC01EF0F4DA9D657E960D748B980C889123AD73266FE814B41E547C58BB94D15686085FECACA41BB2B4D39676DB3CAA7BC1DC5D27F60EF3696FA6B6021F3B4A397D71363EC78F5CD9CACA7B6EE09C5C3D55D957CFAF271951D9A108F5ADE7D7A046E9932935B8513D9B",
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "CPTL_AMT":"10.00",
                "CURRENCY":"0",
                "EXT_INST":"1004",
                "EXT_SERIAL_NO":"011106201912201435113db15894",
                "SESSION":"7ba844946f2548d877f3470123c7774e594a24eab7eb09e3fb3a8ef614ecf11cbc3cfec38be0d09c8f18950d97c06b84d7bd5f3a1b5c53e66c4068a625029e8ac74e6a4e304ff94d16b6961e6145bb5bbda8d63ef16ddbc16299ae2871c9800e67c0b2491b4dacbe103ae963d579b61edc53d3f68822aca25a6f66bee142c86dc7afed9f159be20d9815de009e8ceb7b0fa86dcbed7a9842",
                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.40.162.39",
                "USER_ID":"os-ppuO0fWxMYjSMWxiyFH14HwvY.ZXG",
                "USER_ID_CLS":"1"
            }
        }
    ]
}'''
    req_600320_new = '''{
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600320",
                "TIME_STAMP":"**********",
                "SIGN":"87BE509D09A23B98229A00A8EE3AF18D"
            },
                "REQ_COMM_DATA":{
                "COMPANY_ID":"12900",
                "TRD_DATE":"********",
                "USER_ID":"os-********"
            }
        }
    ]
}'''
    r_600060 = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600060","TIME_STAMP":"**********","SIGN":"C0EE0EBDCC6FC9B98895E57E50CFFBA7"},"REQ_COMM_DATA":{"ACCOUNT":"********","COMPANY_ID":"15900","SESSION":"2eb1265015a339b9abd74b49e25597bac3378251e7a8813326239a47d987422f010c707f369733d9924b72b813ba99ef49d123783c10443085550862f88700b3e50dd6b75944bff7125d9731ccd71230b370ac07bdcd3e22931959146a503b4712e56471d4319a9e55cc86b54235ea38755ef4e91265a44c429cf36fc77d83d6aaf5b8c960d7de49c7264f168531be29ba3a20dd6e4e37a50e3f0e3d9f03640c","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}
    '''
    req_600390 = '''{"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600390","TIME_STAMP":"**********","SIGN":"DFB52D30FAAFAF4F51D1415DD48CB710"},"REQ_COMM_DATA":{"ACCOUNT":"********","BEGIN_DATE":"********","COMPANY_ID":"15900","END_DATE":"********","KEY_STR":"0","REC_COUNT":"100","SESSION":"000000","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}'''
    r_600140 = '''
        {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600140",
                "TIME_STAMP":"**********",
                "SIGN":"********************************"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "EXT_SERIAL_NO":"0304c5201912251039183db158e1",
                "MARKET":"1",
                "ORDER_PRICE":"12.14",
                "ORDER_QTY":"200",
                "SECU_ACC":"A224436597",
                "SECU_CODE":"600000",
                "SESSION":"8f9be03f0d9a592e67934db5a8fa06a9a9b77bc7ac9f87f8e50cb525c48a80d80facda7ef5b674a1dc6b8ba1853cc7d146ef8075ed7ba0eff5e1fd5ca4337057f6b35c8354fcd9684e2c9404ad85cea8f718b7ab1dd3f7be1c5bcfad5a8bae51962415aa35ddec7c0d6295a8b9dc890b44ad36fdea3c4e9a",
                "TCC":"os-ppuOSi1wPDei9JbObGUlBUOXk|***********|10.40.162.39",
                "TRD_ID":"0B",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1",
                "SIGN_CONTRACT":""
            }
        }
    ]
}
    '''

    rr600170 = '''
    {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"600170","TIME_STAMP":"**********","SIGN":"79FAD6AB1086813DA191BD3F754CE6A2"},"REQ_COMM_DATA":{"ACCOUNT":"********","BEGIN_DATE":"********","COMPANY_ID":"15900","END_DATE":"********","KEY_STR":"","REC_COUNT":"1000","SESSION":"000000","USER_ID":"os-ppuGfYWS7n2yR5GtbdBeuRQCM.ZXG","USER_ID_CLS":"1"}}]}
    '''

    req_600047 = '''
    {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600047",
                "TIME_STAMP":"**********",
                "SIGN":"8BC892B97C0838CD0F4081A06E30E929"
            },
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "EDUCATION":"5",
                "OCCU_TYPE":"237",
                "SESSION":"6a19d8b52a518b6cda35d0841ff7542a07dc1e573a769a1ab1d0d4554c687ee3b6db7787aaff13e8353aad001e8fd2885ae3e43e491c5c6ff04eb60457f7a17b8831a6eeca72f99711f17957fd9d6b7971bd5e4920b7b4728ffe561a1b0eb389bbefd43a2d9e706b31a8fdc44502aba516dcab5487eaca55",
                "USER_ID":"os-********",
                "USER_ID_CLS":"1"
            }
        }
    ]
}
    '''


    req_700001 = '''
            {
        "REQUESTS":[
            {
                "REQ_MSG_HDR":{
                    "SERVICE_ID":"700001",
                    "TIME_STAMP":"**********",
                    "SIGN":"27834a5c4855d4c3b889fb9868e95699"
                },
                "REQ_COMM_DATA":{
                    "session_id":"c10442ebb05ab2f9e42dbd629572b020d994dcb6ee89ce43125e67b45e83a6c180007ebaaf37e94e59cb2c1e99d305dbaeec8af40612d70a23818e20b3439b4bfc7f12fcc50b652cdd3f4c5a5f0ac6d9303dc9516a30380c7066e82144a01bfceb8417aadb80c3c8ab71792ffa460bc3220ccfa771d207cb465000421b67426f7e388f42a81698e132636c58ccb79e5fe87e9d5b9636494f"
                }
            }
        ]
    }
            '''
    req_700001_a = '''
    {
    "REQUESTS": [{
            "REQ_MSG_HDR": {
                "SERVICE_ID": "700001",
                "TIME_STAMP": 1603438450,
                "SIGN": "a5efdc1ce717e1842ce659e222d09054"
            },
            "REQ_COMM_DATA": {
                "session_id": "278867009a75b3cb64fd1949ab71da25d6ff7c6dd07a5765bde2d105e51447826f8ae7bb5bd0ab22edfa477bc8ea0f6e1d95df755c1bb394cb33f5be99b60adc1bd2018287a17d519574cbb1ed837136c873333772fa4a9d25b3c550a6bea679b6d2841c7ee670f1f7c0e76f1f1455a9888f3e437887d31c46d0ffc59d086468721703d48f0831a803268dcf6e948b231ad9cb27503f35a6f1ce38110466cf71"
            }
        }
    ]
}

                '''
    treq = '''
        {"REQUESTS":[{"REQ_MSG_HDR":{"SERVICE_ID":"800011","TIME_STAMP":"**********","SIGN":"C0F752F2C6F3AB777B82F7305F343681"},"REQ_COMM_DATA":{"BIZ_TYPE":"6","COMPANY_ID":"15900","ID":"","ID_TYPE":"00","M_TEL":"***********","SESSION":"","USER_ID":"os-********","USER_ID_CLS":"1"}}]}
    '''

    req_600003 = '''
         {
        "REQUESTS": [
            {
                "REQ_MSG_HDR": {
                    "SERVICE_ID": "600003"
                },
                "REQ_COMM_DATA": {
                    "ACCOUNT": "********",
                    "COMPANY_ID": "15900",
                    "USER_ID_CLS": "1",
                    "USER_ID": "os-********",
                    "TCC":"1753A3D1B5511E1BC00003D203D4157E.QQ|***********|10.43.31.56",
                    "SESSION":"b05a4097491095275ac41fbbe4576aa03feaac03749c8606db3dad155378bb284e05815e6cd2af22f51b846b1e93441b3ad003535084876b722d1c19f80427b9f572574cf08d0076f0d84bd0f399d313d96adee21f16da4dce52f9203c044188dd6989dde127410f82951a7d3aa235fbc37ad8b55a5c9bec9fdc131fa5c5706b51c5bd3c70411d680c944fb5d15c6b75c526df6c681ada12"
                }
            }
        ]
    }'''

    req_600064 = '''
             {
            "REQUESTS": [
                {
                    "REQ_MSG_HDR": {
                        "SERVICE_ID": "600064"
                    },
                    "REQ_COMM_DATA": {
                        "ACCOUNT": "********",
                        "COMPANY_ID": "15900",
                        "USER_ID":"os-********",
                        "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                        "SESSION":"bd39d07d7d91a756c305936ba6e3c1b2d9b6e151ed5788749167a7f0d7076eda3cdf02a2fdb8fea32af3adb2564a7d87bbabe21e6831a8f68cc0851333c1a3948801439eab28f8f8f7f76ed1d2e3d110003cc5df3f59db12a6096ae1d4ca0675867b1c4e5594b4a17a2dd536a9d6ac5c00dbf153068898ca",
                        "ACCOUNT":"********",
                        "USER_ID_CLS": "1",
                        "MARKET": "1",
                        "SECU_CODE": "600401",
                        "SIGN_TYPE": "5",
                        "SIGN_VER": "********"
                    }
                }
            ]
        }'''

    req_600055 = '''
             {
            "REQUESTS": [
                {
                    "REQ_MSG_HDR": {
                        "SERVICE_ID": "600055"
                    },
                    "REQ_COMM_DATA": {
                        "ACCOUNT": "********",
                        "COMPANY_ID": "15900",
                        "USER_ID": "os-********",
                        "MARKET": "1",
                        "SECU_CODE": "688001",
                        "SIGN_TYPE": "3",
                        "SESSION":"b05a4097491095275ac41fbbe4576aa03feaac03749c8606db3dad155378bb284e05815e6cd2af22f51b846b1e93441b3ad003535084876b722d1c19f80427b9f572574cf08d0076f0d84bd0f399d313d96adee21f16da4dce52f9203c044188dd6989dde127410f82951a7d3aa235fbc37ad8b55a5c9bec9fdc131fa5c5706b51c5bd3c70411d680c944fb5d15c6b75c526df6c681ada12"
                    }
                }
            ]
        }'''

    req_400001 = '''
         {
            "REQUESTS": [
                {
                    "REQ_MSG_HDR": {
                        "SERVICE_ID": "400001"
                    },
                    "REQ_COMM_DATA": {
                        "USER_ID": "os-********"
                    }
                }
            ]
        }'''

    req_400002 = '''
         {
            "REQUESTS": [
                {
                    "REQ_MSG_HDR": {
                        "SERVICE_ID": "400002"
                    },
                    "REQ_COMM_DATA": {
                        "ACCOUNT": "********"
                    }
                }
            ]
        }'''

    req_400020 = '''
         {
            "REQUESTS": [
                {
                    "REQ_MSG_HDR": {
                        "SERVICE_ID": "400020"
                    },
                    "REQ_COMM_DATA": {
                        "TEL_NO": "***********"
                    }
                }
            ]
        }'''

    req_xinkelicai_600001 = '''
            {
                "REQUESTS":[
                    {
                        "REQ_MSG_HDR":
                        {
                            "SERVICE_ID":"600001",
                            "TIME_STAMP":"**********",
                            "SIGN":"44E45D1FF432E3165F6F8BCCDAA7D425"
                        },
                        "REQ_COMM_DATA":
                        {
                            "ACCOUNT":"********",
                            "COMPANY_ID":"15900",
                            "LOGIN_CODE":"********",
                            "LOGIN_TYPE":"2",
                            "TCC":"MA;IIP=************;IPORT=49867;LIP=NA;MAC=NA;IMEI=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=Andriod;IMSI=NA;@ZXG;NA;1629023E0E98E8FC39A9689CF1A9E199.QQ",
                            "TRD_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                            "USER_ID":"os-********",
                            "USER_ID_CLS":"1"
                        }
                    }
                    ]
            }
                     '''

    req_********_600001 = '''
                    {
                        "REQUESTS":[
                            {
                                "REQ_MSG_HDR":
                                {
                                    "SERVICE_ID":"600001",
                                    "TIME_STAMP":"**********",
                                    "SIGN":"44E45D1FF432E3165F6F8BCCDAA7D425"
                                },
                                "REQ_COMM_DATA":
                                {
                                    "ACCOUNT":"********",
                                    "COMPANY_ID":"15900",
                                    "LOGIN_CODE":"********",
                                    "LOGIN_TYPE":"2",
                                    "TCC":"MA;IIP=************;IPORT=49867;LIP=NA;MAC=NA;IMEI=NA;RMPN=NA;UMPN=NA;ICCID=NA;OSV=Andriod;IMSI=NA;@ZXG;NA;1629023E0E98E8FC39A9689CF1A9E199.QQ",
                                    "TRD_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                                    "USER_ID":"os-********",
                                    "USER_ID_CLS":"1"
                                }
                            }
                            ]
                    }
                             '''

    req_********_600010 = '''
        {
            "REQUESTS":[
                {
                    "REQ_MSG_HDR":{
                        "SERVICE_ID":"600010"
                    },
                    "REQ_COMM_DATA":{
                        "USER_ID_CLS":"1",
                        "COMPANY_ID":"15900",
                        "AdapterDebugInfo":"0",
                        "USER_ID":"os-********",
                        "LOGIN_CODE":"********",
                        "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                        "USER_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                        "LOGIN_TYPE":"1"
                    }
                }
            ]
        }'''

    req_xinkelicai_600010 = '''
        {
            "REQUESTS":[
                {
                    "REQ_MSG_HDR":{
                        "SERVICE_ID":"600010"
                    },
                    "REQ_COMM_DATA":{
                        "USER_ID_CLS":"1",
                        "COMPANY_ID":"15900",
                        "AdapterDebugInfo":"0",
                        "USER_ID":"os-********",
                        "LOGIN_CODE":"********",
                        "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                        "USER_PWD":"60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                        "LOGIN_TYPE":"1"
                    }
                }
            ]
        }'''

    # 查询TA账户
    req_800060 = '''
             {
                "REQUESTS": [
                    {
                        "REQ_MSG_HDR": {
                            "SERVICE_ID": "800060"
                        },
                        "REQ_COMM_DATA": {
                            "USER_ID_CLS":"1",
                            "COMPANY_ID":"15900",
                            "USER_ID":"********************************.QQ.ZXG",
                            "ACCOUNT":"********",
                            "SESSION":"cb5e87da40aeec78791c312a3628b2a07532df8622c945bc0722095d7c4c4d577203aff51db7ff8a5ac6f7a87db2360ba92abdc86999877c0de93a96a625c740d39958d2ffb14ec6d358fea129e700afed0f7b784d40bff68967c4e97fbf9ff926b8f55d1fe43b2fc7b1d2d5b2e6815d49ff1b218bae2140053706b44d8a0135e3f4e9fcdb08a5c14ebadbea6a86c95642fa672976f88ed5",
                            "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                            "TA_SN":"0"
                            
                        }
                    }
                ]
            }'''

    # 开通TA账户
    req_800049 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "800049"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"os-********",
                                "ACCOUNT":"********",
                                "SESSION":"6a19d8b52a518b6c9cac9bc756b76ae5e8b172c27e5b3339c0a8aad5eb28639f2e1195285f8fb9db7cd448a1a06695cdbd0a255b5af53f72c8b7ebe642ada6440dd4b8523af9338ecdb937dd6c6847ab60af9cba258a4a81b4ec31d6e9a64803d29bd69e0e63c5a7335f52275dff03df5811fd4e3fd3f89a",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "TA_SN":"0"

                            }
                        }
                    ]
                }'''

    # 理财产品适当性匹配查询
    req_800063 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "800063"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"os-********",
                                "ACCOUNT":"********",
                                "SESSION":"8c8f45c00f3c8108ed6a3ea6f0582ef22338c9204a123b9d5030fa0e0894efdeec9d6fcd2987a56c586e8352d33e7df765451c0f2959cc72998cd0acb3fa6f35ef53bf606a211522a140516be03bc2349b1974786fc98a655248cf62a91dc7698d4ab5ea2ab2f83b4870e66ca27c43596b4784fc380227bd5a1a827a0289b888",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "TA_SN":"0",
                                "BALANCE_CODE":"SRB893"

                            }
                        }
                    ]
                }'''

    # 签订适当性确认书留痕
    req_800064 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "800064"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"********************************.QQ.ZXG",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "SESSION":"8c8f45c00f3c8108ed6a3ea6f0582ef22338c9204a123b9d5030fa0e0894efdeec9d6fcd2987a56c586e8352d33e7df765451c0f2959cc72998cd0acb3fa6f35ef53bf606a211522a140516be03bc2349b1974786fc98a655248cf62a91dc7698d4ab5ea2ab2f83b4870e66ca27c43596b4784fc380227bd5a1a827a0289b888",
                                "ACCOUNT":"********",
                                "TA_SN":"0",
                                "INSTR_BATCH_NO":"0",
                                "OPER_INFO":"您的风险承受能力等级..."
                            }
                        }
                    ]
                }'''

    # 新客理财产品查询接口
    req_800410 = '''
                                     {
    "REQUESTS":[
        {
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "BALANCE_CODE":"ZQRG01",
                "COMPANY_ID":"15900",
                "TA_SN":"0",
                "USER_ID":"********************************.QQ.ZXG",
                "USER_ID_CLS":"1"
            },
            "REQ_MSG_HDR":{
                "SERVICE_ID":"800410",
                "SIGN":"518F1D18B1191E9934AAB6B396712F4D",
                "TIME_STAMP":"**********"
            }
        }
    ]
}
'''

    # 查询理财持仓
    req_600171 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "600171"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"os-********",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "SESSION":"8c8f45c00f3c8108ed6a3ea6f0582ef22338c9204a123b9d5030fa0e0894efdeec9d6fcd2987a56c586e8352d33e7df765451c0f2959cc72998cd0acb3fa6f35ef53bf606a211522a140516be03bc2349b1974786fc98a655248cf62a91dc7698d4ab5ea2ab2f83b4870e66ca27c43596b4784fc380227bd5a1a827a0289b888",
                                "ACCOUNT":"********",
                                "TA_SN":"0"

                            }
                        }
                    ]
                }'''

    # 理财产品相关的流水信息
    req_600390 = '''
                     {
                        "REQUESTS": [
                            {
                                "REQ_MSG_HDR": {
                                    "SERVICE_ID": "600390"
                                },
                                "REQ_COMM_DATA": {
                                    "USER_ID_CLS":"1",
                                    "COMPANY_ID":"15900",
                                    "USER_ID":"os-********",
                                    "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                    "SESSION":"329df23a8a9f30a58bb5ab4dc1c59454e2614401dd049ad5e24e69afe091b802a0cc436a2359372672b3b347c8181c0fa10f82d9af8c025914839f9d441355007269028a8f62ed35408404db27388eb8da35635fc90fbe69254141b17cefaa43522c71edc37d45f2a2fb2c678c86fb5399fa066794a2e76b697e65e23ea25125",
                                    "ACCOUNT":"********",
                                    "BEGIN_DATE":"********",
                                    "END_DATE":"********"

                                }
                            }
                        ]
                    }'''

    # 新增理财委托接口
    req_800140 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "800140"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"********************************.QQ.ZXG",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "SESSION":"cb5e87da40aeec78791c312a3628b2a07532df8622c945bc0722095d7c4c4d577203aff51db7ff8a5ac6f7a87db2360ba92abdc86999877c0de93a96a625c740d39958d2ffb14ec6d358fea129e700afed0f7b784d40bff68967c4e97fbf9ff926b8f55d1fe43b2fc7b1d2d5b2e6815d49ff1b218bae2140053706b44d8a0135e3f4e9fcdb08a5c14ebadbea6a86c95642fa672976f88ed5",
                                "ACCOUNT":"********",
                                "BALANCE_CODE":"ZQRG01",
                                "ORDER_AMT":"1100000",
                                "BALANCE_ACCOUNT":"0CZZ55001714",
                                "TA_SN":"0"
                            }
                        }
                    ]
                }'''
    # 电子协议签署
    req_800061 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "800061"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"********************************.QQ.ZXG",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "SESSION":"cb5e87da40aeec78791c312a3628b2a07532df8622c945bc0722095d7c4c4d577203aff51db7ff8a5ac6f7a87db2360ba92abdc86999877c0de93a96a625c740d39958d2ffb14ec6d358fea129e700afed0f7b784d40bff68967c4e97fbf9ff926b8f55d1fe43b2fc7b1d2d5b2e6815d49ff1b218bae2140053706b44d8a0135e3f4e9fcdb08a5c14ebadbea6a86c95642fa672976f88ed5",
                                "ACCOUNT":"********",
                                "TYPE":"1",
                                "BALANCE_CODE":"SRB893"
                            }
                        }
                    ]
                }'''
    # 查询理财持仓
    req_800160 = '''
                     {
                        "REQUESTS": [
                            {
                                "REQ_MSG_HDR": {
                                    "SERVICE_ID": "800160"
                                },
                                "REQ_COMM_DATA": {
                                    "USER_ID_CLS":"1",
                                    "COMPANY_ID":"15900",
                                    "USER_ID":"********************************.QQ.ZXG",
                                    "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                    "SESSION":"cb5e87da40aeec78791c312a3628b2a07532df8622c945bc0722095d7c4c4d577203aff51db7ff8a5ac6f7a87db2360ba92abdc86999877c0de93a96a625c740d39958d2ffb14ec6d358fea129e700afed0f7b784d40bff68967c4e97fbf9ff926b8f55d1fe43b2fc7b1d2d5b2e6815d49ff1b218bae2140053706b44d8a0135e3f4e9fcdb08a5c14ebadbea6a86c95642fa672976f88ed5",
                                    "ACCOUNT":"********",
                                    "BALANCE_CODE":"SRB893",
                                    "TA_SN":"0"

                                }
                            }
                        ]
                    }'''

    # 查询风险股票提示
    req_600034 = '''
                 {
                    "REQUESTS": [
                        {
                            "REQ_MSG_HDR": {
                                "SERVICE_ID": "600034"
                            },
                            "REQ_COMM_DATA": {
                                "USER_ID_CLS":"1",
                                "COMPANY_ID":"15900",
                                "USER_ID":"********************************.QQ.ZXG",
                                "TCC":"os-ppuO0fWxMYjSMWxiyFH14HwvY|***********|10.14.87.218",
                                "SESSION":"bd39d07d7d91a756c305936ba6e3c1b2d9b6e151ed5788749167a7f0d7076eda3cdf02a2fdb8fea32af3adb2564a7d87bbabe21e6831a8f68cc0851333c1a3948801439eab28f8f8f7f76ed1d2e3d110003cc5df3f59db12a6096ae1d4ca0675867b1c4e5594b4a17a2dd536a9d6ac5c00dbf153068898ca",
                                "ACCOUNT":"********",
                                "MARKET":"0",
                                "SECU_CODE":"127012"

                            }
                        }
                    ]
                }'''
    req1 = '''
                {
    "REQUESTS":[
        {
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY__ID":"15900",
                "KEY_STR":"",
                "ORDER_DATE":"",
                "REC_COUNT":"20",
                "SESSION":"8609fb3c99ddb329101*b6cIcbHcca",
                "USER_ID":"********************************.QQ.ZXG"
            },
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600171",
                "SIGN":"C0820CBCC417994CEFDC46497D70ED0F",
                "TIME_STAMP":"**********"
            }
        }
    ]
}
    '''
    req1 = '''
            {
    "REQUESTS":[
        {
            "REQ_COMM_DATA":{
                "ACCOUNT":"********",
                "COMPANY_ID":"15900",
                "KEY_STR":"",
                "ORDER_DATE":"",
                "REC_COUNT":"20",
                "SESSION":"86093db1aeee8b86100*b6cJbcJcHa",
                "TA_SN":"0",
                "USER_ID":"os-ppuO1XXmfLiDZSpW25xap8lgY.ZXG.2",
                "USER_ID_CLS":"1"
            },
            "REQ_MSG_HDR":{
                "SERVICE_ID":"600171",
                "SIGN":"9AA87B5F22C02A6B83D506FA087B4B09",
                "TIME_STAMP":"**********"
            }
        }
    ]
}
        '''

    req2 = '''
            {
    "REQUESTS":[
        {
            "REQ_MSG_HDR":{
                "SERVICE_ID":"800063",
                "TIME_STAMP":"**********",
                "SIGN":"F36478C6B4315095038DEE8CFBBC6D60"
            },
            "REQ_COMM_DATA":{
                "USER_ID_CLS":"1",
                "BALANCE_CODE":"ZQRG01",
                "TA_SN":"0",
                "USER_ID":"********************************.QQ.ZXG",
                "ACCOUNT":"********",
                "SESSION":"f7a565d1fade94078af037b1ae433be8f5a46332a3649bafa9fc58a392bcb4c734124c45c498bc1b610a59f51de0a1b683dd0ebf076edfab17d03594b22b87292600dc2f6fbd06ab64946e3cea69725a8aa952f4ab07a7ae818044be4515346054b8ff9f6f86a1d64d8dc7b244941ab4208a9085b2c201bd58cec93d298b7f66f9aba76ea7eaf8153b79accef0069e572cc8b35699081a55",
                "COMPANY_ID":"15900"
            }
        }
    ]
}
            '''
    req_600143 = '''
    {
        "REQUESTS": [
            {
                "REQ_MSG_HDR": {
                    "SERVICE_ID": "600143",
                    "TIME_STAMP": "**********",
                    "SIGN": "********************************"
                },
                "REQ_COMM_DATA": {
                    "TRD_PWD": "60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb",
                    "USER_ID_CLS": "1",
                    "USER_ID": "os-********",
                    "ACCOUNT": "********",
                    "COMPANY_ID": "15900",
                    "MARKET": "0",
                    "TRD_ID": "3B",
                    "SECU_CODE": "000001",
                    "ORDER_PRICE": "15.28",
                    "ORDER_QTY": "500",
                    "EXT_SERIAL_NO": "1234455",
                    "SECU_ACC": "**********",
                    "TCC": "os-ppuOSi1wPDei9JbObGUlBUOXk|***********|10.40.162.39"
                }
            }
        ]
    }'''

    #886&4|887&2|888&4|889&|890&4|891&|892&3|893&|894&4|895&|896&|897&4|898&|899&4|900&|901&3|
    while iLoopStep < 1:
        kesb_req('aaa', req_600143)
        iLoopStep += 1
    #让线程退出 a
    main_On_flag = True
    while True:
        time.sleep(1)
