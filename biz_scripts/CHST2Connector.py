# -*- coding: utf-8 -*-

from HS import *
import json
import asyncio
from kwlpyfunction import  *
from kwl_py_log import *

g_CHST_instant = None
g_config_path = 'T2Config.ini'

async def set_event_func(obj_event:asyncio.Event):
    obj_event.set()

class CHST2Connector(CHST2Wrapper):
    def __init__(self, loopEv):
        self.ans_map = {}
        super().__init__()
        self.loop = loopEv

    def ans_callback(self, send_index, json_string_ans):
        # kwl_py_write_log(json_string_ans, 'ans_callback', ZTLOG_INFO, msgid='send_kafka')
        if str(send_index) not in self.ans_map:
            pass
        else:
            self.ans_map[str(send_index)]['ans'] = json_string_ans
            asyncio.run_coroutine_threadsafe( set_event_func( self.ans_map[str(send_index)]['wait_obj']), self.loop )

    def connect(self, configfile):
        self.Init(configfile)
        return self.Connect(self, "ans_callback")

    def isConnect(self):
        return self.IsConnect()

    def set_err_ans(self, err_code, err_msg):
        err_json = {"ANSWERS": [
            {"ANS_MSG_HDR":
                {
                    "MSG_CODE": str(err_code),
                    "MSG_TEXT": err_msg
                }
            },
            {"ANS_MSG_DATA":
                None
            }
        ]
        }
        return json.dumps(err_json)

    async def send_request(self, biz_id, request, timeout_sec = 10):
        send_index = self.AsynSend(biz_id, request)
        if send_index < 0:
            return self.set_err_ans(-1001, 'send request failed:%s' % (self.GetErrMsg()))

        wait_event = asyncio.Event()
        req_status = {
            "wait_obj": wait_event,
            "ans": ''
        }
        self.ans_map[str(send_index)] = req_status
        try:
            await asyncio.wait_for(wait_event.wait(), timeout_sec)
        except asyncio.TimeoutError:
            self.ans_map.pop(str(send_index))
            return self.set_err_ans(-1002, 'recv answer timeout')

        ans = self.ans_map[str(send_index)]['ans']
        self.ans_map.pop(str(send_index))
        return ans



def get_CHST_instant():
    if not g_CHST_instant.isConnect():
        g_CHST_instant.connect(g_config_path)
    return g_CHST_instant

def init_CHST_instant(loop):
    global g_CHST_instant
    g_CHST_instant = CHST2Connector(loop)



async def async_main(hs):
    bizid = 339303
    # reqdict = {'op_branch_no': '', 'branch_no': '33', 'fund_account': '********', 'entrust_way': '8', 'op_entrust_way': '8', 'issue_date': '********'}
    req_331100 = '''{"biznum" : "331100","password_type":"", "op_branch_no" : "5010","op_entrust_way" : "h","op_station" : "127.0.0.1","password" : "111121","input_content" : "1","account_content" : "********","content_type" : "0" }'''
    reqJson = '''{"entrust_price": "18.01", "op_entrust_way": "h", "COMPANY_ID": "15900", "bsflag": "", "entrust_prop": "0", "custid": "********", "op_branch_no": "5010", "user_token": "", "op_station": "127.0.0.1", "exchange_type": "1", "branch_no": "33", "SESSION": "000000", "fund_account": "********", "registe_sure_flag": "", "password": "", "stock_code": "600446", "password_type": "2", "passwd_tmp": ""}'''
    r_332200 = '''{"op_branch_no": "5010", "exchange_type": "", "bank_no": "6", "sysnode_id": "2", "password_type": "2", "fund_password": "222222", "bank_password": "", "money_type": "0", "op_station": "127.0.0.1", "transfer_direction": "1", "user_token": "", "fund_account": "********", "passwd_tmp": "356b6b76bea5154f", "client_id": "********", "branch_no": "10", "password": "111111", "op_entrust_way": "h", "occur_balance": "10.00"}'''
    r_331300 = '''{"op_branch_no": "5010", "op_station": "127.0.0.1", "sysnode_id": "2", "op_entrust_way": "h", "password_type": "2", "client_id": "********", "fund_account": "********", "user_token": "", "branch_no": "33", "exchange_type": "", "password": "111111", "passwd_tmp": "356b6b76bea5154f"}'''
    r_339303 = '''{
    "qryflag":"1",
    "position_str":"",
    "password_type":"1",
    "start_date":"********",
    "ordergroup":"",
    "sysnode_id":"2",
    "exchange_type":"",
    "user_token":"",
    "end_date":"********",
    "op_entrust_way":"h",
    "stock_code":"",
    "password":"",
    "fund_account":"********",
    "op_branch_no":"5010",
    "branch_no":"33",
    "op_station":"",
    "bankcode":"",
    "request_num":"15",
    "ordersno":"",
    "client_id":"********",
    "passwd_tmp":""
}'''
    ans = await hs.send_request(bizid, r_339303, 3)
    ans_list = json.loads(ans)

async def async_main_mulit_instanse(hs, loop):
    await asyncio.gather(async_main(hs), async_main(hs))






if __name__ == '__main__':
    loop = asyncio.get_event_loop()
    hs = CHST2Connector(loop)
    ret = hs.connect('T2Config.ini')
    loop.set_debug(True)
    loop.run_until_complete(async_main_mulit_instanse(hs, loop))

