# -*- coding: utf-8 -*-

#特殊交易日设置(一般没有)
g_Trade_Date = [

]
#休市日期设置  起始时间,结束时间,休市描述(运维每年需要维护一次)
g_Rest_Date = [
    ('20161231000000','20170102235959','元旦节'),
    ('20170127000000','20170202235959','春节'),
    ('20170402000000','20170404235959','清明节'),
    ('20170501000000','20170501235959','劳动节'),
    ('20170528000000','20170530235959','端午节'),
    ('20171001000000','20171008235959','中秋节,国庆节')
]
#市场开市时间  key为市场  上午开市时间、上午休市时间、下午开市时间、下午休市时间、key描述
g_Market_TradeTime = {
    '0':{
           'beginAM':'090000','endAM':'113000', 'beginPM':'113000','endPM':'160000','remark':'深圳A'
    },
    '1':{
           'beginAM':'090000', 'endAM':'113000', 'beginPM':'113000','endPM':'160000','remark':'上海A'
    },
    '2':{
           'beginAM':'090000','endAM':'113000','beginPM':'113000','endPM':'160000','remark':'深圳B'
    },
    '3':{
           'beginAM':'090000','endAM':'113000','beginPM':'113000','endPM':'160000','remark':'上海B'
    },
    '4':{
           'beginAM':'090000', 'endAM':'113000','beginPM':'113000','endPM':'160000','remark':'深基金'
    },
    '5':{
           'beginAM':'090000', 'endAM':'113000', 'beginPM':'113000','endPM':'160000','remark':'沪基金'
    },
    '9':{
           'beginAM':'090000','endAM':'113000','beginPM':'113000','endPM':'160000','remark':'场外基金'
    }
}
