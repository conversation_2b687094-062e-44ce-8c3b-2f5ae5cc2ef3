

#这里配置bpagent业务相关逻辑
#pyreqCommquery类型功能号对应的调用流程,功能号对应的出参控制,True为开启出参控制,否则适配器返回什么这边就给什么
g_BpagentreqCommqueryCallway ={
    '600020':['KOW003',False,[]],
    '600030':['600030',True,['COMPANY_NAME','BRANCH_CODE','BRANCH_NAME']],
    '600049':['600049',False,[]],
    '600050':['KOW009',True,['STATUS','FAIL_TEXT']],
    '600055':['600055',False,[]],
    '600060':['600060',True,['MARKET','SECU_ACC','STATUS','TRD_STATUS','GEM_STATUS','GEM_FIX_STATUS','KE_CHUANG_STATUS', 'ACCOUNT', 'SH_KZZ_STATUS', 'SZ_KZZ_STATUS', 'BALANCE_SERVICE_STATUS']],
    '600061':['600061',True,['MARKET','SECU_ACC','ACCOUNT_DATE','NEW_ACC_FLAG']],
    '600063':['600063',False,[]],
    '600064':['600064',False,[]],
    '600070':['600070',True,['STATUS']],
    '600080':['600080',True,['ACCOUNT']],
    '600120':['600120',True,['ACCOUNT','CURRENCY','BALANCE','AVAILABLE','FRZ_AMT','OUTSTANDING','DRAW_AMT','ASSERT_VAL','MKT_VAL']],
    '600123': ['600123', True, ['ACCOUNT','ASSERT_AVG_VAL']],
    '600124': ['600124', True, ['ACCOUNT','CURRENCY','NEW_STOCK_PAY']],
    '600130':['600130',True,['TRD_QTY']],
    '600140':['600140',True,['ORDER_ID']],
    '600141':['600141',True,['ORDER_ID']],
    '600174':['600174',True,['ORDER_ID']],
    '600410':['600410',False,[]],
    '600143':['600143',True,['ORDER_ID']],
    '600150':['600150',True,['ORDER_ID']],
    '600160':['600160',True,['ORDER_DATE','ORDER_TIME','TRD_DATE','USER_CODE','USER_NAME','ACCOUNT','CURRENCY','BRANCH','SECU_ACC','TRD_ID','MARKET','SECU_NAME','SECU_CODE','SEAT','PRICE','QTY','ORDER_AMT','IS_WITHDRAW','DCL_FLAG','MATCHED_QTY','WITHDRAWN_QTY','MATCHED_AMT','EXT_SERIAL_NO','ORDER_ID','KEY_STR','MATCHED_PRICE']],
    '600161':['600161',True,['ORDER_DATE','ORDER_TIME','TRD_DATE','USER_CODE','USER_NAME','ACCOUNT','CURRENCY','BRANCH','SECU_ACC','TRD_ID','MARKET','SECU_NAME','SECU_CODE','SEAT','PRICE','QTY','ORDER_AMT','IS_WITHDRAW','DCL_FLAG','MATCHED_QTY','WITHDRAWN_QTY','MATCHED_AMT','EXT_SERIAL_NO','ORDER_ID','KEY_STR','MATCHED_PRICE']],
    '600170':['600170',True,['ORDER_DATE','ORDER_TIME','TRD_DATE','USER_CODE','USER_NAME','ACCOUNT','CURRENCY','BRANCH','SECU_ACC','TRD_ID','BIZ_NO','MARKET','SECU_NAME','SECU_CODE','SEAT','PRICE','QTY','ORDER_FRZ_AMT','IS_WITHDRAW','DCL_TIME','DCL_FLAG','MATCHED_QTY','WITHDRAWN_QTY','MATCHED_AMT','EXT_SERIAL_NO','ORDER_ID','KEY_STR']],
    '600172':['600172',True,['ORDER_DATE','ORDER_TIME','TRD_DATE','USER_CODE','USER_NAME','ACCOUNT','CURRENCY','BRANCH','SECU_ACC','TRD_ID','BIZ_NO','MARKET','SECU_NAME','SECU_CODE','SEAT','PRICE','QTY','ORDER_FRZ_AMT','IS_WITHDRAW','DCL_TIME','DCL_FLAG','MATCHED_QTY','WITHDRAWN_QTY','MATCHED_AMT','EXT_SERIAL_NO','ORDER_ID','KEY_STR']],
    '600171':['600171',True,['ORDER_DATE', 'ORDER_TIME', 'BRANCH_CODE', 'SECU_ACC', 'TRD_ID', 'MARKET', 'BALANCE_CODE', 'BALANCE_NAME', 'BALANCE_TYPE', 'BALANCE_TIME_LIMIT', 'PURCHASE_RATE', 'REPURCHASE_RATE', 'TRADE_DAYS', 'EXPIRE_DATE', 'PURCHASE_QTY', 'PURCHASE_AMT', 'REPURCHSE_QTY', 'REPURCHASE_AMT', 'FINISH_INCOME', 'ESTIMATE_INCOME_DOWN', 'ESTIMATE_INCOME_UP', 'ORDER_ID', 'ORDER_FLAG', 'ACCOUNT', 'EXT_SERIAL_NO', 'REDEEM_ID', 'EXTENSION_FLAG', 'HAS_EXTEND', 'ORIGIN_ID', 'EXT_RET_CODE', 'EXT_RET_MSG', 'KEY_STR',]],
    '600173':['600173',True,['BALANCE_CODE', 'BALANCE_NAME', 'BALANCE_TYPE', 'BALANCE_TIME_LIMIT', 'PURCHASE_RATE', 'REPURCHASE_RATE', 'ORDER_DATE', 'ORDER_TIME', 'REPURCHASE_AMT', 'ORIGIN_ID', 'OFFER_CONTRACT_NO', 'REDEEM_ID', 'ORDER_TYPE', 'HAS_EXTEND', 'ORDER_FLAG', 'KEY_STR',]],
    '600180':['600180',True,['TRD_DATE','CURRENCY','SECU_ACC','TRD_ID','ORDER_ID','MARKET','SECU_NAME','SECU_CODE','PRICE','QTY','MATCH_TYPE','MATCHED_TIME','MATCHED_SN','MATCHED_PRICE','MATCHED_QTY','MATCHED_AMT','KEY_STR','ACCOUNT']],
    '600190':['600190',False,['SECU_CODE','SECU_NAME','CURRENCY','TRD_ID','MATCHED_QTY','MATCHED_AMT','SETT_AMT','COMMISSION','STAMP_DUTY','TRANS_FEE','XTRANS_FEE','HANDLE_FEE','ADMIN_FEE','TRADE_FEE','CLEAR_FEE','KEY_STR','SECU_ACC','MATCHED_DATE','MATCHED_TIME','MATCHED_PRICE','MARKET']],
    '600200':['600200',True,['MARKET','SECU_ACC','SECU_NAME','SECU_CODE','BRANCH','SHARE_QTY','SHARE_AVL','CURRENT_COST','MKT_VAL','SHARE_BLN','MKT_PRICE','INCOME_AMT','CURRENCY','RATE','KEY_STR']],
    '600210':['600210',True,['SERIAL_NO']],
    '600220':['600220',True,['SERIAL_NO']],
    '600040':['600040',True,['STATUS','M_TEL','EDUCATION','OCCU_TYPE','ID_NAME','ACCOUNT_DATE','ACCOUNT','ID_TYPE','ID_CODE','ID_BEG_DATE','ID_EXP_DATE','ACCOUNT']],
    '600041':['600041',True,['RISK_TYPE', 'RISK_EXPIRED', 'EXPIRED_DATE', 'SP_INVESTOR']],
    '600042':['600042',True,['STATUS', 'SZ_ST_STATUS']],
    '600043':['600043',False,[]],
    '600046':['600046',False,[]],
    '600230':['600230',True,['TRAN_DATE','TRAN_TIME','EXT_INST','CURRENCY','CPTL_AMT','STATUS','SERIAL_NO','EXT_RET_CODE','EXT_RET_MSG','BIZ_CODE','KEY_STR']],
    '600231':['600231',False,[]],
    '600232':['600232',False,[]],
    '600270':['600270',True,['TRAN_DATE','TRAN_TIME','EXT_INST','TRD_DATE','CURRENCY','CPTL_AMT','STATUS','SERIAL_NO','EXT_RET_CODE','EXT_RET_MSG','EXT_RET_MSG','BIZ_CODE','KEY_STR','ACCOUNT']],
    '600310':['600310',True,['CURRENCY','BALANCE','SERIAL_NO']],
    '600320':['600320',False,[]],
    '600330':['600330',False,['KEY_STR','TRD_DATE','MARKET','SECU_ACC','SECU_CODE','SECU_NAME','ORDER_ID','ASSIGN_SN','ASSIGN_COUNT','ASSIGN_DATE','PURC_CODE']],
    '600331':['600331',True,['TRD_DATE','MARKET','SECU_ACC','SECU_CODE','SECU_NAME','MATCH_PRICE','ORDER_ID','MATCHED_QTY','ASSIGN_SN','ASSIGN_COUNT','ASSIGN_DATE','ASSIGN_STATUS']],
    '600340':['600340',False,['KEY_STR','MARKET','SECU_ACC','SECU_CODE','SECU_NAME','MATCH_PRICE','MATCHED_QTY','ASSIGN_DATE']],
    '600360':['600360',True,['EXT_INST','EXT_ACC','CURRENCY','MAIN_FLAG','OPEN_FLAG','BANKPWD_FLAG','ACCOUNT']],
    '600380':['600380',True,['MARKET','SECU_ACC','QYSL','QYSL_KCB','ACCOUNT']],
    '600390':['600390',False,['KEY_STR','ACCOUNT','MATCH_DATE','SECU_ACC','MARKET','SECU_NAME','SECU_CODE','MATCH_TIME','MATCH_PRICE','MATCH_QTY','MATCH_AMT','FUNDEFFECT','FUNDBAL','CURRENCY','FEEFRONT','FEE_GHF','FEE_JSF','FEE_JSXF','FEE_JYGF','FEE_QSF','FEE_QTF','FEE_SXF','FEE_YHS','FEE_ZGF','ORDER_ID','SECU_BAL','TRD_BIZ_CODE','TRD_ID','TRD_NAME','ACCOUNT','RATE']],
    '600391':['600391',False,[]],
    '600401':['600401',True,['ACCOUNT','CURRENCY','BALANCE','AVAILABLE','FRZ_AMT','OUTSTANDING','MKT_VAL','ASSERT_VAL']],
    '600402':['600402',True,['DRAW_AMT','BALANCE']],
    '600403':['600403',False,[]],
    '600404':['600404',False,[]],
    '600405':['600405',False,['serial_no1','serial_no2']],
    '600406':['600406',True,['BALANCE','AVAILABLE']],
    '600407':['600407',True,['TRAN_DATE','TRAN_TIME','SERIAL_NO','BIZ_CODE','BUSINESS_NAME','FUND_ACCOUNT_SRC','BANK_NAME_SRC','FUND_ACCOUNT_DEST','BANK_NAME_DEST','MONEY_TYPE','CPTL_AMT','STATUS','KEY_STR','business_flag','deal_status']],
    '600408':['600408',True,['TRAN_DATE','TRAN_TIME','SERIAL_NO','BIZ_CODE','BUSINESS_NAME','ACCOUNT','FUND_ACCOUNT_DEST','MONEY_TYPE','CPTL_AMT','STATUS','BANK_NO','BANK_NAME','BANK_NO_DEST','BANK_NAME_DEST','KEY_STR','business_flag','deal_status']],
    '700713':['700713',False,[]],
    '700714':['700714',True,['FRZ_AMT','FRZ_STATUS']],
    '601160':['601160',True,['ORDER_DATE','ORDER_TIME','TRD_DATE','USER_CODE','USER_NAME','ACCOUNT','CURRENCY','BRANCH','SECU_ACC','TRD_ID','MARKET','SECU_NAME','SECU_CODE','SEAT','PRICE','QTY','ORDER_AMT','IS_WITHDRAW','DCL_FLAG','MATCHED_QTY','WITHDRAWN_QTY','MATCHED_AMT','EXT_SERIAL_NO','ORDER_ID','KEY_STR','RATE']],
    '601170':['601170',True,['ORDER_DATE','ORDER_TIME','TRD_DATE','CURRENCY','TRD_ID','MARKET','SECU_NAME','SECU_CODE','PRICE','QTY','ORDER_FRZ_AMT','MATCHED_QTY','WITHDRAWN_QTY','MATCHED_AMT','EXT_SERIAL_NO','ORDER_ID','SECU_ACC','KEY_STR','DCL_FLAG','ACCOUNT','RATE']],
    '601190':['601190',True,['MARKET','SECU_CODE','SECU_NAME','CURRENCY','TRD_ID','ORDER_ID','MATCHED_QTY','MATCHED_AMT','MATCHED_PRICE','MATCHED_DATE','MATCHED_TIME','MATCHED_SN','SETT_AMT','COMMISSION','STAMP_DUTY','TRANS_FEE','XTRANS_FEE','HANDLE_FEE','ADMIN_FEE','TRADE_FEE','CLEAR_FEE','SECU_ACC','KEY_STR','ACCOUNT','RATE']],
    '500401':['500401',True,['TOTAL_AMT_SZ','USABLE_AMT_SZ','STATUS_SZ','UPDATE_DATE_SZ','TOTAL_AMT_SH','USABLE_AMT_SH','STATUS_SH','UPDATE_DATE_SH']],
    '500402':['500402',True,['RATE_BUY_SZ','RATE_SELL_SZ','RATE_BUY_SH','RATE_SELL_SH']],
    '601180':['601180',True,['TRD_DATE','CURRENCY','SECU_ACC','CURRENCY','TRD_ID','ORDER_ID','MARKET','SECU_NAME','SECU_CODE','PRICE','QTY','MATCH_TYPE','MATCHED_TIME','MATCHED_SN','MATCHED_PRICE','MATCHED_QTY','MATCHED_AMT','KEY_STR','RATE','ACCOUNT']],
    '600121':['600121',True,['ORDER_QTY']],
    '500403':['500403',True,['MARKET','SNO','SECU_NAME','SECU_CODE','DOUBLE_FLAG','KEY_STR']],
    '500060':['500060',True,['MARKET','SECU_ACC','OPEN_FLAG','PRIOR_FLAG','CAN_OPEN_FLAG','NOT_OPEN_REASON','ACCOUNT']],
    '500061':['500061',False,[]],
    '500062':['500062',False,[]],
    '500063':['500063',True,['BASIC_FLAG']],
    '800011':['800011',False,[]],
    '800012':['800012',True,['XID_SESSION']],
    '800013':['800013',False,[]],
    '800014': ['800015', False, []],
    '800015': ['800015', False, []],
    '600044':['600044',False,[]],
    '600047':['600047',False,[]],
    '601140':['601140',True,['ORDER_ID']],
    '601150':['601150',True,['ORDER_ID']],
    '600045':['600045',True,['XGEM_STATUS']],
    '600052':['600052',False,[]],
    '600026':['600026',False,[]],
    '800060':['800060',False,[]],
    '800049':['800049',False,[]],
    '800063':['800063',False,[]],
    '800064':['800064',False,[]],
    '800410':['800410',False,[]],
    '800140':['800140',False,[]],
    '800061':['800061',False,[]],
    '800160':['800160',False,[]],
    '600034':['600034',False,[]],
}

g_CheckSession = {
    'CHECKFLAG':True,  #校验票据的开关
    'CKLBM':['600140','600150','601140','601150','800013','600047','600044', '600141', '600174']   #必须验票据的功能号
}

#这里配置那些不需要验证SESSION，也不需要补SESSION的功能号
g_NoSession = ['600070','600080','600050','800015','600020']

# 这里配置那些全局查询接口，该接口用于查询当前时刻股市信息的全局信息。
# 由于不针对某个客户，所以该类型的接口的入参 无USER_ID、ACCOUNT以及USER_ID_CLS 等字段
g_QueryStockInfo = ['500401','500402','500403','600320', '600410']

#DH edit
#这里配置那些需要验证SESSION，但没有USER_ID_CLS入参的功能号
g_NoUSERIDCLS = ['600124', '600064', '600055', '600174', '600173', '600171', '600141']