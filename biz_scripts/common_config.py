#!/usr/bin/python
# -*- coding: utf-8 -*-

# 券商代码
g_company_id = '15900'
g_user_id_cls = '1'

# 网厅session换token需要验签的key
g_sign_key = 'RFXEHCJNWCHlvRkGyj'

# session的des加解密秘钥
g_session_des_key = 'se410301'

# 发送消息失败，保存消息路径
g_kafka_msg_log_path = './kafka_pend_msg.log'

# kafka的队列配置
g_kafka_config = {
    "host": "************",
    "port": 9092,
    "sub_topics":["sub_trade_topic", "sub_tran_bank_topic"], # 订阅交易,银证转账主题
}

# 331203 模板编号
AGREE_MODEL_NO = "****************"
g_decrypt_param = ['password', 'fund_password', 'bank_password', 'passwd_tmp']

# 600410产品代码
STOCK_CODE = "GDL008,GDL019"
