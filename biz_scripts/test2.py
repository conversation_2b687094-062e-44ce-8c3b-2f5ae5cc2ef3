import xlwt
from adapttransdict import functiondict

def writeExcel():
    nRow = 0
    excelBook = xlwt.Workbook(encoding='utf-8')
    excelFileName = 'tid2hid.xls'

    excelSheetName = excelFileName[:-4]
    excelSheet = excelBook.add_sheet(excelSheetName, cell_overwrite_ok=True)
    rowHead = ['腾讯功能号', '恒生功能号']
    for i in range(len(rowHead)):
        excelSheet.write(nRow, i, rowHead[i])
        excelSheet.col(i).width = 256 * 20  # 10个字符，一个中文占两个字符

    for key in functiondict:
        nRow += 1
        excelSheet.write(nRow, 0, key)
        excelSheet.write(nRow, 1, functiondict[key]['dstfuncid'])

    try:
        filePath = './' + excelFileName
        excelBook.save(filePath.encode('utf-8'))
    except Exception as e:
        print('错误原因: ' + repr(e))


writeExcel()