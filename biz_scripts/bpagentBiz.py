#把bpagent的业务加入其中
from typing import Any, Union

from KWL import *
import kwlpyfunction
import KwlTools
from threading import *
from kcxpconfig import *
from tyrzBiz import *
import NginxConfig
if kwlpyfunction.isLinuxSystem():
    from KWL_Nginx import *
else:
    def GetKWLParam(sKey):
        return '99999'
    def SendResponse(relativeReq, ansConetent, iLenth):
        print('main Send Ans back:' + ansConetent)
    def IsNginxOn():
        return True
import time
import datetime
from kcxpconfig import *  #调kcxp的配置
from bpagentconfig import * #bpagent配置项
import json
import traceback
import os
import adaptspd
from asyncio import *
import asyncio
import requests
from threading import *
import kafka_work
from common_config import *
from kwl_py_log import *

g_ZXdictlock = Lock()
g_dictZXAns = {}  #协程调总线存储的应答
g_IsZXThreadInited = False

#制造应答字符串
def MakeAnsStr(code,msg,debug,session):
    anstext = {"ANSWERS":[{"ANS_COMM_DATA":None,"ANS_MSG_HDR":{"MSG_CODE":str(code),"MSG_LEVEL":"0","MSG_TEXT":msg,"DEBUG_MSG":debug,"SESSION":session}}]}
    return json.dumps(anstext,ensure_ascii=False)
#返回普通应答
def SetErrMsg(ref_request,code,msg,debug,session):
    anstext = {"ANSWERS":[{"ANS_COMM_DATA":None,"ANS_MSG_HDR":{"MSG_CODE":str(code),"MSG_LEVEL":"0","MSG_TEXT":msg,"DEBUG_MSG":debug,"SESSION":session}}]}
    anstext = json.dumps(anstext,ensure_ascii=False)
    kwl_py_write_log('应答串:%s\n'%(anstext), 'ans_to_wzq', ZTLOG_INFO, msgid=ref_request)
    SendResponse(ref_request, anstext,len(anstext))
#返回数据型应答
def SetErrMsgAndBody(ref_request,code,msg,debug,session,dictbody):
    if len(dictbody) == 0:
        anstext = {"ANSWERS":[{"ANS_COMM_DATA":None,"ANS_MSG_HDR":{"MSG_CODE":str(code),"MSG_LEVEL":"0","MSG_TEXT":msg,"DEBUG_MSG":debug,"SESSION":session}}]}
    else:
        anstext = {"ANSWERS":[{"ANS_COMM_DATA":dictbody,"ANS_MSG_HDR":{"MSG_CODE":str(code),"MSG_LEVEL":"0","MSG_TEXT":msg,"DEBUG_MSG":debug,"SESSION":session}}]}
    anstext = json.dumps(anstext,ensure_ascii=False)
    kwl_py_write_log('应答串:%s\n'%(anstext), 'ans_to_wzq', ZTLOG_INFO, msgid=ref_request)
    SendResponse(ref_request, anstext,len(anstext))

# 返回国金网厅应答
def SetGJWTMsgAndBody(ref_request,code,msg,dictbody):
    anstext = {"code": code, "message":msg, "result":dictbody}
    anstext = json.dumps(anstext, ensure_ascii=False)
    kwl_py_write_log('国金网厅应答串:%s' % (anstext), 'SetGJWTMsgAndBody', ZTLOG_INFO, msgid=ref_request)
    SendResponse(ref_request, anstext, len(anstext))


#LOGIN_TYPE =1客户代码，2资金帐号，3深A股东，4 深B股东，5 沪A股东，6 沪B股东
g_dict_logintype ={
     1:'C',
     2:'Z',
     3:'0',
    4:'2',
    5:'1',
    6:'3',
    '1':'C',
    '2':'Z',
    '3':'0',
    '4':'2',
    '5':'1',
    '6':'3'
}


# opstation必须和session保持一致有效。
def setSessionRedis(rediskey, redisvalue, reqdict, session):
    kwl_py_write_log('set redis key:[%s], value:[%s]' % (rediskey, redisvalue), 'setSessionRedis', ZTLOG_INFO, msgid='')
    SetRedisKeyEx(rediskey, redisvalue, g_redis_expiretime)
    setOpStationRedis(reqdict, session)

# 同时延长两个的周期
def expireSessionRedis(rediskey, op_red_key):
    ExpireRedisKey(rediskey, g_redis_expiretime)
    ExpireRedisKey(op_red_key, g_redis_expiretime)

#同步-bpagent签约
async def pyreqbpagent600001(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        reqdict['inputid'] = reqdict['LOGIN_CODE']
        reqdict['inputtype'] = 2
        reqdict['trdpwd'] = reqdict['TRD_PWD']

        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['410301'](ref_request,'410301',reqdict)
        if iRet != '0':
            return SetErrMsg(ref_request,iRet,Msg,'','')
        if  len(RowsData) == 0:
             return SetErrMsg(ref_request,-100,'410301接口未返回第二结果集','','')
        for row in RowsData:
            if row['asset_prop'] != '0':
                err_msg = '当前为普通证券账户交易系统，请使用普通证券交易账户登录。'
                return SetErrMsg(ref_request, -102, err_msg, '', '')
        SECUSZA = ''
        SECUSHA = ''
        SECUSZB = ''
        SECUSHB = ''
        bInsertTyrz = False
        for eachrow in RowsData:
            fundid = eachrow['fundid']
            custid = eachrow['custid']
            orgid = eachrow['orgid']
            custname = eachrow['custname']
            market = eachrow['market']
            if market == '0':
                SECUSZA =  eachrow['secuid']
            elif market == '1':
                SECUSHA =  eachrow['secuid']
            elif market == '2':
                SECUSZB =  eachrow['secuid']
            elif market == '3':
                SECUSHB =  eachrow['secuid']
            if 'sysnode_id' in eachrow:  #恒生需要存节点编号到票据
                reqdict['TYPE_VALUE'] = eachrow['sysnode_id']
                reqdict['TYPE_KEY'] = 'sysnode_id'
                bInsertTyrz = True

        #调统一认证
        reqdict["ID"] = '123'
        reqdict["USER_NAME"] = custname
        reqdict["ORG_CODE"] = orgid
        reqdict["CUSTID"] = custid   # 券商客户号
        reqdict["ACCOUNT"] = fundid  #资产帐号
        reqdict["SECUSZA"] = SECUSZA
        reqdict["SECUSZB"] = SECUSZB
        reqdict["SECUSHA"] = SECUSHA
        reqdict["SECUSHB"] = SECUSHB
        reqdict["BANKINFO"] = ""
        if bInsertTyrz:
            iRet,msg,data = T0005006_insertExinfo(reqdict)
            if iRet != 0:
                return SetErrMsg(iRet,msg,'插入客户拓展信息时失败','')
        iRet,msg,data = T0005005_sign(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')

        # 签约留痕，签署免密协议和免责协议 （add by fusq  2020 0119）
        code, msg, ans = user_sign_mark(reqdict)
        if code != 0:
            return SetErrMsg(ref_request, code, msg, '', '')

        #客户对应的票据,信息  #延长周期
        rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
        redisvalue = {}
        #密文票据和字典票据
        redisvalue['SESSION'] = data[0]['SESSION']
        redisvalue['INFO'] = data[0]['SESSION_JSON']
        SetRedisKeyEx(rediskey,redisvalue,g_redis_expiretime)
        #rediscl.set(encryptsession,DataAcid)   #票据对应的客户
        #rediscl.expire(encryptsession,g_redis_expiretime)  #延长周期
        return SetErrMsgAndBody(ref_request, iRet, '签约成功', '', data[0]['SESSION'], [{"ONLINE_FLAG": "0"}])
        # SetErrMsg(ref_request,iRet,'签约成功','',data[0]['SESSION'])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext), 'SetGJWTMsgAndBody', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#同步-bpagent登录接口
async def pyreqbpagent600010(ref_request,jsonkwlreq):
    #调统一认证T0005001取票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        reqdict['TRD_PWD'] = reqdict['USER_PWD']
        reqdict['SESSION_TYPE'] = 'Login'
        iRet,msg,data = T0005001_makesession(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        jsonsession = data[0]['SESSION_JSON']
        session = data[0]['SESSION']

        #调适配器600010
        reqdict["SESSION_JSON"] = jsonsession
        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['600010'](ref_request,'600010',reqdict)
        if iRet != '0':
            return SetErrMsg(ref_request,iRet,Msg,'','')
        if  len(RowsData) == 0:
            return SetErrMsg(ref_request,-3001,'登录成功但600010未返回第二结果集','adapt 600010未返回第二结果集','')
        adaptfundid = ''
        adaptcustid = ''
        adaptorgid = ''
        adaptcustname = ''
        adaptSECUSZA = ''
        adaptSECUSHA = ''
        adaptSECUSZB = ''
        adaptSECUSHB = ''
        adaptsysnode_id = None #恒生
        for eachrow in RowsData:
            if 'fundid' in eachrow:
                adaptfundid = eachrow['fundid']
            if 'custid' in eachrow:
                adaptcustid = eachrow['custid']
            if 'orgid' in eachrow:
                adaptorgid = eachrow['orgid']
            if 'custname' in eachrow:
                adaptcustname = eachrow['custname']
            if 'market' in eachrow:
                market = eachrow['market']
            if 'secuid' in eachrow:
                if market == '0':
                    adaptSECUSZA =  eachrow['secuid']
                elif market == '1':
                    adaptSECUSHA =  eachrow['secuid']
                elif market == '2':
                    adaptSECUSZB =  eachrow['secuid']
                elif market == '3':
                    adaptSECUSHB =  eachrow['secuid']
            if 'sysnode_id' in eachrow:
                adaptsysnode_id = eachrow['sysnode_id']

        if not adaptfundid  or not adaptcustid or not adaptorgid :
            return SetErrMsg(ref_request,-1003,'适配器600010接口返回字段fundid/custid/orgid某个值为空','实际上是登录succ','')

        IsNeedSyninfo = False #柜台数据有变动,是否需要同步到中台
        session_CUACCT_CODE = ''
        session_ACID = ''
        try:
            session_ACID = jsonsession['0'] #Acid
            session_CUST_ID = jsonsession['1'][0]['3']  #客户号
            session_CUACCT_CODE = jsonsession['1'][0]['4']  #资金帐号
            session_ORGID = jsonsession['1'][0]['5'] #营业部
            if 'LOGIN_CODE' in reqdict and  reqdict['USER_ID_CLS'] != '2':
                if reqdict['LOGIN_TYPE'] == '1' and reqdict['LOGIN_CODE'].find(adaptcustid) == -1 and adaptcustid != '':
                    return SetErrMsg(ref_request,-100,'输入的客户号%s与实际客户号不符'%(reqdict['LOGIN_CODE']),'','')
                #如果是子资金帐号 登录会返回主资金帐号,故与票据中的资金帐号对比即可
                elif reqdict['LOGIN_TYPE'] == '2' and session_CUACCT_CODE != ''  and reqdict['LOGIN_CODE'].find(session_CUACCT_CODE) == -1:
                    return SetErrMsg(ref_request,-100,'输入的资金账号%s与实际资金账号不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '3' and reqdict['LOGIN_CODE'].find(adaptSECUSZA) == -1 and adaptSECUSZA != '':
                    return SetErrMsg(ref_request,-100,'输入的深A股东%s与实际深A股东不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '4' and reqdict['LOGIN_CODE'].find(adaptSECUSZB) == -1 and adaptSECUSZB != '':
                    return SetErrMsg(ref_request,-100,'输入的深B股东%s与实际深B股东不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '5' and reqdict['LOGIN_CODE'].find(adaptSECUSHA) == -1 and adaptSECUSHA != '':
                    return SetErrMsg(ref_request,-100,'输入的上海A股东%s与实际上海A股东不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '6' and reqdict['LOGIN_CODE'].find(adaptSECUSHB) == -1 and adaptSECUSHB != '':
                    return SetErrMsg(ref_request,-100,'输入的上海B股东%s与实际上海B股东不符'%(reqdict['LOGIN_CODE']),'','')
            if session_ORGID != adaptorgid:
                strdebugmsg = '内部提示:客户号%s营业部代码发生变化,将进行同步!由%s修改成%s'%(adaptcustid,session_ORGID,adaptorgid)
                kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010', ZTLOG_ERROR, msgid='')
                IsNeedSyninfo = True
            if '8' not in jsonsession['1'][0]:
                #票据没有股东代码
                if adaptSECUSZA != '' or adaptSECUSHA != '':
                    strdebugmsg = '内部提示:客户号%s股东信息原本为空,将从券商同步股东信息!同步后的股东代码为%s,%s'%(adaptcustid,adaptSECUSZA,adaptSECUSHA)
                    kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010', ZTLOG_ERROR, msgid='')
                    IsNeedSyninfo = True
            elif len(jsonsession['1'][0]['8']) < 2:
                if adaptSECUSZA != '' and adaptSECUSHA != '':
                    strdebugmsg = '内部提示:客户号%s将从券商同步股东信息!同步后的股东代码为%s,%s'%(adaptcustid,adaptSECUSZA,adaptSECUSHA)
                    kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010', ZTLOG_ERROR, msgid='')
                    IsNeedSyninfo = True
            elif len(jsonsession['1'][0]['8']) == 2:
                for eachsecu in jsonsession['1'][0]['8']:
                    if eachsecu['9'] == '0':
                        if adaptSECUSZA != '' and  eachsecu['a'] != adaptSECUSZA:
                            strdebugmsg = '内部提示:客户号%s将从券商同步股东信息!深圳A股东代码由%s同步为%s.'%(adaptcustid,eachsecu['a'],adaptSECUSZA)
                            kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010', ZTLOG_DEBUG, msgid='')
                            IsNeedSyninfo = True
                    if eachsecu['9'] == '1':
                        if adaptSECUSHA != '' and  eachsecu['a'] != adaptSECUSHA:
                            strdebugmsg = '内部提示:客户号%s将从券商同步股东信息!上海A股东代码由%s同步为%s.'%(adaptcustid,eachsecu['a'],adaptSECUSHA)
                            kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010', ZTLOG_DEBUG, msgid='')
                            IsNeedSyninfo = True
            if 'sysnode_id' in jsonsession['1'][0] and adaptsysnode_id:
                if adaptsysnode_id != jsonsession['1'][0]['sysnode_id']:
                    reqdict['TYPE_VALUE'] = adaptsysnode_id
                    reqdict['TYPE_KEY'] = 'sysnode_id'
                    strdebugmsg = '内部提示:客户号%s将从券商同步恒生节点信息为%s.'%(adaptcustid,adaptsysnode_id)
                    kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010', ZTLOG_DEBUG, msgid='')
                    iRet,msg,data = T0005006_insertExinfo(reqdict)
                    if iRet != 0:
                        return SetErrMsg(ref_request,iRet,msg,'更新客户拓展信息时失败','')
        except Exception as e:
            return SetErrMsg(ref_request,-100,'内部错误:核对票据时,抛出异常:' + str(repr(e)),'','')
        if session_CUACCT_CODE != adaptfundid:
            IsNeedSyninfo = False #主子帐号问题暂不处理自动切换签约,也不能去做同步,判断优先级最高

        if not IsNeedSyninfo:
            #客户对应的票据,信息 #延长周期
            rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
            redisvalue = {}
            redisvalue['SESSION'] = session
            redisvalue['INFO'] = jsonsession
            setSessionRedis(rediskey, redisvalue, reqdict, session)
            return SetErrMsg(ref_request,iRet,'登录成功,无需更新客户数据','',session)
        else:
            #券商处有数据更新,重新调统一认证签约
            reqdict['USER_NAME'] = adaptcustname
            reqdict['ORG_CODE'] = adaptorgid
            reqdict['CUSTID'] = adaptcustid
            reqdict['ACCOUNT'] = adaptfundid
            reqdict['SECUSZA'] = adaptSECUSZA
            reqdict['SECUSZB'] = adaptSECUSZB
            reqdict['SECUSHA'] = adaptSECUSHA
            reqdict['SECUSHB'] = adaptSECUSHB
            reqdict['ID'] = '123'
            iRet,msg,data = T0005005_sign(reqdict)
            if iRet != 0:
                return SetErrMsg(ref_request,iRet,'登录成功,但'+msg,'','')
            session = data[0]['SESSION']
            jsonsession = data[0]['SESSION_JSON']
            #客户对应的票据,信息 #延长周期
            rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
            redisvalue = {}
            redisvalue['SESSION'] = session
            redisvalue['INFO'] = jsonsession
            setSessionRedis(rediskey, redisvalue, reqdict, session)
            return SetErrMsg(ref_request,iRet,'登录成功,更新客户中台数据成功','',session)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext), 'pyreqbpagent600010', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

def setOpStationRedis(reqdict, session):
    tcc = reqdict.get('TCC','')
    if tcc:
        op_station = kwlpyfunction.tranOpStation([tcc])
        op_red_key = kwlpyfunction.md5_encrypt(session)
        SetRedisKeyEx(op_red_key, op_station, g_redis_expiretime)

#华林-bpagent登录接口
async def pyreqbpagent600010_hl(ref_request,jsonkwlreq):
    #调统一认证T0005001取票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        reqdict['TRD_PWD'] = reqdict['USER_PWD']
        reqdict['SESSION_TYPE'] = 'Login'
        iRet,msg,data = T0005001_makesession(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        jsonsession = data[0]['SESSION_JSON']
        session = data[0]['SESSION']

        #调适配器600010
        reqdict["SESSION_JSON"] = jsonsession
        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['600010'](ref_request,'600010',reqdict)
        if iRet != '0':
            if iRet == '-63':
                match = re.search(r'v_password_errors=(\d+)', Msg)
                if match:
                    rest_attempts = int(match.group(1))
                    REST_NUM = str(15 - rest_attempts)
                    if REST_NUM in ['0','-1']:
                        return SetErrMsg(ref_request, '-61', '您的账号已冻结', '', '')
                    Msg = '密码错误，还剩 %s 次机会' % REST_NUM
                    return SetErrMsgAndBody(ref_request, iRet, Msg, '', session, [{'REST_NUM':REST_NUM}])
            return SetErrMsg(ref_request,iRet,Msg,'','')
        if  len(RowsData) == 0:
            return SetErrMsg(ref_request,-3001,'登录成功但600010未返回第二结果集','adapt 600010未返回第二结果集','')
        # 登录后，发送订阅消息到kafka
        await send_sub_kafka(jsonsession, ref_request)

        adaptfundid = ''
        adaptcustid = ''
        adaptorgid = ''
        adaptcustname = ''
        adaptSECUSZA = ''
        adaptSECUSHA = ''
        adaptSECUSZB = ''
        adaptSECUSHB = ''
        adaptSECUSGT = ''
        adaptSECUHGT = ''
        adaptsysnode_id = None #恒生
        for eachrow in RowsData:
            if 'fundid' in eachrow:
                adaptfundid = eachrow['fundid']
            if 'custid' in eachrow:
                adaptcustid = eachrow['custid']
            if 'orgid' in eachrow:
                adaptorgid = eachrow['orgid']
            if 'custname' in eachrow:
                adaptcustname = eachrow['custname']
            if 'market' in eachrow:
                market = eachrow['market']
            if 'secuid' in eachrow:
                if market == '0':
                    adaptSECUSZA =  eachrow['secuid']
                elif market == '1':
                    adaptSECUSHA =  eachrow['secuid']
                elif market == '2':
                    adaptSECUSZB =  eachrow['secuid']
                elif market == '3':
                    adaptSECUSHB =  eachrow['secuid']
                elif market == 'S':
                    adaptSECUSGT =  eachrow['secuid']
                elif market == '5':
                    adaptSECUHGT =  eachrow['secuid']
            if 'sysnode_id' in eachrow:
                adaptsysnode_id = eachrow['sysnode_id']

        if not adaptfundid  or not adaptcustid or not adaptorgid :
            return SetErrMsg(ref_request,-1003,'适配器600010接口返回字段fundid/custid/orgid某个值为空','但是登录succ','')

        IsNeedSyninfo = False #柜台数据有变动,是否需要同步到中台
        session_CUACCT_CODE = ''
        session_ACID = ''
        try:
            session_ACID = jsonsession['0'] #Acid
            session_CUST_ID = jsonsession['1'][0]['3']  #客户号
            session_CUACCT_CODE = jsonsession['1'][0]['4']  #资金帐号
            session_ORGID = jsonsession['1'][0]['5'] #营业部
            if 'LOGIN_CODE' in reqdict and  reqdict['USER_ID_CLS'] != '2':
                if reqdict['LOGIN_TYPE'] == '1' and reqdict['LOGIN_CODE'].find(adaptcustid) == -1 and adaptcustid != '':
                    return SetErrMsg(ref_request,-100,'输入的客户号%s与实际客户号不符'%(reqdict['LOGIN_CODE']),'','')
                #如果是子资金帐号 登录会返回主资金帐号,故与票据中的资金帐号对比即可
                elif reqdict['LOGIN_TYPE'] == '2' and session_CUACCT_CODE != ''  and reqdict['LOGIN_CODE'].find(session_CUACCT_CODE) == -1:
                    return SetErrMsg(ref_request,-100,'输入的资金账号%s与实际资金账号不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '3' and reqdict['LOGIN_CODE'].find(adaptSECUSZA) == -1 and adaptSECUSZA != '':
                    return SetErrMsg(ref_request,-100,'输入的深A股东%s与实际深A股东不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '4' and reqdict['LOGIN_CODE'].find(adaptSECUSZB) == -1 and adaptSECUSZB != '':
                    return SetErrMsg(ref_request,-100,'输入的深B股东%s与实际深B股东不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '5' and reqdict['LOGIN_CODE'].find(adaptSECUSHA) == -1 and adaptSECUSHA != '':
                    return SetErrMsg(ref_request,-100,'输入的上海A股东%s与实际上海A股东不符'%(reqdict['LOGIN_CODE']),'','')
                elif reqdict['LOGIN_TYPE'] == '6' and reqdict['LOGIN_CODE'].find(adaptSECUSHB) == -1 and adaptSECUSHB != '':
                    return SetErrMsg(ref_request,-100,'输入的上海B股东%s与实际上海B股东不符'%(reqdict['LOGIN_CODE']),'','')
            if session_ORGID != adaptorgid:
                strdebugmsg = '内部提示:客户号%s营业部代码发生变化,将进行同步!由%s修改成%s'%(adaptcustid,session_ORGID,adaptorgid)
                kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010_hl', ZTLOG_DEBUG, msgid='')
                IsNeedSyninfo = True
            if '8' not in jsonsession['1'][0]:
                #票据没有股东代码
                if adaptSECUSZA != '' or adaptSECUSHA != '':
                    strdebugmsg = '内部提示:客户号%s股东信息原本为空,将从券商同步股东信息!同步后的股东代码为%s,%s'%(adaptcustid,adaptSECUSZA,adaptSECUSHA)
                    kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010_hl', ZTLOG_DEBUG, msgid='')
                    IsNeedSyninfo = True
            elif len(jsonsession['1'][0]['8']) < 2:
                if adaptSECUSZA != '' and adaptSECUSHA != '':
                    strdebugmsg = '内部提示:客户号%s将从券商同步股东信息!同步后的股东代码为%s,%s'%(adaptcustid,adaptSECUSZA,adaptSECUSHA)
                    kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010_hl', ZTLOG_DEBUG, msgid='')
                    IsNeedSyninfo = True
            elif len(jsonsession['1'][0]['8']) == 2:
                for eachsecu in jsonsession['1'][0]['8']:
                    if eachsecu['9'] == '0':
                        if adaptSECUSZA != '' and  eachsecu['a'] != adaptSECUSZA:
                            strdebugmsg = '内部提示:客户号%s将从券商同步股东信息!深圳A股东代码由%s同步为%s.'%(adaptcustid,eachsecu['a'],adaptSECUSZA)
                            kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010_hl', ZTLOG_DEBUG, msgid='')
                            IsNeedSyninfo = True
                    if eachsecu['9'] == '1':
                        if adaptSECUSHA != '' and  eachsecu['a'] != adaptSECUSHA:
                            strdebugmsg = '内部提示:客户号%s将从券商同步股东信息!上海A股东代码由%s同步为%s.'%(adaptcustid,eachsecu['a'],adaptSECUSHA)
                            kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010_hl', ZTLOG_DEBUG, msgid='')
                            IsNeedSyninfo = True
            if 'sysnode_id' in jsonsession['1'][0] and adaptsysnode_id:
                if adaptsysnode_id != jsonsession['1'][0]['sysnode_id']:
                    reqdict['TYPE_VALUE'] = adaptsysnode_id
                    reqdict['TYPE_KEY'] = 'sysnode_id'
                    strdebugmsg = '内部提示:客户号%s将从券商同步恒生节点信息为%s.'%(adaptcustid,adaptsysnode_id)
                    kwl_py_write_log(strdebugmsg, 'pyreqbpagent600010_hl', ZTLOG_DEBUG, msgid='')
                    iRet,msg,data = T0005006_insertExinfo(reqdict)
                    if iRet != 0:
                        return SetErrMsg(ref_request,iRet,msg,'更新客户拓展信息时失败','')
        except Exception as e:
            return SetErrMsg(ref_request,-100,'内部错误:核对票据时,抛出异常:' + str(repr(e)),'','')
        if session_CUACCT_CODE != adaptfundid:
            IsNeedSyninfo = False #主子帐号问题暂不处理自动切换签约,也不能去做同步,判断优先级最高

        if not IsNeedSyninfo:
            #客户对应的票据,信息 #延长周期
            rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
            redisvalue = {}
            redisvalue['SESSION'] = session
            redisvalue['INFO'] = jsonsession
            setSessionRedis(rediskey, redisvalue, reqdict, session)
            return SetErrMsg(ref_request,iRet,'登录成功,无需更新客户数据','',session)
        else:
            #券商处有数据更新,重新调统一认证签约
            reqdict['USER_ID_TYPE'] = reqdict['USER_ID_CLS']
            reqdict['USER_ID_INFO'] = reqdict['USER_ID']
            reqdict['USER_ID'] = session_ACID
            reqdict['USER_NAME'] = adaptcustname
            reqdict['ORG_CODE'] = adaptorgid
            reqdict['USER_CODE'] = adaptcustid
            reqdict['CUACCT_CODE'] = adaptfundid
            reqdict['SECUSZA'] = adaptSECUSZA
            reqdict['SECUSZB'] = adaptSECUSZB
            reqdict['SECUSHA'] = adaptSECUSHA
            reqdict['SECUSHB'] = adaptSECUSHB
            reqdict['SECUSGT'] = adaptSECUSGT
            reqdict['SECUHGT'] = adaptSECUHGT
            reqdict['SECUSZJ'] = 'test'
            reqdict['SECUSHJ'] = 'test'
            reqdict['ID'] = '123'
            iRet,msg,data = T0005005_sign_hlzq(reqdict)
            if iRet != 0:
                return SetErrMsg(ref_request,iRet,'登录成功,但'+msg,'','')
            session = data[0]['SESSION']
            jsonsession = data[0]['SESSION_JSON']
            #客户对应的票据,信息 #延长周期
            rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID_INFO']
            redisvalue = {}
            redisvalue['SESSION'] = session
            redisvalue['INFO'] = jsonsession
            setSessionRedis(rediskey, redisvalue, reqdict, session)
            return SetErrMsg(ref_request,iRet,'登录成功,更新客户中台数据成功','',session)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext), 'pyreqbpagent600010_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


#重置/修改密码接口
async def pyreqbpagent900010_hl(ref_request,jsonkwlreq):
    #调统一认证T0005001取票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']

        iRet,msg,data = T0005001_makesession(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        jsonsession = data[0]['SESSION_JSON']
        session = data[0]['SESSION']

        #客户对应的票据,信息 #删除SESSION 信息
        rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
        redisvalue = {}
        redisvalue['SESSION'] = session
        redisvalue['INFO'] = jsonsession
        var = GetRedisKey(rediskey)
        if var == None:
            return SetErrMsg(ref_request, -177, '票据已过期,请重新登录', '', '')
     
        if var:
            DeleteRedisKey(rediskey)
        return SetErrMsg(ref_request,iRet,'重置/修改成功,请重新登录更新客户数据','',session)

    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext), 'pyreqbpagent900010_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return



#华林-bpagent资金密码登录接口
async def pyreqbpagent600011_hl(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        reqdict['TRD_PWD'] = reqdict['USER_PWD'] #票据交易密码填空
        reqdict['SESSION_TYPE'] = 'Login'
        #调统一认证T0005001取票据
        iRet,msg,data = T0005001_makesession(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        jsonsession = data[0]['SESSION_JSON']
        session = data[0]['SESSION']

        #调适配器600011
        reqdict["SESSION_JSON"] = jsonsession
        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['600011'](ref_request,'600011',reqdict)
        if iRet != '0':
            if iRet == '-61':
                if '120039' in Msg:
                    return SetErrMsg(ref_request, '-63', '您的账号已冻结', '', '')

                match = re.search(r'v_password_errors=(\d+)', Msg)
                if match:
                    rest_attempts = int(match.group(1))
                    REST_NUM = str(15 - rest_attempts)
                    if REST_NUM in ['0','-1']:
                        return SetErrMsg(ref_request, '-63', '您的账号已冻结', '', '')
                    Msg = '密码错误，还剩 %s 次机会' % REST_NUM
                    return SetErrMsgAndBody(ref_request, iRet, Msg, '', session, [{'REST_NUM':REST_NUM}])
            return SetErrMsg(ref_request,iRet,Msg,'','')

        return SetErrMsgAndBody(ref_request,iRet,'sucess','',session,[])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent600011_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#用户信息查询接口600040新 DH eidt
#在pyreqbpagentCommquery的基础上对-61的iRet情况增加返回值STATUS
async def pyreqbpagent600040(ref_request,jsonkwlreq):
    # 票据在登录、签约、带票据请求时才会续
    try:
        SERVICE_ID = jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']['SERVICE_ID']
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        NeedCheckSession = False
        if (g_CheckSession['CHECKFLAG'] == True) and (SERVICE_ID in g_CheckSession['CKLBM']):  # 校验开关为开,且必须校验票据
            NeedCheckSession = True
            if 'SESSION' not in reqdict or reqdict['SESSION'] == '000000':
                return SetErrMsg(ref_request, -100, '该接口需要送登录票据', '', '')

        jsonsession = None
        session = ''

        if SERVICE_ID in g_NoUSERIDCLS:
            reqdict['USER_ID_CLS'] = '1'

        if SERVICE_ID not in g_QueryStockInfo:
            # 客户对应的票据,信息
            rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
            redisvalue = GetRedisKey(rediskey)
            if NeedCheckSession == True:
                # 从redis校验票据
                if redisvalue == None:
                    return SetErrMsg(ref_request, -177, '票据已过期,请重新登录', '', '')
                redisvalue = eval(redisvalue)
                if redisvalue['SESSION'] != reqdict['SESSION']:
                    return SetErrMsg(ref_request, -177, '票据校验失败,登录已过期', '登录过期或送入票据非该客户登录票据', '')
                session = redisvalue['SESSION']
                op_red_key = kwlpyfunction.md5_encrypt(session)
                expireSessionRedis(rediskey, op_red_key)
                jsonsession = redisvalue['INFO']
            else:
                if SERVICE_ID in g_NoSession:  # 如果是不需要补session的功能号，则直接赋值为空
                    jsonsession = ''
                # 如果是800011接口，且入参['BIZ_TYPE']的值为'6'时，则不需要补Session
                elif SERVICE_ID == '800011' and reqdict['BIZ_TYPE'] == '6':
                    jsonsession = ''
                # 如果是800012接口，且入参['BIZ_TYPE']的值为'6'时，则不需要补Session
                elif SERVICE_ID == '800012' and reqdict['BIZ_TYPE'] == '6':
                    jsonsession = ''
                # 6个0则补票据
                # 先从redis取票据
                elif redisvalue != None:
                    redisvalue = eval(redisvalue)
                    session = '000000'  # 没登录干嘛返回票据
                    jsonsession = redisvalue['INFO']
                else:
                    iRet, msg, data = T0005001_makesession(reqdict)
                    if iRet != 0:
                        return SetErrMsg(ref_request, iRet, msg, '', '')
                    jsonsession = data[0]['SESSION_JSON']
                    session = data[0]['SESSION']
                    # 缓存非登录票据,因为此时必定无登录票据#延长周期
                    redisvalue = {}
                    redisvalue['SESSION'] = session
                    redisvalue['INFO'] = jsonsession
                    setSessionRedis(rediskey, redisvalue, reqdict, session)

        # 调适配器接口
        adaptserviceid = g_BpagentreqCommqueryCallway[jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']['SERVICE_ID']][0]
        reqdict["SESSION_JSON"] = jsonsession
        iRet, Msg, RowsData = await adaptspd.g_reqadaptBizFunctionDict[adaptserviceid](ref_request, adaptserviceid,
                                                                                       reqdict)
        if iRet != '0':
            if iRet == '-61':
                iRet = 0
                #查询柜台返回的client_status
                idx = Msg.find('client_status')
                if idx > -1:
                    #对client_status进行截取
                    client_status = Msg[idx+14:idx+15]
                    #字典转换
                    status = (dict_client_status_TO_WZQ[client_status])
                    ansstatusdict = []
                    onestatusrow = {'STATUS': status, 'ID_TYPE': '00'}
                    ansstatusdict.append(onestatusrow)
                    #封装并返回
                    return SetErrMsgAndBody(ref_request, iRet, Msg, 'CallProgram ' + adaptserviceid + ' Failed', '', ansstatusdict)
            iRet = 0
            return SetErrMsg(ref_request, iRet, Msg, 'CallProgram ' + adaptserviceid + ' Failed', '')

        # 空包的判断
        RowsData = kwlpyfunction.judge_return_null(adaptserviceid, RowsData)

        if len(RowsData) == 0:
            return SetErrMsg(ref_request, 0, Msg, '', session)

        if g_BpagentreqCommqueryCallway[SERVICE_ID][1] == False:
            return SetErrMsgAndBody(ref_request, iRet, Msg, '', session, RowsData)
        # 根据bpagentini控制出参
        anscontoldict = []
        for each_row in RowsData:
            onecontolrow = {}
            for eachkey in g_BpagentreqCommqueryCallway[SERVICE_ID][2]:  # 遍历配置的出参
                if eachkey not in each_row:
                    onecontolrow[eachkey] = ''
                    continue
                onecontolrow[eachkey] = each_row[eachkey]
            anscontoldict.append(onecontolrow)

        return SetErrMsgAndBody(ref_request, iRet, Msg, '', session, anscontoldict)
    except Exception as e:
        anstext = MakeAnsStr(-1001, "nginx异常:" + str(repr(e)), "", "")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagentCommquery', ZTLOG_ERROR,
                         msgid='')
        SendResponse(ref_request, anstext, len(anstext))
        # 这些异常都是我们关注的
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "bpagentlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#测试接口
async def pyreqtest(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        session = reqdict['SESSION']
        strjsonsession =session_deskey.decrypt(binascii.unhexlify(session)).decode('utf-8')
        if strjsonsession == '':
            return SetErrMsg(ref_request,-100,'票据校验失败,解压失败','','')
        return SetErrMsg(ref_request,0,'票据解压成功',strjsonsession,'')
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqtest', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


# 发送消息到kafka,kafka消费者再进行订阅
async def send_sub_kafka(jsonsession, ref_request):
    try:
        strdate = time.strftime("%Y%m%d")
        strtime = time.strftime("%H:%M:%S")
        send_data_dict = {"DATE": strdate, "TIME": strtime}

        send_data_dict["USER_ID"] = jsonsession['user_id']  # 腾讯的用户ID
        send_data_dict["ACCOUNT"] = jsonsession['1'][0]['4']  # 资金账号
        send_data_dict["BRANCH"] = jsonsession['1'][0]['5']  # 营业部代码
        sub_topic_list = g_kafka_config['sub_topics']
        for topic in sub_topic_list:
            await kafka_work.send_kafka(topic, json.dumps(send_data_dict))

    except Exception as e:
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + str(repr(e))), 'send_sub_kafka', ZTLOG_ERROR, msgid='')


#同步-bpagent普通接口
async def pyreqbpagentCommquery(ref_request,jsonkwlreq):
    #票据在登录、签约、带票据请求时才会续
    try:
        SERVICE_ID = jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']['SERVICE_ID']
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        NeedCheckSession = False
        if (g_CheckSession['CHECKFLAG'] == True) and (SERVICE_ID  in g_CheckSession['CKLBM']): #校验开关为开,且必须校验票据
            NeedCheckSession = True
            if 'SESSION' not in reqdict or reqdict['SESSION'] == '000000':
                return SetErrMsg(ref_request,-100,'该接口需要送登录票据','','')

        jsonsession = None
        session = ''

        if SERVICE_ID in g_NoUSERIDCLS:
            reqdict['USER_ID_CLS'] = '1'

        if SERVICE_ID not in g_QueryStockInfo:
            #客户对应的票据,信息
            rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
            redisvalue = GetRedisKey(rediskey)
            if NeedCheckSession == True:
                #从redis校验票据
                if redisvalue == None:
                    return SetErrMsg(ref_request,-177,'票据已过期,请重新登录','','')
                redisvalue = eval(redisvalue)
                if redisvalue['SESSION'] != reqdict['SESSION']:
                    return SetErrMsg(ref_request,-177,'票据校验失败,登录已过期','登录过期或送入票据非该客户登录票据','')
                session = redisvalue['SESSION']
                op_red_key = kwlpyfunction.md5_encrypt(session)
                expireSessionRedis(rediskey, op_red_key)
                jsonsession = redisvalue['INFO']
            else:
                if SERVICE_ID in g_NoSession:  # 如果是不需要补session的功能号，则直接赋值为空
                    jsonsession = ''
                #如果是800011接口，且入参['BIZ_TYPE']的值为'6'时，则不需要补Session
                elif SERVICE_ID == '800011' and reqdict['BIZ_TYPE'] == '6':
                    jsonsession = ''
                #如果是800012接口，且入参['BIZ_TYPE']的值为'6'时，则不需要补Session
                elif SERVICE_ID == '800012' and reqdict['BIZ_TYPE'] == '6':
                    jsonsession = ''
                #6个0则补票据
                #先从redis取票据
                elif redisvalue != None:
                    redisvalue = eval(redisvalue)
                    session = '000000' #没登录干嘛返回票据
                    jsonsession = redisvalue['INFO']
                else:
                    iRet,msg,data = T0005001_makesession(reqdict)
                    if iRet != 0:
                        return SetErrMsg(ref_request,iRet,msg,'','')
                    jsonsession = data[0]['SESSION_JSON']
                    session = data[0]['SESSION']
                    #缓存非登录票据,因为此时必定无登录票据#延长周期
                    redisvalue = {}
                    redisvalue['SESSION'] = session
                    redisvalue['INFO'] = jsonsession
                    setSessionRedis(rediskey, redisvalue, reqdict, session)

        #调适配器接口
        adaptserviceid = g_BpagentreqCommqueryCallway[jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']['SERVICE_ID']][0]
        reqdict["SESSION_JSON"] = jsonsession
        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict[adaptserviceid](ref_request,adaptserviceid,reqdict)
        if iRet != '0':
            return SetErrMsg(ref_request,iRet,Msg,'CallProgram ' + adaptserviceid + ' Failed' ,'')

        # 空包的判断
        RowsData = kwlpyfunction.judge_return_null(adaptserviceid, RowsData)

        if len(RowsData) == 0:
             return SetErrMsg(ref_request,0,Msg,'',session)

        if g_BpagentreqCommqueryCallway[SERVICE_ID][1] == False:
            return SetErrMsgAndBody(ref_request,iRet,Msg,'',session,RowsData)
        #根据bpagentini控制出参
        anscontoldict = []
        for each_row in RowsData:
            onecontolrow = {}
            for eachkey in g_BpagentreqCommqueryCallway[SERVICE_ID][2]: #遍历配置的出参
                if eachkey not in each_row:
                    onecontolrow[eachkey] = ''
                    continue
                onecontolrow[eachkey] = each_row[eachkey]
            anscontoldict.append(onecontolrow)

        return SetErrMsgAndBody(ref_request,iRet,Msg,'',session,anscontoldict)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext), 'pyreqbpagentCommquery', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#600401延长票据
async def pyrlengthenSession(ref_request,jsonkwlreq):
    try:
        SERVICE_ID = jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']['SERVICE_ID']
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']

        redisvalue = {}
        redisvalue['SESSION'] = reqdict['SESSION']
        jsonsession = session_decrypt(reqdict['SESSION'], g_session_des_key)
        redisvalue['INFO'] = eval(jsonsession)

        rediskey = '1' + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
        setSessionRedis(rediskey, redisvalue, reqdict, redisvalue['SESSION'])

        return SetErrMsgAndBody(ref_request, '0', '延长票据成功', '', reqdict['SESSION'], [])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyrlengthenSession', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


#同步bpagent非组合接口-普通解约
async def pyreqbpagent600002(ref_request,jsonkwlreq):
     #校验票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        if  'SESSION' not in reqdict or reqdict['SESSION'] == '000000':
            return SetErrMsg(ref_request,-100,'解约SESSION输入不合法','','')
         #客户对应的票据,信息
        rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
        redisvalue = GetRedisKey(rediskey)
        if redisvalue == None:
             return SetErrMsg(ref_request,-177,'票据校验失败,登录已过期','登录过期或送入票据非该客户登录票据','')
        redisvalue = eval(redisvalue)
        if redisvalue['SESSION'] != reqdict['SESSION']:
            return SetErrMsg(ref_request,-177,'票据校验失败,登录已过期','登录过期或送入票据非该客户登录票据','')
        reqdict['SESSION_JSON'] = redisvalue['INFO']
        iRet,msg,data = T0005009_unsign(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsg(ref_request,0,'解约成功','','')
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent600002', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#同步bpagent非组合接口-强制解约
async def pyreqbpagent60000201(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        now = time.time()
        if abs(now - int(reqdict['TIME_STAMP'])) > 1800:
            return SetErrMsg(ref_request,-100,'解绑时间戳过期,请重新登录','','')
        if reqdict['OPER_USER'] != 'kbss_kcas':
             return SetErrMsg(ref_request,-100,'解绑操作员非法','','')
        iRet,msg,data = T0005009_unsign(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsg(ref_request,0,'解约成功','','')
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent60000201', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#华林微证券签约接口（老架构）
async def pyreqbpagent600001_hl(ref_request,jsonkwlreq):
     #判断客户是否已经签约，若已经签约则不让客户重复签约
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        #判断该资金账号是否已经签约了，签约了则不能重复签约
        iRet, msg, data = isExistInTyrzDb(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request, iRet, msg, '', '')
        tmp = await pyreqSimpleZX(ref_request,jsonkwlreq)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent600001_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#华林微证券签约接口（新架构）
async def newpyreqbpagent600001_hl(ref_request,jsonkwlreq):
     #判断客户是否已经签约，若已经签约则不让客户重复签约
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        #判断该资金账号是否已经签约了，签约了则不能重复签约
        iRet, msg, data = isExistInTyrzDb(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request, iRet, msg, '', '')

        #tmp = await pyreqSimpleZX(ref_request,jsonkwlreq)
        '''签约先校验核身验证码凭据（短信验证）'''
        # strUserCode = reqdict.get('LOGIN_CODE','')
        strUserId = reqdict.get('USER_ID','')
        strXidSession = reqdict.get('XID_SESSION','')
        if strXidSession != '' :
            nRet, msg = CheckXIDSessionString(strUserId, strXidSession)
            if nRet != '0' :
                return SetErrMsg(ref_request, nRet, "签约失败：" + msg, '', '')

        '''客户签约'''
        session = await pyreqbpagent600001(ref_request,jsonkwlreq)

        '''查网厅开户方式'''
        # reqdict['USER_CODE'] = strUserCode
        # reqdict['USER_ROLE'] = '1'
        # reqdict['OP_TYPE'] = '1'
        # iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['99000270'](ref_request,'99000270',reqdict)
        # if iRet != '0':
        #     return SetErrMsg(ref_request,iRet,Msg,'','')
        # for eachrow in RowsData:
        #     list_source = ['0', '1', '2', '3', '5']
        #     #/OPEN_SOURCE 为 0 1 2 3 5 传统渠道开户方式
        #     if eachrow['OPEN_SOURCE'] in list_source :
        #         eachrow['OPEN_SOURCE'] = '0'
        #     else:
        #         eachrow['OPEN_SOURCE'] = '1'
        # return SetErrMsgAndBody(ref_request, iRet, 'success', '', session, RowsData)


    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'newpyreqbpagent600001_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#华林同步-bpagent签约（通用）
async def LbmCustSignAgent(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        reqdict['USER_ID_TYPE'] = reqdict['USER_ID_CLS']
        reqdict['USER_ID_INFO'] = reqdict['USER_ID']
        reqdict['inputid'] = reqdict['LOGIN_CODE']
        reqdict['inputtype'] = g_dict_logintype[reqdict['LOGIN_TYPE']]
        reqdict['trdpwd'] = reqdict['TRD_PWD']

        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['410301'](ref_request,'410301',reqdict)
        if iRet != '0':
            return SetErrMsg(ref_request,iRet,Msg,'','')
        if len(RowsData) == 0:
            return SetErrMsg(ref_request,-100,'410301接口未返回第二结果集','','')
        SECUSZA = ''
        SECUSHA = ''
        SECUSZB = ''
        SECUSHB = ''
        SECUSGT = ''
        SECUHGT = ''
        fundid = ''
        custid = ''
        orgid = ''
        custname = ''
        for eachrow in RowsData:
            fundid = eachrow['fundid']
            custid = eachrow['custid']
            orgid = eachrow['orgid']
            custname = eachrow['custname']
            market = eachrow['market']
            if market == '0':
                SECUSZA =  eachrow['secuid']
            elif market == '1':
                SECUSHA =  eachrow['secuid']
            elif market == '2':
                SECUSZB =  eachrow['secuid']
            elif market == '3':
                SECUSHB =  eachrow['secuid']
            elif market == 'S':
                SECUSGT =  eachrow['secuid']
            elif market == '5':
                SECUHGT =  eachrow['secuid']

        #调用适配器的 KOW001 客户绑定接口，返回应答  added by fusq 2018/01/18
        reqdict['BRANCH_CODE'] = orgid
        reqdict['CUST_CODE'] = custid
        reqdict['FUND_CODE'] = fundid
        reqdict['SECUSZA'] = SECUSZA
        reqdict['SECUSZB'] = SECUSZB
        reqdict['SECUSHA'] = SECUSHA
        reqdict['SECUSHB'] = SECUSHB
        reqdict['SECUSGT'] = SECUSGT
        reqdict['SECUHGT'] = SECUHGT
        iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['KOW001'](ref_request,'KOW001',reqdict)
        if iRet != '0':
            return SetErrMsg(ref_request,iRet,Msg,'','')
        for eachrow in RowsData:
            CUST_ID = eachrow['CUST_ID']

        #调统一认证T0005005
        reqdict["USER_ID"] = CUST_ID
        #reqdict['USER_ID_TYPE'] = reqdict['USER_ID_CLS']  #注释的前面入参已经赋值
        #reqdict['USER_ID_INFO'] = reqdict['USER_ID']
        reqdict["ID"] = '123'
        reqdict["USER_NAME"] = custname
        reqdict["ORG_CODE"] = orgid
        reqdict["USER_CODE"] = custid   # 券商客户号
        reqdict["CUACCT_CODE"] = fundid  #资产帐号
        #reqdict["SECUSZA"] = SECUSZA
        #reqdict["SECUSZB"] = SECUSZB
        #reqdict["SECUSHA"] = SECUSHA
        #reqdict["SECUSHB"] = SECUSHB
        #reqdict['SECUSGT'] = SECUSGT
        #reqdict['SECUHGT'] = SECUHGT
        reqdict['SECUSZJ'] = 'test'
        reqdict['SECUSHJ'] = 'test'
        reqdict["BANKINFO"] = ""
        reqdict["SIGN_ACC_TYPE"] = reqdict['LOGIN_TYPE']

        iRet,msg,data = T0005005_sign_hlzq(reqdict)
        if iRet != 0:
            reqdict['CUSI_ID'] = CUST_ID
            nRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['KOW002'](ref_request,'KOW002',reqdict)
            return SetErrMsg(ref_request,iRet,msg,'','')

        #客户对应的票据,信息  #延长周期
        rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID_INFO']
        redisvalue = {}
        #密文票据和字典票据
        redisvalue['SESSION'] = data[0]['SESSION']
        redisvalue['INFO'] = data[0]['SESSION_JSON']
        setSessionRedis(rediskey, redisvalue, reqdict, redisvalue['SESSION'])
        #rediscl.set(encryptsession,DataAcid)   #票据对应的客户
        #rediscl.expire(encryptsession,g_redis_expiretime)  #延长周期
        return data[0]['SESSION']
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'LbmCustSignAgent', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


#华林解约接口（老架构）
async def pyreqbpagent600002_hl(ref_request,jsonkwlreq):
     #校验票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        # 获取USER_ID
        userId = ''
        if 'USER_ID' not in reqdict:
            return SetErrMsg(ref_request, -100, '入参有误：必要的入参[USER_ID]不存在', '', '')
        userId = reqdict['USER_ID']
        if len(userId) == 0:
            return SetErrMsg(ref_request, -200, '入参有误：USER_ID不能为空值', '', '')
        # 将客户对应的票据,信息删除
        rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
        DeleteRedisKey(rediskey)

        # 判断客户的资金账号绑定有多少微信号
        bandnum = bandNum(userId)
        if bandnum > 1:
            #如果大于1条，则从数据路删除这一条签约的UserId
            iRet, msg, data = deleteDbUserID(userId)
            return SetErrMsg(ref_request, iRet, msg, '', '')
        else:
            tmp = await pyreqSimpleZX(ref_request,jsonkwlreq)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent600002_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


#华林解约接口(新架构)
async def newpyreqbpagent600002_hl(ref_request,jsonkwlreq):
     #校验票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        # 获取USER_ID
        userId = ''
        if 'USER_ID' not in reqdict:
            return SetErrMsg(ref_request, -100, '入参有误：必要的入参[USER_ID]不存在', '', '')
        userId = reqdict['USER_ID']
        if len(userId) == 0:
            return SetErrMsg(ref_request, -200, '入参有误：USER_ID不能为空值', '', '')
        # 将客户对应的票据,信息删除
        rediskey = reqdict['USER_ID_CLS'] + str(reqdict['COMPANY_ID']) + reqdict['USER_ID']
        DeleteRedisKey(rediskey)

        # 判断客户的资金账号绑定有多少微信号
        bandnum = bandNum(userId)
        if bandnum > 1:
            #如果大于1条，则从数据路删除这一条签约的UserId
            iRet, msg, data = deleteDbUserID(userId)
            return SetErrMsg(ref_request, iRet, msg, '', '')
        else:
            tmp = await LbmUnSignNoSession(ref_request,jsonkwlreq)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'newpyreqbpagent600002_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


#国金重绑留痕接口（自选股前端退出登陆后重绑留痕，重绑没调600001接口，所有加接口做退出重登留痕）
async def newpyreqbpagent600003_hl(ref_request,jsonkwlreq):
     #校验票据
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        # 获取USER_ID
        userId = ''
        if 'USER_ID' not in reqdict:
            return SetErrMsg(ref_request, -100, '入参有误：必要的入参[USER_ID]不存在', '', '')
        userId = reqdict['USER_ID']
        if len(userId) == 0:
            return SetErrMsg(ref_request, -200, '入参有误：USER_ID不能为空值', '', '')

        # 签约留痕，签署免密协议和免责协议 （add by fusq  2020 0224）
        code, msg, ans = user_sign_mark(reqdict)
        if code != 0:
            return SetErrMsg(ref_request, code, msg, '', '')
        return SetErrMsg(ref_request, code, 'success', '', '')
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'newpyreqbpagent600003_hl', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

async def LbmUnSignNoSession(ref_request,jsonkwlreq):
    try:
        #调用适配器的 KOW002
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        # iRet,Msg,RowsData = await adaptspd.g_reqadaptBizFunctionDict['KOW002'](ref_request,'KOW002',reqdict)
        # if iRet != '0':
        #     return SetErrMsg(ref_request,iRet,Msg,'','')

        #调统一认证T0005009
        iRet,msg,data = T0005009_unsign_hlzq(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')

        return SetErrMsg(ref_request,iRet,'success','','')

    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'LbmUnSignNoSession', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#同步bpagent非组合接口-查询客户签约信息
async def pyreqbpagent600003(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet,msg,data = T0005010_querrysigninfo(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent600003', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#查询客户最新签约信息
async def pyreqbpagent610003(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet,msg,data = T0005020_querrylastsigninfo(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqbpagent610003', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#查询某银行银证业务是否需要密码信息600092
async def pyreqSelectBankPwdInfo(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet,msg,data = SelectBankPwdInfo(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqSelectBankPwdInfo', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#券商公告入库600090
async def pyreqInsertNotice(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet,msg,data = InsertNotice(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqInsertNotice', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#券商公告查询600091
async def pyreqSelectNotice(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet,msg,data = SelectNotice(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqSelectNotice', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#查询是否交易日
async def pyreqCheckIsTradeTime(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        maket = None
        curtime = None
        IS_TRADE_DAY = '0'
        IS_MARKET_TRADE_TIME = '0'
        if 'MARKET' not in  reqdict:
            maket = '0'
        else:
            maket = reqdict['MARKET']
        if 'CUR_TIME' not in reqdict:
            curtime = time.strftime( '%Y%m%d%H%M%S',  time.localtime() )
        else:
            curtime = reqdict['CUR_TIME']
        if len(curtime) != 14:
            return SetErrMsg(ref_request,-100,'请求CUR_TIME非法,格式为YYYYMMDDhhmmss','','')
        if maket not in NginxConfig.g_Market_TradeTime:
            return SetErrMsg(ref_request,-100,'无此市场的交易时间配置:'+ maket,'','')

        timehhmmss = curtime[8:]
        #先看有没有特殊的交易日设置
        for each in NginxConfig.g_Trade_Date:
            if curtime >= each[0] and curtime <= each[1]:
                #为特殊交易日,再判断时间
                if (timehhmmss >= NginxConfig.g_Market_TradeTime[maket]['beginAM'] and timehhmmss <= NginxConfig.g_Market_TradeTime[maket]['endAM'])  or (timehhmmss >= NginxConfig.g_Market_TradeTime[maket]['beginPM'] and timehhmmss <= NginxConfig[maket].g_Market_TradeTime['endPM']):
                    return SetErrMsgAndBody(ref_request,0,'succ','','',[{'IS_TRADE_DAY':'1','IS_MARKET_TRADE_TIME':'1','REMARK':each[2]}])

        #不在特殊交易日,,判断是周几
        weekday = datetime.datetime.strptime(curtime[0:8],'%Y%m%d').weekday()
        if weekday == 5 or weekday == 6:
            #周六日休市
            return SetErrMsgAndBody(ref_request,0,'succ','','',[{'IS_TRADE_DAY':'0','IS_MARKET_TRADE_TIME':'0'}])

        #再看特殊休市日期设置
        for each in NginxConfig.g_Rest_Date:
            if curtime >= each[0] and curtime <= each[1]:
                #休市
                return SetErrMsgAndBody(ref_request,0,'succ','','',[{'IS_TRADE_DAY':'0','IS_MARKET_TRADE_TIME':'0','REMARK':each[2]}])

        #已在交易日判断市场时间
        if (timehhmmss >= NginxConfig.g_Market_TradeTime[maket]['beginAM'] and timehhmmss <= NginxConfig.g_Market_TradeTime[maket]['endAM'])  or (timehhmmss >= NginxConfig.g_Market_TradeTime[maket]['beginPM'] and timehhmmss <= NginxConfig.g_Market_TradeTime[maket]['endPM']):
            return SetErrMsgAndBody(ref_request,0,'succ','','',[{'IS_TRADE_DAY':'1','IS_MARKET_TRADE_TIME':'1'}])
        else:
            return SetErrMsgAndBody(ref_request,0,'succ','','',[{'IS_TRADE_DAY':'1','IS_MARKET_TRADE_TIME':'0'}])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqCheckIsTradeTime', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#返回客户ip地址
async def pyreqgetclientip(ref_request,jsonkwlreq):
    try:
        #调的nginx里面导出的方法
        ip = GetClientIp(ref_request)
        if not ip:
            return SetErrMsg(ref_request,-1001,'获取ip为空','','')
        return SetErrMsgAndBody(ref_request,0,'succ','','',[{'IP':ip}])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqgetclientip', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#查看nginx变量情况
async def pyreqgetNginxInfo(ref_request,jsonkwlreq):
    try:
        global g_dictZXAns
        ZXanslen = len(g_dictZXAns)
        return SetErrMsgAndBody(ref_request,0,'succ','','',[{'g_dictZXAns':ZXanslen}])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqgetNginxInfo', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#清空
async def pyreqClearNginxXpAns(ref_request,jsonkwlreq):
    try:
        global g_dictZXAns,g_ZXdictlock
        ZXanslen = len(g_dictZXAns)
        g_ZXdictlock.acquire()
        g_dictZXAns.clear()
        g_ZXdictlock.release()
        return SetErrMsgAndBody(ref_request,0,'清理成功','','',[{'g_dictZXAns':ZXanslen}])
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqClearNginxXpAns', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

#调总线流程
async def pyreqSimpleZX(ref_request,jsonkwlreq):
    try:
        anstext = await pyreqSimpleZX_ct(ref_request,jsonkwlreq)
        SendResponse(ref_request, anstext,len(anstext))
        kwl_py_write_log('%s应答串:%s'%(ref_request,anstext), 'pyreqSimpleZX', ZTLOG_INFO, msgid='')
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqSimpleZX', ZTLOG_ERROR, msgid='')
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        SendResponse(ref_request, anstext,len(anstext))
    return

# 查询客户号 400001 测试环境专用
async def pyreqQueryAccount(ref_request, jsonkwlreq):
    try:
        if g_environment != 'test':
            return SetErrMsg(ref_request, -100, '非测试环境不支持调用测试接口', '', '')
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet, msg, data = queryAccount(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request, iRet, msg, '', '')
        if len(data) == 0:
            return SetErrMsg(ref_request, -200, '无对应客户号，查询失败', '', '')
        return SetErrMsgAndBody(ref_request, 0, msg, '', '', data)
    except Exception as e:
        anstext = MakeAnsStr(-1001, "nginx异常:" + str(repr(e)), "", "")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqQueryAccount', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext, len(anstext))
        # 这些异常都是我们关注的
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "bpagentlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


# 查询腾讯id 400002 测试环境专用
async def pyreqQueryUserId(ref_request, jsonkwlreq):
    try:
        if g_environment != 'test':
            return SetErrMsg(ref_request, -100, '非测试环境不支持调用测试接口', '', '')
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        iRet, msg, data = queryUserId(reqdict)

        if iRet != 0:
            return SetErrMsg(ref_request, iRet, msg, '', '')
        if len(data) == 0:
            return SetErrMsg(ref_request, -200, '无对应腾讯id，查询失败', '', '')

        return SetErrMsgAndBody(ref_request, 0, msg, '', '', data)
    except Exception as e:
        anstext = MakeAnsStr(-1001, "nginx异常:" + str(repr(e)), "", "")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqQueryAccount', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext, len(anstext))
        # 这些异常都是我们关注的
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "bpagentlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


# 查询验证码 400020 测试环境专用
async def pyreqCheckSmsCode(ref_request, jsonkwlreq):
    try:
        if g_environment != 'test':
            return SetErrMsg(ref_request, -100, '非测试环境不支持调用测试接口', '', '')
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        TEL_NO = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']['TEL_NO']

        if len(TEL_NO) == 0:
            return SetErrMsg(ref_request, -200, '验证码查询[pyreqCheckSmsCode]:手机号入参[TEL_NO]为空', '', '')

        SMS_CODE = GetRedisKey(TEL_NO)

        if SMS_CODE is None:
            return SetErrMsg(ref_request, -200, '无对应验证码，查询失败', '', '')

        return SetErrMsgAndBody(ref_request, '0', '验证码查询成功', '', '', [{"SMS_CODE": SMS_CODE.decode("utf-8")}])
    except Exception as e:
        anstext = MakeAnsStr(-1001, "nginx异常:" + str(repr(e)), "", "")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqCheckSmsCode', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext, len(anstext))
        # 这些异常都是我们关注的
        todaydate = time.strftime('%Y-%m-%d', time.localtime())
        exceptlogfile = open("./logs/" + "bpagentlog" + todaydate + ".txt", 'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime('%Y-%m-%d %X', time.localtime())
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return

def makeResult(resultDict):
    answerDict = {}
    ansHeaderDict = {}
    rootDict = {}

    ansHeaderDict["MSG_CODE"] = resultDict["MSG_CODE"]
    ansHeaderDict["MSG_TEXT"] = resultDict["MSG_TEXT"]

    answerDict["ANS_MSG_HDR"] = ansHeaderDict

    tempList = []
    tempList.append(answerDict)

    rootDict["ANSWERS"] = tempList

    if ansHeaderDict["MSG_CODE"] != "0":
        return rootDict

    resultDict.pop("MSG_CODE")
    resultDict.pop("MSG_TEXT")
    answerDict["ANS_COMM_DATA"] = resultDict

    return rootDict


orderUrlDict = {
    '73':'http://116.31.80.124:8081/cn-jsfund-server-mobile/bkt/jinweilan?code=3aq1v2aqeh0osnkpm5bep8f3uj' #金贝塔
}

async def pyreqJBTOrder(ref_request,jsonkwlreq):
    try:
        userID = jsonkwlreq["REQUESTS"][0]["REQ_COMM_DATA"]["USER_ID_CLS"]

        url = orderUrlDict.get(userID)

        if url is None:
            return SetErrMsg(ref_request, '-1', 'USER_ID error', '', '')

        result = requests.get(url)
        resultDict = json.loads(result.text)

        ansDict = makeResult(resultDict)
        ansJson = json.dumps(ansDict, ensure_ascii=False)

        SendResponse(ref_request, ansJson, len(ansJson))

    except Exception as e:
        anstext = '{"ANSWERS":[{"ANS_COMM_DATA":null,"ANS_MSG_HDR":{"MSG_CODE":"-100","MSG_LEVEL":"0","MSG_TEXT":"nginx异常:' + str(repr(e)) + '"}}]}'
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext) , 'pyreqSimpleZX_ct', ZTLOG_ERROR, msgid='')
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        SendResponse(ref_request, anstext,len(anstext))
    return

def InitThread(recvInstanse):
    global g_IsZXThreadInited
    if g_IsZXThreadInited:
        return
    threadRecv = Thread(target=RecvThread, args=(recvInstanse,))
    threadRecv.start()
    g_IsZXThreadInited = True


#券商公告删除600098
async def pyreqDeleteNotice(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']

        #校验签名用
        reqheaddict = jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']
        # 各渠道可设置免校验
        # if 'VERSION' in reqheaddict and g_channelsign['CHECKFLAG'] == True and g_channelsign[reqdict['USER_ID_CLS']] != False:
        #     #校验签名
        #     if 'SIGN' not in reqheaddict or reqheaddict['SIGN'] == '':
        #         return SetErrMsg(ref_request,-100,'该功能请求包头参数SIGN未送','','')
        #     if 'SIGN_TYPE' not in reqheaddict or reqheaddict['SIGN_TYPE'] == '':
        #         return SetErrMsg(ref_request,-100,'该功能请求包头参数SIGN_TYPE未送','','')
        #     sign = reqheaddict.pop('SIGN')
        #     strsortstr = kwlpyfunction.sortdict([reqheaddict]) + '&' + kwlpyfunction.sortdict([reqdict])  + '&key=' + g_channelsign[reqdict['USER_ID_CLS']]
        #     if  kwlpyfunction.CheckSign([strsortstr,sign,reqheaddict['SIGN_TYPE']]) != True:
        #         return SetErrMsg(ref_request,-100,'校验签名失败','校验签名失败','')

        iRet,msg,data = DeleteNotice(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s'%(ref_request,  '抛出异常应答:' + anstext), 'pyreqDeleteNotice', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return '-1001','业务异常',[], anstext

#券商公告修改600099
async def pyreqUpdateNotice(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']

        #校验签名用
        reqheaddict = jsonkwlreq['REQUESTS'][0]['REQ_MSG_HDR']
        # 各渠道可设置免校验
        # if 'VERSION' in reqheaddict and g_channelsign['CHECKFLAG'] == True and g_channelsign[reqdict['USER_ID_CLS']] != False:
        #     #校验签名
        #     if 'SIGN' not in reqheaddict or reqheaddict['SIGN'] == '':
        #         return SetErrMsg(ref_request,-100,'该功能请求包头参数SIGN未送','','')
        #     if 'SIGN_TYPE' not in reqheaddict or reqheaddict['SIGN_TYPE'] == '':
        #         return SetErrMsg(ref_request,-100,'该功能请求包头参数SIGN_TYPE未送','','')
        #     sign = reqheaddict.pop('SIGN')
        #     strsortstr = kwlpyfunction.sortdict([reqheaddict]) + '&' + kwlpyfunction.sortdict([reqdict])  + '&key=' + g_channelsign[reqdict['USER_ID_CLS']]
        #     if  kwlpyfunction.CheckSign([strsortstr,sign,reqheaddict['SIGN_TYPE']]) != True:
        #         return SetErrMsg(ref_request,-100,'校验签名失败','校验签名失败','')

        iRet,msg,data = UpdateNotice(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        return SetErrMsgAndBody(ref_request,0,msg,'','',data)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqUpdateNotice', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
        return '-1001','业务异常',[], anstext

#券商公告查询600400
async def pyreqSelectNotice_tx(ref_request,jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        reqdict['TRD_DATE'] = reqdict['TRD_DATE'][0:4] + '-' + reqdict['TRD_DATE'][4:6] + '-' + reqdict['TRD_DATE'][6:8] + ' 00:00:00'
        iRet,msg,data = SelectNotice(reqdict)
        if iRet != 0:
            return SetErrMsg(ref_request,iRet,msg,'','')
        tmp_dict = {}
        ans = []
        clearing_dict = {}
        todaydate = time.strftime('%Y%m%d', time.localtime())
        for row in data:
            nowtime = datetime.datetime.now()
            # 比较大小
            endtime = datetime.datetime.strptime(row['END_DATE'], '%Y-%m-%d %H:%M:%S')
            if endtime > nowtime:
                tmp_dict['STATUS'] = '1'
            else:
                tmp_dict['STATUS'] = '0'
            tmp_dict['BEGIN_DATE'] = row['BGN_DATE'].replace('-','')[0:8]
            tmp_dict['END_DATE'] = row['END_DATE'].replace('-', '')[0:8]
            tmp_dict['BEGIN_TIME'] = row['BGN_DATE'][-8:].replace(':','')
            tmp_dict['END_TIME'] = row['END_DATE'][-8:].replace(':','')
            tmp_dict['TEXT'] = row['TEXT']
            if tmp_dict['BEGIN_DATE'] > todaydate:
                continue
            if tmp_dict['TEXT'] == '清算公告s':
                clearing_dict = copy.deepcopy(tmp_dict)
            else:
                ans.append(tmp_dict)
            tmp_dict = {}
        note = 0
        if clearing_dict:
            for row in ans: #维护公告和清算公告有时间重叠时合并
                # if row['END_DATE'] == clearing_dict['END_DATE']:
                clearing_begin = clearing_dict['BEGIN_DATE'] + clearing_dict['BEGIN_TIME']
                clearing_end = clearing_dict['END_DATE'] + clearing_dict['END_TIME']
                row_begin = row['BEGIN_DATE'] + row['BEGIN_TIME']
                row_end = row['END_DATE'] + row['END_TIME']
                if clearing_begin <= row_begin <= clearing_end :
                    row['BEGIN_TIME'] = clearing_dict['BEGIN_TIME']
                    note = 1
                if clearing_begin <= row_end <= clearing_end :
                    row['END_TIME'] = clearing_dict['END_TIME']
                    note = 2
                if row_begin <= clearing_begin  and row_end >= clearing_end :
                    note = 3 #完全覆盖
                if row_begin > clearing_begin and row_end < clearing_end:
                    row['BEGIN_TIME'] = clearing_dict['BEGIN_TIME']
                    row['END_TIME'] = clearing_dict['END_TIME']
                    note = 3  # 完全覆盖
            if note == 0:
                ans.append(clearing_dict)
        return SetErrMsgAndBody(ref_request,0,msg,'','',ans)
    except Exception as e:
        anstext = MakeAnsStr(-1001,"nginx异常:" + str(repr(e)) ,"","")
        kwl_py_write_log('应答串[%s]:%s' % (ref_request, '抛出异常应答:' + anstext), 'pyreqSelectNotice_tx', ZTLOG_ERROR, msgid='')
        SendResponse(ref_request, anstext,len(anstext))
        #这些异常都是我们关注的
        todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
        exceptlogfile=open("./logs/"  +"bpagentlog" + todaydate + ".txt",'a+')
        strerr = traceback.format_exc()
        nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
        exceptlogfile.write(nowtime + ':' + strerr)
        exceptlogfile.flush()
        exceptlogfile.close()
    return


# session换国金网厅token
async def sessionExchangeToken(ref_request, jsonkwlreq):
    try:
        reqdict = jsonkwlreq['REQUESTS'][0]['REQ_COMM_DATA']
        if 'session_id' not in reqdict.keys():
            return SetGJWTMsgAndBody(ref_request, -7001, '[session_id]必传', {})

        # 验签
        sign_key = g_sign_key
        if not check_sign(jsonkwlreq, sign_key):
            return SetGJWTMsgAndBody(ref_request, -900009, '验签失败', {})

        session_id = reqdict['session_id']
        str_session = DESCli.SessionDeCrypt(session_id, g_session_des_key)
        if not str_session:
            return SetGJWTMsgAndBody(ref_request, -7002, '[session_id]解析失败', )

        send_dict = {}
        send_dict['app_id'] = g_GJ_token_config['app_id']
        send_dict['account_type'] = g_GJ_token_config['account_type']
        send_dict['expired_time'] = g_redis_expiretime

        session_dict = json.loads(str_session)
        rediskey = g_user_id_cls + g_company_id + session_dict['user_id']
        redisvalue = GetRedisKey(rediskey)

        # 从redis校验票据
        if redisvalue == None:
            return SetGJWTMsgAndBody(ref_request, -177, '票据已过期', {})
        redisvalue = eval(redisvalue)
        if redisvalue['SESSION'] != session_id:
            kwl_py_write_log('最新的的session:%s'%redisvalue['SESSION'], 'sessionExchangeToken', ZTLOG_ERROR, msgid='')
            return SetGJWTMsgAndBody(ref_request, -178, '票据已过期', {})

        send_dict['client_id'] = session_dict['1'][0]['3']
        send_dict['account'] = session_dict['1'][0]['4']
        pwd = des_decrypt(session_dict['1'][0]['6'], '410301_DesPwd')
        if not pwd:
            return SetGJWTMsgAndBody(ref_request, -7003, '取session密码失败', {})

        send_dict['password'] = pwd
        code, msg, ans = await httprequest_str(g_GJ_token_config['global_url'], send_dict)
        if code != '0':
            return SetGJWTMsgAndBody(ref_request,code, msg, {})
        ans = json.loads(ans)
        if ans['code'] != 0:
            return SetGJWTMsgAndBody(ref_request, ans['code'], ans['message'], {})
        send_dict.clear()
        send_dict['durable_token'] = ans['result']['durableToken']
        send_dict['expired_time'] = g_redis_expiretime
        code, msg, ans = await httprequest_str(g_GJ_token_config['simple_url'], send_dict)
        if code != '0':
            return SetGJWTMsgAndBody(ref_request,code, msg, {})
        ans = json.loads(ans)
        if ans['code'] != 0:
            return SetGJWTMsgAndBody(ref_request, ans['code'], ans['message'], {})
        op_red_key = kwlpyfunction.md5_encrypt(session_id)
        op_station = GetRedisKey(op_red_key)
        if op_station:
            op_station = op_station.decode()
            ans['result']['op_station'] = kwlpyfunction.aes_Encrypt(op_station, g_op_aes_key)
            expireSessionRedis(rediskey, op_red_key)
        else:
            kwl_py_write_log('从redis取的op_station为空', 'sessionExchangeToken', ZTLOG_ERROR, msgid='')
            ans['result']['op_station'] = ''
        return SetGJWTMsgAndBody(ref_request, ans['code'], ans['message'], ans['result'])
    except Exception as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'sessionExchangeToken', ZTLOG_ERROR,  msgid='')
        return SetGJWTMsgAndBody(ref_request, -7100, repr(e), {})
