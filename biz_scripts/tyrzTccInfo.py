# -*- coding: utf-8 -*-
#各渠道各券商组TCC的函数模块
import json

#全户通华林取手机号
def Q12900MakeTccPhone(TCC):
    try:
        dicttcc = json.loads(TCC)
        if 'phone' in dicttcc:
            return dicttcc['phone']
    except Exception :
        return ''
    return ''

#全户通华林组留痕信息
def Q12900MakeTccInfo(TCC):
    try:
        dicttcc = json.loads(TCC)
        strTcc = ''
        if 'appName' in dicttcc:
            strTcc = strTcc + dicttcc['appName']
        strTcc = strTcc + ';'
        if 'phone' in dicttcc:
            strTcc = strTcc + dicttcc['phone']
        strTcc = strTcc + ';'
        if 'deviceId' in dicttcc:
            strTcc = strTcc + dicttcc['deviceId']
        strTcc = strTcc + ';'
        if 'mac' in dicttcc:
            strTcc = strTcc + dicttcc['mac']
        strTcc = strTcc + ';'
        if 'channelNo' in dicttcc:
            strTcc = strTcc + dicttcc['channelNo']
        strTcc = strTcc + ';'
        if 'ip' in dicttcc:
            strTcc = strTcc + dicttcc['ip']
        strTcc = strTcc + ';'
        if 'osVer' in dicttcc:
            strTcc = strTcc + dicttcc['osVer']
        return strTcc
    except Exception :
        return ''
    return ''

#恒泰组留痕信息
def Q19000MakeTccInfo(TCC):
    try:
        dicttcc = json.loads(TCC)
        strTcc = ''
        if 'channelNo' in dicttcc:
            strTcc = strTcc + dicttcc['channelNo']
        strTcc = strTcc + ';'
        if 'ip' in dicttcc:
            strTcc = strTcc + dicttcc['ip']
        strTcc = strTcc + ';'
        if 'mac' in dicttcc:
            strTcc = strTcc + dicttcc['mac']
        strTcc = strTcc + ';'
        if 'deviceId' in dicttcc:
            strTcc = strTcc + dicttcc['deviceId']
        strTcc = strTcc + ';'
        if 'phone' in dicttcc:
            strTcc = strTcc + dicttcc['phone']
        strTcc = strTcc + ';'
        if 'appName' in dicttcc:
            strTcc = strTcc + dicttcc['appName']
        return strTcc
    except Exception :
        return ''
    return ''


#平安组留痕信息TCC_INFO
def Q11600MakeTccInfo(TCC):
    try:
        dicttcc = json.loads(TCC)
        strTcc = ''
        if 'appName' in dicttcc:
            strTcc = strTcc + dicttcc['appName']
        strTcc = strTcc + ';'
        if 'deviceId' in dicttcc:
            strTcc = strTcc + dicttcc['deviceId']
        strTcc = strTcc + ';'
        if 'phone' in dicttcc:
            strTcc = strTcc + dicttcc['phone']
        return strTcc
    except Exception :
        return ''
    return ''

#平安组留痕信息TCC_DEVICEINFO
def Q11600MakeDeviceInfo(TCC):
    try:
        dicttcc = json.loads(TCC)
        strTcc = ''
        if 'deviceId' in dicttcc:
            strTcc = strTcc + dicttcc['deviceId']
        return strTcc
    except Exception :
        return ''
    return ''

#平安组留痕信息TCC_PHONEINFO
def Q11600MakePhoneInfo(TCC):
    try:
        dicttcc = json.loads(TCC)
        strTcc = ''
        if 'phone' in dicttcc:
            strTcc = strTcc + dicttcc['phone']
        strTcc = strTcc + ';'
        if 'appName' in dicttcc:
            strTcc = strTcc + dicttcc['appName']
        strTcc = strTcc + ';'
        if 'osVer' in dicttcc:
            strTcc = strTcc + dicttcc['osVer']
        return strTcc
    except Exception :
        return ''
    return ''