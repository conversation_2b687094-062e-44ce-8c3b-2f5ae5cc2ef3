#rsa解密私钥
from common_config import STOCK_CODE

with open('private.pem') as f:
    g_masterprivatekey = f.read()


#各渠道解密方式字典  #自选股渠道kddecode不适用
g_dict_channel_decode_way = {
    'Q':'rsa_decrypt',   #全户通
    'L':'rsa_decrypt',   #陆金所
    '1':'rsa_decrypt',   #微证券
    'B':'jd_des_decrypt'  #京东
}

#各渠道解密方式key字典
g_dict_channel_decode_key = {
    'Q':g_masterprivatekey,   #全户通
    'L':g_masterprivatekey,   #陆金所
    '1':g_masterprivatekey,   #微证券
    'B':'jd_jz734'  #京东
}

general_dict = {
    'operway' :'e',  #操作方式
    "ext_inst":"WZQ",   #外部接入机构编码
    "g_busi_node":"1",   #节点编号
    "g_operid":"110136",  #柜员帐号
    "g_operpwd":"SWTKtb6lUpkJANSydX5gEFg=="   #柜员密码

}

#银行入参转换字典,各券商都要修改
BankcodeTransfer_indict = {
    "8006":"4", #工行三方
    "8005":"6", #建行三方
    "8003":"7", #招行三方
    "8007":"E", #中行三方
    "8004":"C", #交行三方
    "1007":"9", #兴业三方
    "8010":"B", #民生三方
    "8013":"", #广发三方
    "1200":"J", #浦发三方
    "1700":"G", #上海三方
    "8002":"A", #农行三方
    "8011":"I", #光大三方
    "8012":"K", #平安三方
    "8008":"D", #中信三方
    "8009":"F", #华夏三方
    "2300":"H", #北京三方
    "8017":"" #邮储三方
}

#存管指定标志
Bkaccount_regflag_outdict = {
    "0": "0",  # 未指定
    "1": "0",  # 预指定
    "2": "1",  # 已指定
}

#银行出参转换字典,各券商都需要修改
BankcodeTransfer_outdict = {
    "4":"1001", #工行银行
    "A":"1002", #农业银行
    "E":"1003", #中国银行
    "6":"1004", #建设银行
    "C":"1005", #交通银行
    "7":"1006", #招商银行
    "9":"1007", #兴业银行
    "B":"1008", #民生银行
    "K":"1009", #平安银行
    "J":"1010", #上海浦东发展银行
    "I":"1012", #光大银行
    "F":"1013", #华夏银行
    "Q":"1014", #中国邮政储蓄
    "M":"1015", #广东发展银行
    "P":"1016", #宁波银行
    "D":"1017", #中信银行
    "0000":"1018", #渤海银行
    "0000":"1019", #浙商银行
    "H":"1020", #北京银行
    "G":"1021", #上海银行
    "0000":"1022", #长沙银行
    "0000":"1023", #东莞银行
    "O":"1024", #南京银行
    "S":"1025", #广州银行
    "0000":"1027", #天津银行
    "N":"1026", #江苏银行
}


#银行入参转换字典
dict_HLBkCode = {
    "1001":"4", #工商银行
    "1002":"A", #农业银行
    "1003":"E", #中国银行
    "1004":"6", #建设银行
    "1005":"C", #交通银行
    "1006":"7", #招商银行
    "1007":"9", #兴业银行
    "1008":"B", #民生银行
    "1009":"K", #平安银行
    "1010":"J", #上海浦东发展银行
    "1012":"I", #光大银行
    "1013":"F", #华夏银行
    "1014":"Q", #中国邮政储蓄
    "1015":"M", #广东发展银行
    "1016":"P", #宁波银行
    "1017":"D", #中信银行
    "1018":"", #渤海银行
    "1019":"", #浙商银行
    "1020":"H", #北京银行
    "1021":"G", #上海银行
    "1022":"", #长沙银行
    "1023":"", #东莞银行
    "1024":"O", #南京银行
    "1025":"S", #广州银行
    "1027":"", #天津银行
    "1026":"N", #江苏银行
}

dict_Hualin_Security_To_Tencent = {
    "2001": "1001",  # 工商银行
    "2002": "1002",  # 农业银行
    "2003": "1003",  # 中国银行
    "2004": "1004",  # 建设银行
    "2005": "1005",  # 交通银行
    "2006": "1006",  # 招商银行
    "2011": "1007",  # 兴业银行
    "2008": "1008",  # 民生银行
    "2007": "1009",  # 平安银行
    "2018": "1010",  # 上海浦东发展银行
    "2008": "1011",  # 民生银行
    "2016": "1012",  # 光大银行
    "2009": "1013",  # 华夏银行
    "0000": "1014",  # 中国邮政储蓄
    "2012": "1015",  # 广东发展银行
    "0000": "1016",  # 宁波银行
    "2010": "1017",  # 中信银行
    "0000": "1018",  # 渤海银行
    "0000": "1019",  # 浙商银行
    "2017": "1020",  # 北京银行
    "2013": "1021",  # 上海银行
    "0000": "1022",  # 长沙银行
    "0000": "1023",  # 东莞银行
    "2014": "1024",  # 广州银行
}

TRD_ID_bsflag_600130dict = {
    "0B"  :"0B",  #"买入"
    "0S"  :"1F",  #"卖出"
    "UB"  :"1E",  #"最优成交转价买"
    "US"  :"1F",  #"最优成交转价卖"
    "VB"  :"1E",  #"最优成交剩撤买"
    "VS"  :"1F",  #"深圳即时成交剩撤买"
    "2B"  :"1E",   #"深圳即时成交剩撤卖"
    "2S"  :"1F",  #"深圳即时成交剩撤卖"
    "WB"  :"1E",  #"深圳全成交或撤销买"
    "WS"  :"1F",  #"深圳全成交或撤销卖"
    "XB"  :"1E",  #"深圳本方最优价格买"
    "XS"  :"1F",  #"深圳本方最优价格卖"
    "YB"  :"1E",  #"深圳对手最优价格买"
    "YS"  :"1F"  #"深圳对手最优价格卖"
}

TRD_ID_bsflag_600140dict = {
    "0B":"1" , #买入
    "0S":"2",  #卖出
    "UB":"1",  #最优成交转价买
    "US":"2",  #最优成交转价卖
    "VB":"1",  #最优成交剩撤买
    "VS":"2",  #最优成交剩撤卖
    "2B":"1",  #深圳即时成交剩撤买
    "2S":"2",  #深圳即时成交剩撤卖
    "WB":"1",  #深圳全成交或撤销买
    "WS":"2",  #深圳全成交或撤销卖
    "XB":"1",  #深圳本方最优价格买
    "XS":"2",  #深圳本方最优价格卖
    "YB":"1",  #深圳对手最优价格买
    "YS":"2",  #深圳对手最优价格卖
    "3B":"1",
    "3S":"2",
    "04": "1",
    "11": "2",
    "TI": "1",
    "TO": "2",
    "Z0":"0U",  #指定交易
    "Z1":"0V",  #撤指定交易
    "NB":"1",  #新股申购
    "NS":"2",  # 申购还款
    "1R":"3B",  #增强型限价买入
    "4R":"2B",  #竞价限价买入
    "1C":"3S",  #特别限价卖出
    "4C":"2S",  #竞价限价卖出
    "5C":"4S"  #碎股卖出

}

QueryBsflag_outdict = {
    "1": "0B",  # 买入
    "2": "0S",  # 卖出
    "42": "0S",  # 卖出
    "01":"0B",  #买入
    "02":"0S",  #卖出
    "P":"NB",  #定价申购
    "12":"04",  #配股缴款
    "0J":"04", #配股缴款(600390)
    "T":"0T",  #配售缴款
    "L":"0L",  #配售放弃
    "T1":"2B",  #即时买入 深交所即时成交剩余撤销申报买
    "U1":"VB",  #五档买入 上交所最优五档即时成交剩余撤销买
    "V1":"WB",  #全额买入 深交所全额成交或撤销申报买
    "T2":"2S",  #即时卖出 深交所即时成交剩余撤销申报卖
    "U2":"VS",  #五档卖出 上交所最优五档即时成交剩余撤销卖
    "V2":"WS",  #全额卖出 深交所全额成交或撤销申报卖
    "R1":"UB",  #转限买入 上交所最优五档即时成交剩余转限价买
    "R2":"US",  #转限卖出  上交所最优五档即时成交剩余转限价卖

    "S1":"XB", #"深交所本方最优价格申报买"/>
    "S2":"XS",  #="深交所本方最优价格申报卖"/>
    "Q1":"YB",  #="深交所对手方最优价格申报买"/>
    "Q2":"YS",  #="深交所对手方最优价格申报卖"/>
    "31":"NB",  #="新股申购"/>

    "3B":"0B",  #增强型限价买入  港股
    "2B":"0B",  #竞价限价买入  港股
    "3S":"0S",  #增强型限价卖出  港股
    "2S":"0S",  #竞价限价卖出  港股
    "4S":"5C",   #碎股卖出  港股
    "PFP1":"3B",  #盘后定价买
    "PFP2":"3S"   #盘后定价卖

}

#撤单标志字典
CancelFlagTranfer_outdict ={
  "0":"0",   #正常
  "2":"1"    #撤单
}

#投资者风险类型字典
HLZQ_RISK_TYPE_TO_WECHAT_dict ={
    "18": "0",  # 保守型
    "19": "1",  # 稳健型
    "14": "2",  # 平衡型
    "20": "3",  # 积极型
    "25": "4"   # 激进型
}

#客户风险等级
HLZQ_RATING_LVL_TO_WECHAT_dict ={
    "1": "0",  # 保守型
    "2": "1",  # 稳健型
    "3": "2",  # 平衡型
    "4": "3",  # 积极型
    "5": "4",  # 激进型
    "": "5",  # 特殊保护型或保守型（最低等级）
}

# 客户投资偏好范围
HLZQ_INVEST_PRO_TO_WECHAT_dict ={
    "01": "0",  # 固定收益类
    "02": "1",  # 权益类
    "03": "2",  # 衍生类
    "04": "3",  # 复杂投资类
    "99": "7"   # 其他
}


# 新客理财TA编号字典 腾讯-柜台
GJZQ2TC_xinkelicai_TA_dict = {
    "CZZ": "0"
}

# 新客理财TA编号字典 腾讯-柜台
TC2GJZQ_xinkelicai_TA_dict = {
    "0": "CZZ"
}

# 客户风险等级 柜台-腾讯
GJZQ_CUST_RISK_LEV_dict ={
    "1": "0",  # 保守型
    "2": "1",  # 稳健型
    "3": "2",  # 平衡型
    "4": "3",  # 积极型
    "5": "4",  # 激进型
    "": "5",  # 保守型（最低等级）  柜台min_rank_flag=1就是最低等级。
}

# 产品风险等级 柜台-腾讯
GJZQ_PROD_RISK_LEV_dict ={
    "1": "0",  # 低风险
    "2": "1",  # 较低风险
    "3": "2",  # 中风险
    "4": "3",  # 较高风险
    "5": "4",  # 高风险
}

# 产品代码类型 柜台-腾讯
GJZQ_PROD_TYPE_dict = {
    "c": "1",  # 券商理财
    "": "2",  # 公募基金
    "z": "3",  # 其他理财
}

# 新客理财委托字典 柜台-腾讯
GJZQ2TC_xinkelicai_order_dict = {
    "0": "0",   # 未报
    "2": "1",   # 已报
    "V": "2",   # 已确认
    "8": "3",   # 已成
    "6": "4",   # 已撤
    "9": "5"    # 废单
}

# 客户投资偏好期限
HLZQ_INVEST_LMT_TO_WECHAT_dict ={
    "2": "0",  # 短期
    "1": "1",  # 中期
    "0": "2",  # 长期

}

#发送标志
orderstats_to_DCL_FLAG_dict = {
    "0" :"0", #未发送
    "1" :"0", #未发送
    "2" :"1", #已发送
    "3" :"7", #已报待撤
    "4" :"8", #部成待撤
    "5" :"4", #部成部撤
    "6" :"5", #全部撤单
    "7" :"2", #部分成交
    "8" :"3", #全部成交
    "9" :"6", #废单
    "D": "6",  # 废单
    "C" :"0", #未发送
    "U" :"1", #已发送
    "V" :"1", #已发送
    "W" :"1" #已发送
}

#恒生成交类型
dict_hs_match_type={
    "00":"0" ,#普通成交
    "20":"1" ,#撤单成交
    "02":"1" ,#废单
    "22":"1" ,#撤单废单
}

#成交类型
dict_match_type={
    "0":"0" ,#普通成交
    "1":"1" ,#撤单成交
    "2":"1" ,#废单
    "3":"1" ,#内部撤单
    "4":"1" ,#撤单废单
}

#银证业务类型
Bnktranid_dict = {
    "1":"1000", #转入
    "2":"1001", #转出
    "3":"1003"  #查询余额
}

#银证状态字典
Transfer_status_dict = {
    "0":"1" , #已报
    "1":"1" , #已报
    "2":"2" , #成功
    "3":"3" , #失败
    "4":"4" , #超时
    "5":"1" , #已报
    "6":"6" , #已冲正
    "7":"2" , #成功
    "8":"3" , #失败
}

#是否主资金账户
MAIN_FUND_FLAG_outdict = {
    "0":"1" , #主币种帐户
    "1":"0" , #子账户
    "2":"0" , #子账户
    "3":"0" , #子账户
    "4":"0" , #子账户
    "5":"0" , #子账户
}

#入参市场字典
dict_market_wzq_to_T2sdk = {
    "0":"2" , #深圳A
    "1":"1" , #上海A
    "2":"H" , #深圳B
    "3":"D" , #上海B
    "4":"2" , #深基金
    "5":"1" , #沪基金
    "6": "S",  # 深 H
    "7": "G",  # 沪H
    "8": "",  # 代 A（新三板）
    "9": "",  # 代 B
    "10": "",  # 场外开放基金；
    "11": "",  # 场外代销；；

}

#出参市场字典
dict_market_T2sdk_to_wzq = {
    #holder_kind,exchange_type拼接后转换
    "02": "0",  # 深圳A
    "01": "1",  # 上海A
    "0H": "2",  # 深圳B
    "0D": "3",  # 上海B
    "12": "4",  # 深基金
    "11": "5",  # 沪基金
    "0S": "6",  # 沪H
    "0G": "7",  # 深 H
    "09": "8",  # 代A(新三板)
    "0A": "9",  # 代B

    #之前的一般转换
    "2":"0" , #深圳A
    "1":"1" , #上海A
    "H":"2" , #深圳B
    "D":"3" , #上海B
    "C": "4",  # 深基金
    # "": "5",  # 沪基金
    "9": "",  #
    "A": "",  #
    "G": "7",  # 沪H
    "S": "6",  # 深 H
}
market_dict = {
    "2": "0",# 或4（当exchange为2时，同步判断holder_kind，holder_kind不为1，则对应0 - 深市，holder_kind为1，则对应4 - 深基金）
    "1": "1",# 或5（当exchange为1时，同步判断holder_kind，holder_kind不为1，则对应1 - 沪市，holder_kind为1，则对应5 - 沪基金）
    "H": "2",
    "D": "3",
    "S": "6",
    "G": "7",
    "9": "8",
    "A": "9"
}


#港股通返回市场字典
GGT_Market_outdict = {
    "S":"6" , #深港市场
    "5":"7"   #沪港市场
}

#港股通入参市场字典
GGT_Market_indict = {
    "6":"s" , #深港市场
    "7":"5"   #沪港市场
}

#职业入参 tx : 柜台
dict_GJZQ_WECHAT_TO_OCCU_TYPE = {
    # 新字典
    "200": "02",  # 党政机关负责人及管理人员
    "201": "03",  # 企事业单位负责人及管理人员
    "202": "A2",  # 民主党派和工商联负责人及管理人员
    "203": "A3",  # 人民团体或群众团体负责人及管理人员
    "204": "A4",  # 社会组织（社会团体、基金会、社会服务机构、外国商会等）负责人及管理人员
    "205": "A5",  # 科学研究及教学人员
    "206": "70",  # 文学艺术、体育专业人员
    "207": "71",  # 新闻出版、文化专业人员
    "208": "67",  # 卫生专业技术人员
    "209": "A6",  # 工程、农业专业人员
    "210": "A7",  # 法律、会计、审计、税务专业人员
    "211": "10",  # 证券从业人员
    "212": "18",  # 经济和金融专业人员
    "213": "A8",  # 宗教人士等特殊职业人员
    "214": "A9",  # 其他专业技术人员
    "215": "Aa",  # 党政机关、企事业单位行政工作人员
    "216": "Ab",  # 民主党派、工商联、人民团体或社会组织等单位工作人员
    "217": "Ac",  # 人民警察、消防、应急救援人员
    "218": "47",  # 批发与零售服务人员
    "219": "63",  # 房地产服务人员
    "220": "Ad",  # 旅游、住宿和餐饮服务人员
    "221": "Ae",  # 珠宝、黄金等贵金属行业服务人员
    "222": "54",  # 文化、体育和娱乐服务人员
    "223": "Af",  # 典当、拍卖行业服务人员
    "224": "Ag",  # 艺术品或文物收藏行业服务人员
    "225": "Ah",  # 废品、旧货回收服务人员
    "226": "72",  # 交通运输、仓储、邮政业服务人员
    "227": "48",  # 信息运输、软件和信息技术服务人员
    "228": "Ai",  # 居民、健康服务人员
    "229": "Aj",  # 其他社会生产和社会服务人员
    "230": "05",  # 农、林、牧、渔业生产及辅助人员
    "231": "Ak",  # 生产制造及有关人员
    "232": "08",  # 军人
    "233": "Al",  # 国际组织工作人员
    "234": "11",  # 离退休人员
    "235": "06",  # 个体工商户（含淘宝店自营等）
    "236": "07",  # 无业
    "237": "09",  # 学生

}

#职业出参 柜台 : tx
dict_GJZQ_OCCU_TYPE_TO_WECHAT = {
    # 新字典
    "02": "200",  # 党政机关负责人及管理人员
    "03": "201",  # 企事业单位负责人及管理人员
    "A2": "202",  # 民主党派和工商联负责人及管理人员
    "A3": "203",  # 人民团体或群众团体负责人及管理人员
    "A4": "204",  # 社会组织（社会团体、基金会、社会服务机构、外国商会等）负责人及管理人员
    "A5": "205",  # 科学研究及教学人员
    "70": "206",  # 文学艺术、体育专业人员
    "71": "207",  # 新闻出版、文化专业人员
    "67": "208",  # 卫生专业技术人员
    "A6": "209",  # 工程、农业专业人员
    "A7": "210",  # 法律、会计、审计、税务专业人员
    "10": "211",  # 证券从业人员
    "18": "212",  # 经济和金融专业人员
    "A8": "213",  # 宗教人士等特殊职业人员
    "A9": "214",  # 其他专业技术人员
    "Aa": "215",  # 党政机关、企事业单位行政工作人员
    "Ab": "216",  # 民主党派、工商联、人民团体或社会组织等单位工作人员
    "Ac": "217",  # 人民警察、消防、应急救援人员
    "47": "218",  # 批发与零售服务人员
    "63": "219",  # 房地产服务人员
    "Ad": "220",  # 旅游、住宿和餐饮服务人员
    "Ae": "221",  # 珠宝、黄金等贵金属行业服务人员
    "54": "222",  # 文化、体育和娱乐服务人员
    "Af": "223",  # 典当、拍卖行业服务人员
    "Ag": "224",  # 艺术品或文物收藏行业服务人员
    "Ah": "225",  # 废品、旧货回收服务人员
    "72": "226",  # 交通运输、仓储、邮政业服务人员
    "48": "227",  # 信息运输、软件和信息技术服务人员
    "Ai": "228",  # 居民、健康服务人员
    "Aj": "229",  # 其他社会生产和社会服务人员
    "05": "230",  # 农、林、牧、渔业生产及辅助人员
    "Ak": "231",  # 生产制造及有关人员
    "08": "232",  # 军人
    "Al": "233",  # 国际组织工作人员
    "11": "234",  # 离退休人员
    "06": "235",  # 个体工商户（含淘宝店自营等）
    "07": "236",  # 无业
    "09": "237",  # 学生


}

#学历入参
dict_HLZQ_EDUCATION_TO_T2SDK = {
    "0":"1", #博士
    "1":"2", #硕士
    "2":"3", #本科
    "3":"4", #大专
    "4":"5", #中专
    "5":"6", #高中
    "6":"7", #初中以下
    "Z":"8", #其他

}


#学历出参
dict_HLZQ_EDUCATION_TO_WECHAT = {
    "1":"0", #博士
    "2":"1", #硕士
    "3":"2", #本科
    "4":"3", #大专
    "5":"4", #中专
    "6":"5", #高中
    "7":"6", #初中以下
    "8":"Z", #其他

}

#港股通市场额度状态字典
GGT_QuotaStatus_outdict = {
    "1":"0" , #不可用
    "2":"1"   #可用
}

#柜台证件类型转换字典
dict_idtype_COUNTER_TO_WZQ = {
    "0": "00",  # 身份证",
    "J": "01",  # 护照",
    "3": "02",  # 军官证",
    "E": "03",  # 士兵证",
    "h": "04",  # 回乡证",
    "F": "05",  # 户口本",
    "1": "06",  # 外国护照",
    "C": "07",  # 解放军文职干部",
    "N": "08",  # 临时身份证",
    "K": "0a",  # 武警文职干部",
    "2": "10",  # 工商营业执照",
    "M": "11",  # 社团法人注册登记证书",
    "4": "09",  # 其他证件",
    "7": "09",  # 其他证件",
    "9": "09",  # 其他证件",
    "a": "09",  # 其他证件",
    "b": "09",  # 其他证件",
    "c": "09",  # 其他证件",
    "d": "09",  # 其他证件",
    "f": "09",  # 其他证件",
    "g": "09",  # 其他证件",
    "i": "09",  # 其他证件",
    "j": "09",  # 其他证件",
    "A": "09",  # 其他证件",
    "D": "09",  # 其他证件",
    "G": "09",  # 其他证件",
    "H": "09",  # 其他证件",
    "I": "09",  # 其他证件",
    "L": "09",  # 其他证件",
    "M": "09",  # 其他证件",
    "P": "09",  # 其他证件",
    "O": "09",  # 其他证件",
    "Q": "09",  # 其他证件",
    "R": "09",  # 其他证件",
    "S": "09",  # 其他证件",
    "T": "09",  # 其他证件",
    "Y": "09",  # 其他证件",
}

#证券类型
dict_stktype_PURCH_TYPE_TO_WZQ = {
    "4":"0",  #普通股票是
    "G":"1",   #转换债券
    "e": "2"   #科创版
}

dict_tx_balance_time_limit = {
    "1": "0",
    "3": "1",
    "7": "2",
    "14": "3",
    "21": "4",
    "28": "5",
    "35": "6",
    "91": "7",
    "31": "8",
    "8": "9",
    "2": "10",
    "19": "11"
}


dict_order_type_to_hs_type = {
    0: "QCA",  # 目前国金没提供 恒生 另外两个值 与腾讯的映射关系,暂定弄成一样  QNE、QCA、QNP
    1: "QCA",  # 目前国金没提供 恒生 另外两个值 与腾讯的映射关系,暂定弄成一样  QNE、QCA、QNP
    2: "QCA",   # 目前国金没提供 恒生 另外两个值 与腾讯的映射关系,暂定弄成一样  QNE、QCA、QNP
    "0": "QCA",  # 目前国金没提供 恒生 另外两个值 与腾讯的映射关系,暂定弄成一样  QNE、QCA、QNP
    "1": "QCA",  # 目前国金没提供 恒生 另外两个值 与腾讯的映射关系,暂定弄成一样  QNE、QCA、QNP
    "2": "QCA"   # 目前国金没提供 恒生 另外两个值 与腾讯的映射关系,暂定弄成一样  QNE、QCA、QNP
}


dict_order_type = {
    "提前购回": "0",
    "购买": "1",
    "所有": "2",
}

#客户状态
dict_client_status_TO_WZQ = {
    "0":"0",  #正常
    "":"1",   #锁定（用户输错密码或柜员对用户锁定）
    "2":"3",  #挂失
    "1":"4",   #冻结
    "3":"9",   #销户
    "4":"10",  # 其他
	"5":"10",  # 其他
	"6":"10",  # 其他
	"7":"10",  # 其他
	"8":"10",  # 其他
	"9":"10",  # 其他
	"A":"10",  # 其他
	"B":"10",  # 其他
	"C":"10",  # 其他
	"D":"10",  # 其他
	"E":"10",  # 其他
	"F":"10",  # 其他
	"G":"10",  # 其他
	"H":"10",  # 其他
	"I":"10",  # 其他
	"J":"10",  # 其他
	"K":"10",  # 其他
	"L":"10",  # 其他
	"S":"10",  # 其他
}

functiondict = {
    #签约的登录
    '410301':
        {
            'dstfuncid':'331100',
            'req':
                [
                    {'biznum#': {'dst': 'biznum', 'default': '410301'}},
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    # {'password':{'dst':'password','default':'111111'}},
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'input_content': {'dst': 'input_content', 'default': '1'}},
                    {'inputid': {'dst': 'account_content'}},    #资金账号
                    {'content_type': {'dst': 'content_type', 'default': '0'}}
                ],
            'ans':
                [
                    {'content_type': {'dst': 'market'}},
                    {'stock_account': {'dst': 'secuid'}},
                    {'client_name': {'dst': 'name'}},
                    {'asset_prop': {'dst': 'asset_prop'}},
                    {'fund_account': {'dst': 'fundid'}},
                    {'client_id': {'dst': 'custid'}},
                    {'client_name': {'dst': 'custname'}},
                    {'branch_no': {'dst': 'orgid'}},
                    {'bank_no': {'dst': 'bankcode'}}
                ]
        },
    '410324':
        {
            'dstfuncid':'410324',
            'req':
            [
                {'funcid':{'dst':'funcid'}},
                {'custid':{'dst':'custid'}},
                {'custorgid':{'dst':'custorgid'}},
                {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS,"kdencode","410301")'}},
                {'orgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext#-1':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},
                {'inputid':{'dst':'fundid'}},
                {'custcert':{'dst':'custcert'}}
            ],
            'ans':
            [
                {'name':{'dst':'name'}},
                {'fundid':{'dst':'fundid'}},
                {'operway':{'dst':'operway'}}
            ]
        },
    '410323':
        {
            'dstfuncid':'410323',
            'req':
            [
                {'funcid':{'dst':'funcid'}},
                {'custid':{'dst':'custid'}},
                {'custorgid':{'dst':'custorgid'}},
                {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS,"kdencode","410301")'}},
                {'orgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext#-1':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},
                {'custcert':{'dst':'custcert'}},
                {'newoperway':{'dst':'newoperway'}},
                {'inputid':{'dst':'fundid'}}
            ],
            'ans':
            [
                {'msgok':{'dst':'msgok'}}
            ]
        },
     '600010': #登录
        {
            'dstfuncid':'331100',
            'req':
            [
                {'biznum#':{'dst':'biznum','default':'410301'}},
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                # {'password':{'dst':'password','default':'111111'}},
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'TCC':{'dst':'op_station','functionPlugIn':'functionPlugIn(tranOpStation,TCC)'}},  #站点地址
                {'input_content':{'dst':'input_content','default':'1'}},
                {'account_content':{'dst':'account_content','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                {'content_type':{'dst':'content_type','default':'0'}}
            ],
            'ans':
            [
                {'content_type':{'dst':'market'}},
                {'stock_account':{'dst':'secuid'}},
                {'client_name':{'dst':'name'}},
                {'fund_account':{'dst':'fundid'}},
                {'client_id':{'dst':'custid'}},
                {'client_name':{'dst':'custname'}},
                {'branch_no':{'dst':'orgid'}},
                {'bank_no':{'dst':'bankcode'}}
            ]
        },
    '900010': #登录
        {
            'dstfuncid':'900010',
            'req':
            [
                {'account':{'dst':'account'}},
                {'company_id':{'dst':'company_id','default':'5010'}},   #操作分支机构
                {'login_code':{'dst':'login_code'}}, #操作方式
                {'login_type':{'dst':'login_type','default':'1'}},
                {'user_id':{'dst':'user_id'}}, #从票据取资金帐号
                {'user_id_cls':{'dst':'content_type','default':'3'}}
            ],
            'ans':
            [
                {'bank_no':{'dst':'bankcode'}}
            ]
        },
    '600041':  # 查询客户风险测评级别
        {
            'dstfuncid': '331018',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},  # 市场
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                ],
            'ans':
                [
                    {'corp_risk_level': {'dst': 'RISK_TYPE', 'dict': HLZQ_RATING_LVL_TO_WECHAT_dict}},
                    {'corp_risk_level': {'dst': 'corp_risk_level'}},
                    {'corp_end_date': {'dst': 'corp_end_date'}},
                    {'corp_end_date': {'dst': 'EXPIRED_DATE'}},
                    {'prof_flag': {'dst': 'SP_INVESTOR'}},
                    {'min_rank_flag': {'dst': 'min_rank_flag'}},
                ]
        },
    '600120A':	#资金信息查询-最大可取金额查询
        {
            'dstfuncid':'420507',
            'req':
            [
                {'funcid_tmp':{'dst':'funcid','default':'420507'}},
                {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd': {'dst': 'trdpwd'}},
                {'custorgid': {'dst': 'orgid'}},
                {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                {'ext': {'dst': 'ext', 'default': '0'}},
                {'netaddr': {'dst': 'netaddr'}},
                {'netaddr2': {'dst': 'netaddr2'}},
                {'custcert': {'dst': 'custcert'}},

                {'CURRENCY':{'dst':'moneytype'}},
                {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
            ],
            'ans':
            [
                {'fundid':{'dst':'fundid'}},  #资金账号
                {'moneytype':{'dst':'moneytype'}},	#货币代码
                {'orgid':{'dst':'orgid'}},	#机构代码
                {'maxdraw':{'dst':'maxdraw'}}	#最大可取金额
            ]
        },
	'600120':	#资金信息查询,原dstfuncid332278
        {
            'dstfuncid':'332217',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'CURRENCY':{'dst':'money_type'}}
            ],
            'ans':
            [
                {'FUNDID':{'dst':'ACCOUNT'}},		#资金账号
                {'money_type':{'dst':'CURRENCY'}},	#币种
                {'current_balance':{'dst':'BALANCE'}},		#资金余额
                {'enable_balance':{'dst':'AVAILABLE'}}, 	#资金可用金额
                {'frozen_balance':{'dst':'FRZ_AMT','functionPlugIn':'functionPlugIn(FormatFloat,frozen_balance,"2")'}},		#冻结金额
                {'fetch_cash': {'dst': 'DRAW_AMT','functionPlugIn': 'functionPlugIn(FormatFloat,fetch_cash,"2")'}},  # 可取资金
                {'market_value':{'dst':'MKT_VAL'}},		#持仓市值
                {'OUTSTANDING_tmp':{'dst':'OUTSTANDING','default':'0'}}, #在途金额
                {'asset_balance':{'dst':'ASSERT_VAL','default':'0'}}	#资产总值
            ]
        },
    '600124':	# 可转出资金信息查询 DH edit
        {
            'dstfuncid':'332301',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},    # 操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}},     # 操作方式
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no':{'dst':'branch_no','default':'5010'}},   # 分支机构
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'ACCOUNT': {'dst': 'fund_account', 'default': ''}},  # 资产账户
                # {'USER_ID_CLS': {'dst': 'USER_ID_CLS', 'default': '1'}},
                {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                # 从票据取密码
                {'password': {'dst': 'password',
                              'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,"1")'}}
            ],
            'ans':
            [
                {'lucky_balance': {'dst': 'lucky_balance'}}
            ]
        },
    '********':	#用户区间资金信息、券商开通标志查询
        {
            'dstfuncid':'********',
            'req':
            [
                {'funcid': {'dst': 'funcid'}},
                {'g_checksno': {'dst': 'g_checksno', 'default': '0'}},
                {'g_ext_inst': {'dst': 'g_ext_inst', 'default': '0'}},
                {'g_funcid': {'dst': 'g_funcid'}},
                {'p_gnbh': {'dst': 'p_gnbh', 'default': '********'}},
                {'p_gybh': {'dst': 'p_gybh', 'default': '999'}},
                {'ACCOUNT': {'dst': 'zjzh'}},   #资金账号
                {'cxlx': {'dst': 'cxlx','default':'11'}}       #查询类型
            ],
            'ans':
            [
                {'t20kcbzc':{'dst':'ASSERT_AVG_VAL'}}, #20日均资产
                {'bs':{'dst':'CAN_OPEN'}}, #标志 是否满足券商开通科创版条件
                {'scjyrq': {'dst': 'EARLY_TRD_DATE'}} #首次交易日
            ]
        },
    '600130': #最大交易查询
        {
            'dstfuncid':'410410',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'410410'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
               # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                {'MARKET':{'dst':'market'}},
                {'secuid_tmp':{'dst':'secuid','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}}, #从票据股东代码
                {'SECU_CODE':{'dst':'stkcode'}},
                {'TRD_ID':{'dst':'bsflag','dict':TRD_ID_bsflag_600130dict}},
                {'ORDER_PRICE':{'dst':'price'}},
                {'bankcode':{'dst':'bankcode'}},
                {'hiqtyflag':{'dst':'hiqtyflag'}}
            ],
            'ans':
            [
                {'maxstkqty':{'dst':'TRD_QTY'}}
            ]
        },
    '600140': #下单
        {
            'dstfuncid':'333002',
            'req':
            [
                {'COMPANY_ID':{'dst':'COMPANY_ID'}},
                {'funcid':{'dst':'funcid','default':'333002'}},
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'registe_sure_flag': {'dst': 'registe_sure_flag'}},
                {'TCC':{'dst':'op_station','functionPlugIn':'functionPlugIn(tranOpStation,TCC)'}},  #站点地址
                {'branch_no':{'dst':'branch_no','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'ACCOUNT':{'dst':'fund_account'}}, #资金帐号
                {'password_type':{'dst':'password_type','default':'2'}},
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'user_token':{'dst':'user_token'}},
                {'SECU_CODE':{'dst':'stock_code'}},
                {'TRD_ID':{'dst':'entrust_bs','dict':TRD_ID_bsflag_600140dict}},
                {'ORDER_PRICE':{'dst':'entrust_price'}},
                {'ORDER_QTY':{'dst':'entrust_amount'}},
                # {'TRD_ID':{'dst':'entrust_prop'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},
                {'netaddr':{'dst':'netaddr'}}
            ],
            'ans':
            [
                {'entrust_no':{'dst':'ORDER_ID'}}
            ]
        },
    # 理财委托
    '600141':
        {
            'dstfuncid': '332602',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    # 从票据中取机构号
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据取密码
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}}, # 密码类别


                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}}, # 交易类别
                    {'user_token': {'dst': 'user_token', 'default': ''}}, # 用户口令
                    {'BALANCE_CODE': {'dst': 'stock_code'}}, # 证券代码
                    {'ORDER_AMT': {'dst': 'entrust_balance'}},  # 委托金额

                    {'entrust_reference': {'dst': 'entrust_reference', 'default': ''}}, # 委托引用
                    {'EXTENSION_FLAG': {'dst': 'postpone_flag', 'default': '0'}}, # 自动展期标志
                    {'SECU_ACC': {'dst': 'stock_account'}}, # 证券账号
                    {'ask_date_back': {'dst': 'ask_date_back', 'default': ''}}, # 申请购回日期

                ],
            'ans':
                [
                    {'entrust_no': {'dst': 'ORDER_ID', 'functionPlugIn': 'functionPlugIn(getOrderId332602,entrust_no)'}}
                ]
        },
    '600410-330300':  # 证券代码信息查询
        {
            'dstfuncid': '330300',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'query_type': {'dst': 'query_type', 'default': '2'}},  # 操作分支机构
                    {'stock_code': {'dst': 'stock_code'}},  # 证券代码
                ],
            'ans':
                [
                    {'sell_unit': {'dst': 'REPURCHASE_START_AMT',
                                   'functionPlugIn': 'functionPlugIn(format_sell_unit,sell_unit)'}},

                ]
        },
    # 查询余额理财产品信息
    '600410':
        {
            'dstfuncid': '332600',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'stock_code': {'dst': 'stock_code', 'default': STOCK_CODE}}
                ],
            'ans':
                [
                    {'prod_begin_date': {'dst': 'OCCUR_DATE', 'functionPlugIn': 'functionPlugIn(GetToday332600,prod_begin_date)'}},
                    {'stock_code': {'dst': 'BALANCE_CODE'}},
                    {'stock_name': {'dst': 'BALANCE_NAME'}},
                    {'BALANCE_TYPE': {'dst': 'BALANCE_TYPE', 'default': '1'}},
                    {'bond_term': {'dst': 'BALANCE_TIME_LIMIT', 'dict': dict_tx_balance_time_limit}},  # 理财产品期限
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
                    {'postpone_flag': {'dst': 'AUTO_RENEWAL'}},
                    {'interest_cycle': {'dst': 'TRADE_DAYS'}},
                    {'expire_year_rate': {'dst': 'PURCHASE_RATE',
                                          'functionPlugIn': 'functionPlugIn(WSRateTrans,expire_year_rate)'}},  # 购买年化利率
                    {'preend_year_rate': {'dst': 'REPURCHASE_RATE',
                                          'functionPlugIn': 'functionPlugIn(WSRateTrans,preend_year_rate)'}},  # 提前回购利率

                    {'low_balance': {'dst': 'PURCHASE_START_AMT', 'functionPlugIn': 'functionPlugIn(ToFloat332600,low_balance)'}},
                    #{'sell_unit': {'dst': 'REPURCHASE_START_AMT', 'functionPlugIn': 'functionPlugIn(ToFloat332600,sell_unit)'}},

                    {'settle_start_date': {'dst': 'START_DATE'}},
                    {'real_end_date': {'dst': 'END_DATE'}},
                    {'settle_due_date': {'dst': 'FINISH_DATE'}},
                    {'enable_quota': {'dst': 'OVER_SELL', 'functionPlugIn': 'functionPlugIn(TansEnableQuota332600,enable_quota)'}},
                ]
        },
    '600143':  # 条件单
        {
            'dstfuncid': '333002',
            'req':
                [
                    {'COMPANY_ID': {'dst': 'COMPANY_ID'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号

                    {'op_branch_no':
                         {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#':
                         {'dst': 'op_entrust_way',
                                         'default': 'h'}},  # 操作方式
                    {'TCC':
                         {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},
                    {'branch_no':
                         {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(query_branch_no,ACCOUNT)'}},
                    {'ACCOUNT':
                         {'dst': 'client_id', }},
                    {'TRD_PWD':
                         {'dst': 'password', 'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,TRD_PWD,USER_ID_CLS)'} },# 交易密码
                    {'password_type':
                         {'dst': 'password_type', 'default': '2'}},
                    {'SECU_CODE':
                         {'dst': 'stock_code'}},
                    {'ORDER_QTY':
                         {'dst': 'entrust_amount'}},
                    {'ORDER_PRICE':
                         {'dst': 'entrust_price'}},
                    {'entrust_prop':
                         {'dst':'entrust_prop', 'functionPlugIn': 'functionPlugIn(tran_entrust_prop,TRD_ID)'}},
                    {'TRD_ID':
                         {'dst': 'entrust_bs', 'dict': TRD_ID_bsflag_600140dict}},
                    {'user_token': {'dst': 'user_token'}},
                    {'funcid': {'dst': 'funcid', 'default': '333002'}},
                    {'registe_sure_flag': {'dst': 'registe_sure_flag'}},
                    # 站点地址
                    # 从票据中取机构号
                    {'custid': {'dst': 'custid',
                                'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'MARKET': {'dst': 'exchange_type',
                                'dict': dict_market_wzq_to_T2sdk}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},
                    {'netaddr': {'dst': 'netaddr'}}
                ],
            'ans':
                [
                    {'entrust_no': {'dst': 'ORDER_ID'}}
                ]
        },
    '600140A': #盘后下单
        {
            'dstfuncid':'332702',
            'req':
            [
                {'COMPANY_ID':{'dst':'COMPANY_ID'}},
                {'funcid':{'dst':'funcid','default':'332702'}},
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'registe_sure_flag': {'dst': 'registe_sure_flag'}},
                {'TCC':{'dst':'op_station','functionPlugIn':'functionPlugIn(tranOpStation,TCC)'}},  #站点地址
                {'branch_no':{'dst':'branch_no','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'ACCOUNT':{'dst':'fund_account'}}, #资金帐号
                {'password_type':{'dst':'password_type','default':'2'}},
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'user_token':{'dst':'user_token'}},
                {'SECU_CODE':{'dst':'stock_code'}},
                {'TRD_ID':{'dst':'entrust_bs','dict':TRD_ID_bsflag_600140dict}},
                {'ORDER_PRICE':{'dst':'entrust_price'}},
                {'ORDER_QTY':{'dst':'entrust_amount'}},
                {'entrust_prop':{'dst':'entrust_prop','default':'PFP'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},
                {'netaddr':{'dst':'netaddr'}}
            ],
            'ans':
            [
                {'entrust_no':{'dst':'ORDER_ID'}}
            ]
        },
        '600150A': #盘后委托撤单
        {
            'dstfuncid':'332703',
            'req':
            [
                {'COMPANY_ID':{'dst':'COMPANY_ID'}},
                {'SESSION':{'dst':'SESSION'}},
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'registe_sure_flag': {'dst': 'registe_sure_flag'}},
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no':{'dst':'branch_no','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'custid':{'dst':'custid','client_id':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'fundid_tmp': {'dst': 'fund_account', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},# 从票据取资金帐号
                # {'ACCOUNT':{'dst':'fund_account'}}, #资金帐号
                {'password_type':{'dst':'password_type','default':'2'}},
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'user_token':{'dst':'user_token'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},
                {'ORDER_ID':{'dst':'entrust_no'}}
            ],
            'ans':
            [
                {'entrust_no':{'dst':'ORDER_ID'}}
            ]
        },
    '600150': #撤单
        {
            'dstfuncid':'333017',
            'req':
            [
                {'COMPANY_ID':{'dst':'COMPANY_ID'}},
                {'SESSION':{'dst':'SESSION'}},
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'registe_sure_flag': {'dst': 'registe_sure_flag'}},
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no':{'dst':'branch_no','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'custid':{'dst':'custid','client_id':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'fundid_tmp': {'dst': 'fund_account', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},# 从票据取资金帐号
                # {'ACCOUNT':{'dst':'fund_account'}}, #资金帐号
                {'password_type':{'dst':'password_type','default':'2'}},
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'user_token':{'dst':'user_token'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},
                {'ORDER_ID':{'dst':'entrust_no'}}
            ],
            'ans':
            [
                {'entrust_no':{'dst':'ORDER_ID'}}
            ]
        },
    '600161': #查询盘后业务当日委托
        {
            'dstfuncid':'849501',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                # {'sysnode_id':{'dst':'sysnode_id','default':'2'}},
                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                # {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'en_entrust_prop': {'dst': 'en_entrust_prop','default':'PFP'}},
                {'KEY_STR': {'dst': 'position_str'}},
                {'REC_COUNT': {'dst': 'request_num', 'default': '100'}},
                {'QRYFLAG_tmp': {'dst': 'sort_direction', 'default': '1'}},  # 查询方向


                #{'password_type': {'dst': 'password_type', 'default': '2'}},
                #{'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                # 从票据取密码
                #{'password': {'dst': 'password',
                #             'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
            ],
            'ans':
            [
                {'curr_time':{'dst':'ORDER_TIME','functionPlugIn':'functionPlugIn(TimeFormat,curr_time)'}},
                {'curr_date':{'dst':'ORDER_DATE'}},
                {'init_date':{'dst':'TRD_DATE'}},
                {'fund_account':{'dst':'USER_CODE'}},
                {'money_type':{'dst':'CURRENCY','default':'0'}},
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},
                {'stock_code':{'dst':'SECU_CODE'}},
                {'stock_name':{'dst':'SECU_NAME'}},
                {'entrust_price':{'dst':'PRICE','functionPlugIn':'functionPlugIn(FormatFloat,entrust_price,"3")'}},
                {'entrust_amount':{'dst':'QTY','functionPlugIn':'functionPlugIn(FormatFloat,entrust_amount,"0")'}},
                {'entrust_balance':{'dst':'ORDER_AMT','functionPlugIn':'functionPlugIn(FormatFloat,order_amt,"2")'}},
                {'business_amount':{'dst':'MATCHED_QTY','functionPlugIn':'functionPlugIn(FormatFloat,business_amount,"0")'}},
                {'business_price':{'dst':'MATCHED_PRICE','functionPlugIn':'wfunctionPlugIn(FormatFloat,business_price,"3")'}},
                {'withdraw_amount':{'dst':'WITHDRAWN_QTY','functionPlugIn':'functionPlugIn(FormatFloat,withdraw_amount,"0")'}},
                {'business_balance':{'dst':'MATCHED_AMT','functionPlugIn':'functionPlugIn(FormatFloat,business_balance,"2")'}},
                {'entrust_no': {'dst': 'ORDER_ID'}},
                {'stock_account':{'dst':'SECU_ACC'}},
                {'entrust_status':{'dst':'DCL_FLAG','dict':orderstats_to_DCL_FLAG_dict}},
                {'entrust_bs':{'dst':'TRD_ID','functionPlugIn':'functionPlugIn(JoinString,entrust_prop,entrust_bs)','dict':QueryBsflag_outdict}},
                # ----------------------------------------------
                {'fund_account': {'dst': 'ACCOUNT'}},  # 资金账号
                {'branch_no': {'dst': 'BRANCH'}},
                {'entrust_prop': {'dst': 'entrust_prop'}},
                {'orderid': {'dst': 'ORDER_TEMPID'}},
                {'seat':{'dst':'SEAT'}},
                {'entrust_type':{'dst':'IS_WITHDRAW','dict':CancelFlagTranfer_outdict}},
                {'ordergroup': {'dst': 'EXT_SERIAL_NO'}},
                # ------------------------------------------------
                {'position_str': {'dst': 'KEY_STR'}}
            ]
        },
        '600172':  # 查询盘后业务历史委托
        {
            'dstfuncid': '849107',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    # {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},
                    {'en_entrust_prop': {'dst': 'en_entrust_prop','default':'PFP'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    #{'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    # {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    # {'password': {'dst': 'password',
                    #              'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    # {'user_token': {'dst': 'user_token'}},

                    {'BEGIN_DATE': {'dst': 'begin_date'}},
                    {'END_DATE': {'dst': 'end_date'}},
                    #{'SECU_CODE': {'dst': 'stock_code'}},
                    #{'ORDER_ID': {'dst': 'ordersno'}},
                    {'EXT_SERIAL_NO': {'dst': 'ordergroup'}},
                    #{'bankcode': {'dst': 'bankcode'}},
                    {'QRYFLAG_tmp': {'dst': 'sort_direction', 'default': '1'}},  # 查询方向
                    {'REC_COUNT': {'dst': 'request_num', 'default': '50'}},  # 请求行数
                    {'KEY_STR': {'dst': 'position_str'}}
                ],
            'ans':
                [
                    {'init_date': {'dst': 'ORDER_DATE'}},  # 委托日期
                    {'entrust_time': {'dst': 'ORDER_TIME',
                                      'functionPlugIn': 'functionPlugIn(TimeFormat,entrust_time)'}},  # 委托时间
                    {'init_date': {'dst': 'TRD_DATE'}},
                    {'fund_account': {'dst': 'USER_CODE'}},
                    {'relation_name': {'dst': 'USER_NAME'}},
                    {'fund_account': {'dst': 'ACCOUNT'}},  # 资金账号
                    {'currency': {'dst': 'CURRENCY','default':'0'}},
                    {'branch_no': {'dst': 'BRANCH'}},
                    {'stock_account': {'dst': 'SECU_ACC'}},
                    {'entrust_bs': {'dst': 'TRD_ID', 'functionPlugIn':'functionPlugIn(JoinString,entrust_prop,entrust_bs)','dict': QueryBsflag_outdict}},
                    {'entrust_no': {'dst': 'BIZ_NO'}},
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
                    {'stock_name': {'dst': 'SECU_NAME'}},  # 证券名称
                    {'stock_code': {'dst': 'SECU_CODE'}},  # 证券代码
                    {'seat_no': {'dst': 'SEAT'}},  # 交易席位
                    {'entrust_price': {'dst': 'PRICE',
                                       'functionPlugIn': 'functionPlugIn(FormatFloat,entrust_price,"3")'}},  # 委托价格
                    {'entrust_amount': {'dst': 'QTY',
                                        'functionPlugIn': 'functionPlugIn(FormatFloat,entrust_amount,"0")'}},  # 数量
                    {'frozen_balance': {'dst': 'ORDER_FRZ_AMT'}},  # 委托冻结金额
                    {'entrust_type': {'dst': 'IS_WITHDRAW', 'dict': CancelFlagTranfer_outdict}},
                    {'report_time': {'dst': 'DCL_TIME',
                                     'functionPlugIn': 'functionPlugIn(TimeFormat,report_time)'}},  # 报盘时间
                    {'entrust_status': {'dst': 'DCL_FLAG', 'dict': orderstats_to_DCL_FLAG_dict}},  # 委托状态
                    {'business_amount': {'dst': 'MATCHED_QTY',
                                         'functionPlugIn': 'functionPlugIn(FormatFloat,business_amount,"0")'}},
                    {'business_price':{'dst':'MATCHED_PRICE',
                                       'functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},
                    {'withdraw_amount': {'dst': 'WITHDRAWN_QTY',
                                         'functionPlugIn': 'functionPlugIn(FormatFloat,withdraw_amount,"0")'}},
                    {'business_balance': {'dst': 'MATCHED_AMT'}},  # 成交金额
                    {'ext_serial_no': {'dst': 'EXT_SERIAL_NO'}},  # 外部流水号
                    {'entrust_no': {'dst': 'ORDER_ID'}},  # 合同序号
                    {'position_str': {'dst': 'KEY_STR'}}  # 定位串
                ]
        },
    # 加薪宝等余额理财产品赎回
    '600174':
        {
            'dstfuncid': '332604',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    # 从票据中取机构号
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据取密码
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},  # 密码类别

                    {'REDEEM_AMT': {'dst': 'entrust_balance'}},  # 委托金额
                    {'serial_no': {'dst': 'serial_no'}},  # 流水序号 此接口特殊处理
                    {'entrust_date': {'dst': 'entrust_date'}},  # 流水序号 此接口特殊处理
                    {'entrust_reference': {'dst': 'entrust_reference', 'default': ''}},  # 委托引用
                ],
            'ans':
                [
                    {'entrust_no': {'dst': 'ORDER_ID'}}
                ]
        },

    '600160': #查询当日委托
        {
            'dstfuncid':'333101',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},  # 市场
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},
                {'SECU_CODE': {'dst': 'stock_code'}},
                {'KEY_STR': {'dst': 'position_str'}},
                {'REC_COUNT': {'dst': 'request_num', 'default': '50'}},
            ],
            'ans':
            [
                {'entrust_time':{'dst':'ORDER_TIME','functionPlugIn':'functionPlugIn(TimeFormat,entrust_time)'}},
                {'entrust_date':{'dst':'ORDER_DATE'}},
                {'init_date':{'dst':'TRD_DATE'}},
                {'fund_account':{'dst':'USER_CODE'}},
                {'custname':{'dst':'USER_NAME'}},
                {'fund_account':{'dst':'ACCOUNT'}},
                {'money_type':{'dst':'CURRENCY'}},
                {'orgid': {'dst': 'BRANCH'}},
                {'stock_account':{'dst':'SECU_ACC'}},
                {'entrust_prop': {'dst': 'entrust_prop'}},
                {'entrust_bs':{'dst':'TRD_ID','functionPlugIn':'functionPlugIn(JoinString,entrust_prop,entrust_bs)','dict':QueryBsflag_outdict}},
                {'entrust_no': {'dst': 'ORDER_ID'}},
                {'orderid': {'dst': 'ORDER_TEMPID'}},
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},
                {'stock_code':{'dst':'SECU_CODE'}},
                {'stock_name':{'dst':'SECU_NAME'}},
                {'seat':{'dst':'SEAT'}},
                {'entrust_price':{'dst':'PRICE','functionPlugIn':'functionPlugIn(FormatFloat,entrust_price,"3")'}},
                {'entrust_amount':{'dst':'QTY','functionPlugIn':'functionPlugIn(FormatFloat,entrust_amount,"0")'}},
                {'order_amt':{'dst':'ORDER_AMT','functionPlugIn':'functionPlugIn(FormatFloat,order_amt,"2")'}},
                {'entrust_type':{'dst':'IS_WITHDRAW','dict':CancelFlagTranfer_outdict}},
                {'entrust_status':{'dst':'DCL_FLAG','dict':orderstats_to_DCL_FLAG_dict}},
                {'business_amount':{'dst':'MATCHED_QTY','functionPlugIn':'functionPlugIn(FormatFloat,business_amount,"0")'}},
                {'withdraw_amount':{'dst':'WITHDRAWN_QTY','functionPlugIn':'functionPlugIn(FormatFloat,withdraw_amount,"0")'}},
                {'business_balance':{'dst':'MATCHED_AMT','functionPlugIn':'functionPlugIn(FormatFloat,business_balance,"2")'}},
                {'business_price':{'dst':'MATCHED_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},
                {'ordergroup': {'dst': 'EXT_SERIAL_NO'}},
                {'position_str': {'dst': 'KEY_STR'}}
            ]
        },
    '600170':  # 历史委托查询
        {
            'dstfuncid': '849103',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                    {'BEGIN_DATE': {'dst': 'start_date'}},
                    {'END_DATE': {'dst': 'end_date'}},
                    {'SECU_CODE': {'dst': 'stock_code'}},
                    {'ORDER_ID': {'dst': 'ordersno'}},
                    {'EXT_SERIAL_NO': {'dst': 'ordergroup'}},
                    {'bankcode': {'dst': 'bankcode'}},
                    {'QRYFLAG_tmp': {'dst': 'qryflag', 'default': '1'}},  # 查询方向
                    {'REC_COUNT': {'dst': 'request_num', 'default': '50'}},  # 请求行数
                    {'KEY_STR': {'dst': 'position_str'}}
                ],
            'ans':
                [
                    {'curr_date': {'dst': 'ORDER_DATE'}},  # 委托日期
                    {'entrust_time': {'dst': 'ORDER_TIME',
                                      'functionPlugIn': 'functionPlugIn(TimeFormat,entrust_time)'}},  # 委托时间
                    {'entrust_date': {'dst': 'TRD_DATE'}},
                    {'fund_account': {'dst': 'USER_CODE'}},
                    {'user_name': {'dst': 'USER_NAME'}},
                    {'fund_account': {'dst': 'ACCOUNT'}},  # 资金账号
                    {'currency': {'dst': 'CURRENCY'}},
                    {'orgid': {'dst': 'BRANCH'}},
                    {'stock_account': {'dst': 'SECU_ACC'}},
                    {'entrust_bs':{'dst':'TRD_ID','functionPlugIn':'functionPlugIn(JoinString,entrust_prop,entrust_bs)','dict':QueryBsflag_outdict}},
                    {'order_id': {'dst': 'BIZ_NO'}},
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
                    {'stock_name': {'dst': 'SECU_NAME'}},  # 证券名称
                    {'stock_code': {'dst': 'SECU_CODE'}},  # 证券代码
                    {'seat_no': {'dst': 'SEAT'}},  # 交易席位
                    {'entrust_price': {'dst': 'PRICE',
                                       'functionPlugIn': 'functionPlugIn(FormatFloat,entrust_price,"3")'}},  # 委托价格
                    {'entrust_amount': {'dst': 'QTY',
                                        'functionPlugIn': 'functionPlugIn(FormatFloat,entrust_amount,"0")'}},  # 数量
                    {'frozen_balance': {'dst': 'ORDER_FRZ_AMT'}},  # 委托冻结金额
                    {'entrust_type': {'dst': 'IS_WITHDRAW', 'dict': CancelFlagTranfer_outdict}},
                    {'report_time': {'dst': 'DCL_TIME'}},  # 报盘时间
                    {'entrust_status': {'dst': 'DCL_FLAG', 'dict': orderstats_to_DCL_FLAG_dict}},  # 委托状态
                    {'business_amount': {'dst': 'MATCHED_QTY',
                                         'functionPlugIn': 'functionPlugIn(FormatFloat,business_amount,"0")'}},
                    {'withdraw_amount': {'dst': 'WITHDRAWN_QTY',
                                         'functionPlugIn': 'functionPlugIn(FormatFloat,withdraw_amount,"0")'}},
                    {'business_balance': {'dst': 'MATCHED_AMT'}},  # 成交金额
                    {'ext_serial_no': {'dst': 'EXT_SERIAL_NO'}},  # 外部流水号
                    {'entrust_no': {'dst': 'ORDER_ID'}},  # 合同序号
                    {'position_str': {'dst': 'KEY_STR'}}  # 定位串
                ]
        },
        '600180A': #当日综合业务成交查询
        {
            'dstfuncid':'849502',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                # {'password_type': {'dst': 'password_type', 'default': '2'}},
                # {'sysnode_id':{'dst':'sysnode_id','default':'2'}},
                {'en_entrust_prop': {'dst': 'en_entrust_prop','default':'PFP'}},
                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                # {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                # {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                # {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                # {'password': {'dst': 'password'}},
                # {'user_token':{'dst':'user_token'}},
                {'SECU_CODE': {'dst': 'stock_code'}},
                {'KEY_STR': {'dst': 'position_str'}},
                {'QRYFLAG_tmp': {'dst': 'sort_direction', 'default': '1'}},  # 查询方向
                {'REC_COUNT': {'dst': 'request_num', 'default': '50'}}
            ],
            'ans':
            [
                {'fund_account': {'dst': 'ACCOUNT'}},  # 资金账号
                {'init_date':{'dst':'TRD_DATE'}},		    #交易日期
                {'undefined':{'dst':'CURRENCY','default':'0'}},	#货币
                {'stock_account':{'dst':'SECU_ACC'}},		    #股东代码
                # {'entrust_prop': {'dst': 'entrust_prop'}},
                {'entrust_bs':{'dst':'TRD_ID','functionPlugIn':'functionPlugIn(JoinString,entrust_prop,entrust_bs)','dict':QueryBsflag_outdict}},
                {'entrust_no':{'dst':'ORDER_ID'}},	    #合同序号
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},		    #交易市场
                {'stock_name':{'dst':'SECU_NAME'}},	    #证券名称
                {'stock_code':{'dst':'SECU_CODE'}},            #证券代码
                {'entrust_price':{'dst':'PRICE','functionPlugIn':'functionPlugIn(FormatFloat,entrust_price,"3")'}},		    #价格
                {'entrust_amount':{'dst':'QTY','functionPlugIn':'functionPlugIn(FormatFloat,entrust_amount,"0")'}}, 		    #数量
                {'real_type':{'dst':'MATCH_TYPE','functionPlugIn':'functionPlugIn(JoinString,real_type,real_status)','dict':dict_hs_match_type}}, #成交类型
                {'business_time':{'dst':'MATCHED_TIME','functionPlugIn':'functionPlugIn(TimeFormat,business_time)'}}, 	    #成交时间
                {'business_id':{'dst':'MATCHED_SN'}}, 	    #成交序号
                {'business_price':{'dst':'MATCHED_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},     #成交价格
                {'business_amount':{'dst':'MATCHED_QTY','functionPlugIn':'functionPlugIn(FormatFloat,business_amount,"0")'}}, 	    #成交数量
                {'business_balance':{'dst':'MATCHED_AMT'}}, 	    #成交金额
                {'position_str':{'dst':'KEY_STR'}} 		    #定位串
            ]
        },
    '600180': #当日成交查询
        {
            'dstfuncid':'333119',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                # {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'password': {'dst': 'password'}},
                {'user_token':{'dst':'user_token'}},
                {'SECU_CODE': {'dst': 'stock_code'}},
                {'KEY_STR': {'dst': 'position_str'}},
                {'REC_COUNT': {'dst': 'request_num', 'default': '50'}}
            ],
            'ans':
            [
                {'fund_account': {'dst': 'ACCOUNT'}},  # 资金账号
                {'date':{'dst':'TRD_DATE'}},		    #交易日期
                {'undefined':{'dst':'CURRENCY','default':'0'}},	#货币
                {'stock_account':{'dst':'SECU_ACC'}},		    #股东代码
                # {'entrust_prop': {'dst': 'entrust_prop'}},
                {'entrust_bs':{'dst':'TRD_ID','functionPlugIn':'functionPlugIn(JoinString,entrust_prop,entrust_bs)','dict':QueryBsflag_outdict}},
                {'entrust_no':{'dst':'ORDER_ID'}},	    #合同序号
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},		    #交易市场
                {'stock_name':{'dst':'SECU_NAME'}},	    #证券名称
                {'stock_code':{'dst':'SECU_CODE'}},            #证券代码
                {'orderprice':{'dst':'PRICE','functionPlugIn':'functionPlugIn(FormatFloat,orderprice,"3")'}},		    #价格
                {'orderqty':{'dst':'QTY','functionPlugIn':'functionPlugIn(FormatFloat,orderqty,"0")'}}, 		    #数量
                {'real_type':{'dst':'MATCH_TYPE','functionPlugIn':'functionPlugIn(JoinString,real_type,real_status)','dict':dict_hs_match_type}}, #成交类型
                {'business_time':{'dst':'MATCHED_TIME','functionPlugIn':'functionPlugIn(TimeFormat,business_time)'}}, 	    #成交时间
                {'business_id':{'dst':'MATCHED_SN'}}, 	    #成交序号
                {'business_price':{'dst':'MATCHED_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},     #成交价格
                {'business_amount':{'dst':'MATCHED_QTY','functionPlugIn':'functionPlugIn(FormatFloat,business_amount,"0")'}}, 	    #成交数量
                {'business_balance':{'dst':'MATCHED_AMT'}}, 	    #成交金额
                {'position_str':{'dst':'KEY_STR'}} 		    #定位串
            ]
        },
    '600190': #历史成交明细查询
        {
            'dstfuncid':'339304',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},

                # {'CURRENCY': {'dst': 'moneytype'}},
                {'SECUID': {'dst': 'stock_code'}},  # 股东代码
                {'BEGIN_DATE':{'dst':'start_date'}},
                {'END_DATE':{'dst':'end_date'}},
                # {'FUNDID':{'dst':'fundid'}},
                # {'default0':{'dst':'printflag','default':'0'}},
                # {'default1':{'dst':'qryflag','default':'1'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'entrust_no': {'dst': 'ORDER_ID'}},  # 合同序号
                {'stock_code': {'dst': 'SECU_CODE'}},
                {'stock_name':{'dst':'SECU_NAME'}},
                # {'moneytype': {'dst': 'CURRENCY'}},
                {'entrust_bs':{'dst':'TRD_ID','functionPlugIn':'functionPlugIn(JoinString,"0",entrust_bs)','dict':QueryBsflag_outdict}},
                {'occur_amount': {'dst': 'MATCHED_QTY','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},
                {'business_balance': {'dst': 'MATCHED_AMT'}},
                {'occur_balance': {'dst': 'SETT_AMT'}},
                {'fare0': {'dst': 'FEE_SXF'}},#手续费
                {'fare1': {'dst': 'FEE_YHS'}},#印花税
                {'fare2': {'dst': 'FEE_GHF'}},#过户费
                {'fare3': {'dst': 'FEE_QSF'}},#清算费
                {'farex': {'dst': 'FEE_JYGF'}},#交易规费
                {'exchange_fare0': {'dst': 'FEE_JSF'}},#经手费
                {'exchange_fare3': {'dst': 'FEE_ZGF'}},#证管费
                {'exchange_fare6': {'dst': 'FEE_QTF'}},#其他费
                {'feefront': {'dst': 'FEEFRONT'}},#前台费用
                {'position_str': {'dst': 'KEY_STR'}},
                {'stock_account':{'dst':'SECU_ACC'}},
                {'init_date': {'dst': 'MATCHED_DATE'}},
                {'business_time':{'dst':'MATCHED_TIME','functionPlugIn':'functionPlugIn(TimeFormat,business_time)'}}, #时间格式化
                {'business_price':{'dst':'MATCHED_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},
                {'business_id': {'dst': 'MATCHED_SN'}},  # 成交序号
                {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
            ]
        },
    '600200': #持仓查询,原dstfuncid333050
        {
            'dstfuncid':'333120',
            'req':
            [
                {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},    # 从票据中取机构号
                {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},# 从票据取密码
                {'password': {'dst': 'password',
                              'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'CURRENCY': {'dst': 'money_type'}},
                {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},  # 市场
                {'SECU_CODE':{'dst':'stkcode'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'money_type':{'dst':'CURRENCY'}},
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},
                {'stock_account':{'dst':'SECU_ACC'}},
                {'stock_name':{'dst':'SECU_NAME'}},
                {'stock_code':{'dst':'SECU_CODE'}},

                {'orgid':{'dst':'BRANCH'}},
                {'current_amount':{'dst':'SHARE_QTY','functionPlugIn':'functionPlugIn(FormatFloat,current_amount,"0")'}},
                {'begin_amount':{'dst':'SHARE_BLN'}}, #股份余额
                {'enable_amount':{'dst':'SHARE_AVL','functionPlugIn':'functionPlugIn(FormatFloat,enable_amount,"0")'}},
                {'cost_price':{'dst':'CURRENT_COST','functionPlugIn':'functionPlugIn(FormatFloat,cost_price,"3")'}},
                {'market_value':{'dst':'MKT_VAL','functionPlugIn':'functionPlugIn(FormatFloat,market_value,"2")'}},
                {'last_price':{'dst':'MKT_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,last_price,"3")'}}, #现价
                {'income_balance':{'dst':'INCOME_AMT','functionPlugIn':'functionPlugIn(FormatFloat,income_balance,"2")'}}, #参考盈亏
                {'settrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}},
                {'position_str': {'dst': 'KEY_STR'}}
            ]
        },
    '600210':  # 银证转入
        {
            'dstfuncid': '332200',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'TCC':{'dst':'op_station','functionPlugIn':'functionPlugIn(tranOpStation,TCC)'}},  #站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, # 从票据取密码
                    {'password': {'dst': 'password', 'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                    {'CURRENCY': {'dst': 'money_type', 'default': '0'}},
                    {'direction1#': {'dst': 'transfer_direction', 'default': '1'}},  # 银行转证券
                    {'ACC_PWD': {'dst': 'fund_password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,ACC_PWD,USER_ID_CLS)'}},
                    {'EXT_ACC_PWD': {'dst': 'bank_password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,EXT_ACC_PWD,USER_ID_CLS)'}},
                    {'CPTL_AMT': {'dst': 'occur_balance'}},
                    {'EXT_INST': {'dst': 'bank_no', 'dict': dict_HLBkCode}},  # 银行入参字典转换
                    # {'EXT_SERIAL_NO': {'dst': 'extsno'}}
                ],
            'ans':
                [
                    {'entrust_no': {'dst': 'SERIAL_NO'}}  # 券商流水号
                ]
        },
    '600220':  # 银证转出
        {
            'dstfuncid': '332200',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'fund_account': {'dst': 'fund_account', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},# 从票据取资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, # 从票据取密码
                    {'password': {'dst': 'password', 'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},


                    {'CURRENCY': {'dst': 'money_type'}},
                    {'banktrantype#': {'dst': 'transfer_direction', 'default': '2'}},  # 证券转银行
                    {'ACC_PWD': {'dst': 'fund_password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,ACC_PWD,USER_ID_CLS)'}},
                    {'EXT_ACC_PWD': {'dst': 'bank_password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,EXT_ACC_PWD,USER_ID_CLS)'}},
                    {'CPTL_AMT': {'dst': 'occur_balance'}},
                    {'EXT_INST': {'dst': 'bank_no', 'dict': dict_HLBkCode}},  # 银行入参字典转换
                    {'EXT_SERIAL_NO': {'dst': 'extsno'}}  # 外部流水号
                ],
            'ans':
                [
                    {'entrust_no': {'dst': 'SERIAL_NO'}}  # 券商流水号
                ]
        },
    '600230': #银证转账流水查询
        {
            'dstfuncid':'410608',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'410608'}},
                {'CUSTID':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'ORGID':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                {'BEGIN_DATE':{'dst':'strdate'}},
                {'END_DATE':{'dst':'enddate'}},
                {'TRDPWDBYCUSTID':{'dst':'trdpwd'}},
                {'netaddr':{'dst':'netaddr'}},
                {'ORGID':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext#':{'dst':'ext','default':'0'}},

                {'MARKET':{'dst':'market'}},
                {'COMPANY_ID':{'dst':'COMPANYID'}},
                {'FUNDID':{'dst':'fundid'}},
                {'SECUID': {'dst': 'secuid'}},
                {'CURRENCY': {'dst': 'moneytype'}},
                {'EXT_SERIAL_NO': {'dst': 'extsno'}},
                {'SERIAL_NO': {'dst': 'sno'}}
            ],
            'ans':
            [
                {'operdate':{'dst':'TRAN_DATE'}},
                {'opertime':{'dst':'TRAN_TIME'}},
                {'bankcode':{'dst':'EXT_INST','dict':BankcodeTransfer_outdict}},
                {'money_type':{'dst':'CURRENCY'}},
                {'fundeffect':{'dst':'CPTL_AMT'}},
                {'status':{'dst':'STATUS'}},
                {'sno':{'dst':'SERIAL_NO'}},
                {'bankmsgid':{'dst':'EXT_RET_CODE'}},
                {'bankmsg':{'dst':'EXT_RET_MSG'}},
                {'errormsg':{'dst':'EXT_RET_MSG'}},
                {'banktranid':{'dst':'BIZ_CODE','dict':Bnktranid_dict}}
            ]
        },
    '600270': #银证转账流水批量查询
        {
            'dstfuncid':'332250',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},

                {'EXT_INST': {'dst': 'bank_no', 'dict': BankcodeTransfer_indict}},
                {'CURRENCY':{'dst':'money_type','default':'0'}},
                {'action_in':{'dst':'action_in','default':'0'}},
                {'qryflag':{'dst':'qryflag','default':'1'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'init_date': {'dst': 'TRD_DATE'}},
                {'entrust_date':{'dst':'TRAN_DATE'}},
                {'entrust_time':{'dst':'TRAN_TIME','functionPlugIn':'functionPlugIn(InsertAndDeleString,entrust_time,":","2#4","0","8")'}},
                {'bank_no':{'dst':'EXT_INST','dict':BankcodeTransfer_outdict}},
                {'money_type':{'dst':'CURRENCY'}},
                {'occur_balance':{'dst':'CPTL_AMT','functionPlugIn':'functionPlugIn(FormatFloat,occur_balance,"2")'}},

                {'business_type':{'dst':'BIZ_CODE','dict':Bnktranid_dict}},
                {'entrust_status':{'dst':'STATUS','default':'2'}},
                {'entrust_no':{'dst':'SERIAL_NO'}},
                {'error_no_t':{'dst':'EXT_RET_CODE'}},
                {'bank_error_info':{'dst':'EXT_RET_MSG'}},
                {'fund_account':{'dst':'ACCOUNT'}},
                {'position_str':{'dst':'KEY_STR'}}
            ]
        },
    '600310': #银行余额查询
        {
            'dstfuncid':'410606',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'410606'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'fundid_tmp':{'dst':'fundid_tmp','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(SelectNoNULLPama,ACCOUNT,fundid_tmp)'}}, #选择非空的ACCOUNT
                {'CURRENCY':{'dst':'moneytype','default':'0'}},
                {'fundpwd':{'dst':'fundpwd'}},
                {'EXT_INST':{'dst':'bankcode','dict':BankcodeTransfer_indict}},  #银行字典转换
                {'bankpwd':{'dst':'bankpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,EXT_ACC_PWD,USER_ID_CLS,"","")'}}
            ],
            'ans':
            [
                {'sno':{'dst':'SERIAL_NO'}}
            ]
        },
    '600320':  # 今日申购数据查询
        {
            'dstfuncid': '330303',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址

                    {'sub_stock_type': {'dst': 'sub_stock_type'}},  # 证券子类
                    {'TRD_DATE':{'dst':'issue_date'}}  # 申购日期
                ],
            'ans':
                [
                    {'stock_name':{'dst':'SECU_NAME'}}, # 证券名称
                    {'stock_code':{'dst':'SECU_CODE'}}, # 证券代码
                    {'stock_code':{'dst':'SG_SECU_CODE'}}, # 申购代码
                    {'high_amount':{'dst':'SHARE_QTY','functionPlugIn':'functionPlugIn(FormatFloat,high_amount,"0")'}}, # 申购上限
                    {'last_price':{'dst':'SHARE_AVL','functionPlugIn':'functionPlugIn(FormatFloat,last_price,"3")'}}, # 申购价格
                    {'#issue_date':{'dst':'TRADE_DATE'}}, # 上市日期（后台不支持，留空）
                    {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},#交易市场
                    #{'stock_account': {'dst': 'PE_RATIO'}},  # 市盈率
                    {'stkcode_ctrlstr': {'dst': 'GEM_TYPE'}},
                    {'stock_type': {'dst': 'PURCH_TYPE', 'dict': dict_stktype_PURCH_TYPE_TO_WZQ}}  # 申购类型

                ]
        },
    '330300':  # 证券代码信息查询
        {
            'dstfuncid': '330300',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址

                    {'stock_code': {'dst': 'stock_code'}},  # 证券代码
                    # {'exchange_type': {'dst': 'MARKET', 'default': '1'}},  # 交易市场
                    # {'query_type': {'dst': 'query_type', 'default': '0'}},  # 查询类别 1查全部
                    # {'stock_type': {'dst': 'stock_type', 'default': 'E'}}  #E债券申购 4新股申购 两个类型沪市的申购代码和配好代码不一样，要做关联
                ],
            'ans':
                [
                    {'stock_name': {'dst': 'SECU_NAME'}},  # 证券名称
                    {'stock_code': {'dst': 'stock_code'}},  # 证券代码
                    {'relative_code': {'dst': 'relative_code'}},  # 相关代码
                    {'stock_type': {'dst': 'PURCH_TYPE', 'dict': dict_stktype_PURCH_TYPE_TO_WZQ}}  # 申购类型

                ]
        },
     '600330':  # 配号查询
        {
            'dstfuncid': '339301',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},# 从票据取密码
                    {'password': {'dst': 'password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                    # {'secuid_tmp':{'dst': 'secuid','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}},  # 从票据股东代码
                    # {'stkcode_tmp':{'dst':'stkcode'}},
                    {'QRYFLAG_tmp':{'dst':'qryflag','default':'0'}},
                    {'REC_COUNT':{'dst':'request_num','default':'100'}},
                    {'KEY_STR':{'dst':'position_str'}},
                    {'BEGIN_DATE':{'dst':'start_date'}},
                    {'END_DATE':{'dst':'end_date'}}
                ],
            'ans':
                [
                    {'fund_account': {'dst': 'ACCOUNT'}},
                    {'stock_name':{'dst':'SECU_NAME'}},
                    {'position_str':{'dst':'KEY_STR'}},
                    {'init_date':{'dst':'TRD_DATE'}},
                    {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},#交易市场
                    {'stock_account': {'dst': 'SECU_ACC'}},  # 股东代码
                    {'stock_code':{'dst':'SECU_CODE'}},
                    {'serial_no':{'dst':'ORDER_ID'}},
                    {'remark':{'dst':'ASSIGN_SN','functionPlugIn':'functionPlugIn(filter_key,remark)'}},#成交数量
                    {'init_date':{'dst':'ASSIGN_DATE'}},
                    {'occur_amount':{'dst':'ASSIGN_COUNT','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},#成交数量
                    {'stock_code':{'dst':'PURC_CODE'}}, # 申购代码

                ]
        },
    '600340':	#市值配售中签查询
        {
            'dstfuncid':'339302',
            'req':
            [
                {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},# 从票据取密码
                {'password': {'dst': 'password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token': {'dst': 'user_token'}},

                {'REC_COUNT': {'dst': 'request_num', 'default': '50'}},
                {'KEY_STR': {'dst': 'position_str'}},
                {'BEGIN_DATE':{'dst':'start_date'}},
                {'END_DATE':{'dst':'end_date'}}
            ],
            'ans':
            [
                {'fund_account': {'dst': 'ACCOUNT'}},
                {'position_str':{'dst':'KEY_STR'}},			#定位串
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},#交易市场
                {'stock_account':{'dst':'SECU_ACC'}},			#股东代码
                {'stock_code':{'dst':'SECU_CODE'}},		#证券代码
                {'stock_name':{'dst':'SECU_NAME'}},		#证券名称
                {'business_price':{'dst':'MATCH_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"2")'}},#配股价格
                {'occur_amount':{'dst':'MATCHED_QTY','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},#中签数量
                {'init_date':{'dst':'ASSIGN_DATE'}} 	#中签日期
            ]
        },

    '600340B':	#市值配售中签历史查询
        {
            'dstfuncid':'411560',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'411560'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'MARKET':{'dst':'market'}},
                {'custcert':{'dst':'custcert'}},
                {'BEGIN_DATE':{'dst':'begindate'}},
                {'END_DATE':{'dst':'enddate'}},
                {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                {'secuid_tmp':{'dst':'secuid','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}},  # 从票据股东代码
                {'stkcode_tmp':{'dst':'stkcode','default':''}},
                {'QRYFLAG_tmp':{'dst':'qryflag','default':'0'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEY_STR':{'dst':'position_str'}},
                {'issuetype':{'dst':'issuetype'}}
            ],
            'ans':
            [
                {'position_str':{'dst':'KEY_STR'}},			#定位串
                {'market':{'dst':'MARKET'}},			#交易市场
                {'secuid':{'dst':'SECU_ACC'}},			#股东代码
                {'stock_code':{'dst':'SECU_CODE'}},		#证券代码
                {'stock_name':{'dst':'SECU_NAME'}},		#证券名称
                {'matchprice':{'dst':'MATCH_PRICE'}}, 	#配股价格
                {'hitqty':{'dst':'MATCHED_QTY'}}, 		#中签数量
                {'matchdate':{'dst':'ASSIGN_DATE'}} 	#中签日期
            ]
        },
    '600360': #查询银行账号
        {
            'dstfuncid':'331157',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},

                {'CURRENCY':{'dst':'money_type','default':'0'}},
                {'EXT_INST':{'dst':'bank_no','dict':dict_HLBkCode}}
            ],
            'ans':
            [
                {'bkaccount_regflag':{'dst':'OPEN_FLAG','dict':Bkaccount_regflag_outdict}}, #
                {'bkaccount_status': {'dst': 'bkaccount_status'}}, #银行账户状态
                {'money_type':{'dst':'CURRENCY'}},
                {'bank_account':{'dst':'EXT_ACC'}},
                {'bank_no':{'dst':'EXT_INST','dict':BankcodeTransfer_outdict}},
                {'bank_name':{'dst':'EXT_INST_NAME'}},
                {'MAIN_FLAG': {'dst': 'MAIN_FLAG','default':'0'}},
                {'fund_account':{'dst':'ACCOUNT'}}
            ]
        },
    '332256':  # 查询主银行标志
        {
            'dstfuncid': '332256',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},
                ],
            'ans':
                [
                    {'main_flag': {'dst': 'main_flag'}},  # 主银行标志
                    {'money_type': {'dst': 'CURRENCY'}},
                    {'bank_account': {'dst': 'EXT_ACC'}},
                    {'bank_no': {'dst': 'EXT_INST', 'dict': BankcodeTransfer_outdict}},
                    {'bank_name': {'dst': 'EXT_INST_NAME'}},
                    {'fund_account': {'dst': 'ACCOUNT'}}
                ]
        },
    '600380': #查询新股申购市值
        {
            'dstfuncid':'333039',
            'req':
            [
                {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},# 从票据取密码
                {'password': {'dst': 'password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token': {'dst': 'user_token'}},

                {'custcert':{'dst':'custcert'}},
                {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},  # 市场
                # {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                {'secuid_tmp':{'dst': 'stock_account', 'functionPlugIn': 'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}},  # 从票据股东代码
                {'QRYFLAG_tmp':{'dst':'qryflag','default':'0'}},  #查询方向
                {'REC_COUNT':{'dst':'request_num','default':'50'}},    #请求行数
                {'KEY_STR':{'dst':'posstr'}}    #定位串
            ],
            'ans':
            [
                {'fund_account': {'dst': 'ACCOUNT'}},  # 资金帐号
                {'enable_amount':{'dst':'QYSL','functionPlugIn':'functionPlugIn(FormatFloat,enable_amount,"0")'}},#可申购额度（权益数量）
                {'exchange_type':{'dst':'MARKET','dict':dict_market_T2sdk_to_wzq}},#交易市场
                {'stock_account':{'dst':'SECU_ACC'}},	#股东代码
                {'stib_enable_quota': {'dst': 'QYSL_KCB', 'functionPlugIn': 'functionPlugIn(FormatFloat,stib_enable_quota,"0")'}},# 科创版申购额度
                {'posstr':{'dst':'KEY_STR'}}
            ]
        },
    '600390': #历史资金流水查询
        {
            'dstfuncid':'339300',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},


                {'deliver_type':{'dst':'deliver_type','default':'1'}},
                {'BEGIN_DATE': {'dst': 'start_date'}},
                {'END_DATE':{'dst':'end_date'}},
                {'#REC_COUNT':{'dst':'request_num'}},
                {'#KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'position_str':{'dst':'KEY_STR'}},#定位串
                {'init_date':{'dst':'MATCH_DATE'}}, #成交日期
                {'business_time':{'dst':'MATCH_TIME','functionPlugIn':'functionPlugIn(TimeFormat,business_time)'}}, #成交时间格式化
                {'entrust_bs':{'dst':'TRD_ID','dict':QueryBsflag_outdict}},#交易行为
                {'business_flag':{'dst':'TRD_BIZ_CODE'}},#交易业务代码
                {'business_name':{'dst':'TRD_NAME'}},#交易行为名称
                {'entrust_date': {'dst': 'TRD_DATE'}},  # 委托日期
                {'position_str': {'dst': 'TRD_FUND_ID'}},  # 资金流水
                {'fund_account':{'dst':'ACCOUNT'}},#
                {'money_type':{'dst':'CURRENCY'}},#
                {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},#
                {'stock_account': {'dst': 'SECU_ACC'}},#   股东代码
                {'occur_balance': {'dst': 'FUNDEFFECT'}},#资金发生数
                {'post_balance': {'dst': 'FUNDBAL'}},#资金余额
                {'stkbal': {'dst': 'SECU_BAL'}},#股票金额
                {'entrust_no': {'dst': 'ORDER_ID'}},#合同序号
                {'stock_code': {'dst': 'SECU_CODE'}},#证券代码
                {'stock_name': {'dst': 'SECU_NAME'}},#证券名称
                {'occur_amount': {'dst': 'MATCH_QTY','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},#成交数量
                {'business_price': {'dst': 'MATCH_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},#成交价格
				{'business_balance': {'dst': 'MATCH_AMT'}},#成交金额
                {'exchange_fare5': {'dst': 'FEE_JSXF'}},#净手续费
                {'fare0': {'dst': 'FEE_SXF'}},#手续费
                {'fare1': {'dst': 'FEE_YHS'}},#印花税
                {'fare2': {'dst': 'FEE_GHF'}},#过户费
                {'fare3': {'dst': 'FEE_QSF'}},#清算费
                {'farex': {'dst': 'FEE_JYGF'}},#交易规费
                {'exchange_fare0': {'dst': 'FEE_JSF'}},#经手费
                {'exchange_fare3': {'dst': 'FEE_ZGF'}},#证管费
                {'exchange_fare6': {'dst': 'FEE_QTF'}},#其他费
                {'feefront': {'dst': 'FEEFRONT'}},#前台费用
                # {'settrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}}#
            ]
        },
    '339305': #历史转账资金流水查询
        {
            'dstfuncid':'339305',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},


                {'deliver_type':{'dst':'deliver_type','default':'1'}},
                {'BEGIN_DATE': {'dst': 'start_date'}},
                {'END_DATE':{'dst':'end_date'}},
                {'#REC_COUNT':{'dst':'request_num'}},
                {'#KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'position_str':{'dst':'KEY_STR'}},#定位串
                {'business_date':{'dst':'MATCH_DATE'}}, #成交日期
                {'business_time':{'dst':'MATCH_TIME'}}, #成交时间  格式化,'functionPlugIn':'functionPlugIn(TimeFormat,business_time)'
                {'entrust_bs':{'dst':'TRD_ID','dict':QueryBsflag_outdict}},#交易行为
                {'business_flag':{'dst':'TRD_BIZ_CODE'}},#交易业务代码
                {'business_name':{'dst':'TRD_NAME'}},#交易行为名称
                {'entrust_date': {'dst': 'TRD_DATE'}},  # 委托日期
                {'position_str': {'dst': 'TRD_FUND_ID'}},  # 资金流水
                {'fund_account':{'dst':'ACCOUNT'}},#
                {'money_type':{'dst':'CURRENCY'}},#
                {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},#
                {'stock_account': {'dst': 'SECU_ACC'}},#   股东代码
                {'occur_balance': {'dst': 'FUNDEFFECT'}},#资金发生数
                {'post_balance': {'dst': 'FUNDBAL'}},#资金余额
                {'stkbal': {'dst': 'SECU_BAL'}},#股票金额
                {'entrust_no': {'dst': 'ORDER_ID'}},#合同序号
                {'stock_code': {'dst': 'SECU_CODE'}},#证券代码
                {'stock_name': {'dst': 'SECU_NAME'}},#证券名称
                {'occur_amount': {'dst': 'MATCH_QTY','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},#成交数量
                {'business_price': {'dst': 'MATCH_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},#成交价格
				{'occur_balance': {'dst': 'MATCH_AMT'}},#成交金额
                {'exchange_fare5': {'dst': 'FEE_JSXF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare5,"2")'}},#净手续费
                {'fare0': {'dst': 'FEE_SXF','functionPlugIn':'functionPlugIn(FormatFloat,fare0,"2")'}},#手续费
                {'fare1': {'dst': 'FEE_YHS','functionPlugIn':'functionPlugIn(FormatFloat,fare1,"2")'}},#印花税
                {'fare2': {'dst': 'FEE_GHF','functionPlugIn':'functionPlugIn(FormatFloat,fare2,"2")'}},#过户费
                {'fare3': {'dst': 'FEE_QSF','functionPlugIn':'functionPlugIn(FormatFloat,fare3,"2")'}},#清算费
                {'farex': {'dst': 'FEE_JYGF','functionPlugIn':'functionPlugIn(FormatFloat,farex,"2")'}},#交易规费
                {'exchange_fare0': {'dst': 'FEE_JSF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare0,"2")'}},#经手费
                {'exchange_fare3': {'dst': 'FEE_ZGF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare3,"2")'}},#证管费
                {'exchange_fare6': {'dst': 'FEE_QTF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare6,"2")'}},#其他费
                {'feefront': {'dst': 'FEEFRONT','functionPlugIn':'functionPlugIn(FormatFloat,feefront,"2")'}},#前台费用
            ]
        },
    '337497': #证券理财成交信息查询
        {
            'dstfuncid':'337497',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'TA_SN': {'dst': 'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                {'BALANCE_CODE': {'dst': 'prod_code'}},  # 产品代码

                {'BEGIN_DATE': {'dst': 'begin_date'}},
                {'END_DATE':{'dst':'end_date'}},
                {'#REC_COUNT':{'dst':'request_num'}},
                {'#KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'position_str':{'dst':'KEY_STR'}},#定位串
                {'init_date':{'dst':'MATCH_DATE', 'default': '********'}}, #成交日期
                {'business_time':{'dst':'MATCH_TIME','functionPlugIn':'functionPlugIn(TimeFormat,business_time)'}}, #成交时间格式化
                {'entrust_bs':{'dst':'TRD_ID','dict':QueryBsflag_outdict}},#交易行为
                {'business_flag':{'dst':'TRD_BIZ_CODE'}},#交易业务代码
                {'business_name':{'dst':'TRD_NAME'}},#交易行为名称
                {'entrust_date': {'dst': 'TRD_DATE'}},  # 委托日期
                {'position_str': {'dst': 'TRD_FUND_ID'}},  # 资金流水
                {'fund_account':{'dst':'ACCOUNT'}},#
                {'money_type':{'dst':'CURRENCY'}},#
                {'exchange_type': {'dst': 'MARKET', 'default': '10'}},#
                {'prodta_no': {'dst': 'TA_SN'}},#   TA编码
                {'stock_account': {'dst': 'SECU_ACC'}},#   股东代码
                {'occur_balance': {'dst': 'FUNDEFFECT'}},#资金发生数
                {'post_balance': {'dst': 'FUNDBAL'}},#资金余额
                {'stkbal': {'dst': 'SECU_BAL'}},#股票金额
                {'entrust_no': {'dst': 'ORDER_ID'}},#合同序号
                {'prod_code': {'dst': 'SECU_CODE'}},#证券代码，这里返回产品代码
                {'stock_name': {'dst': 'SECU_NAME'}},#证券名称
                {'occur_amount': {'dst': 'MATCH_QTY','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},#成交数量
                {'business_price': {'dst': 'MATCH_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},#成交价格
				{'business_balance': {'dst': 'MATCH_AMT'}},#成交金额
                {'exchange_fare5': {'dst': 'FEE_JSXF'}},#净手续费
                {'fare0': {'dst': 'FEE_SXF'}},#手续费
                {'fare1': {'dst': 'FEE_YHS'}},#印花税
                {'fare2': {'dst': 'FEE_GHF'}},#过户费
                {'fare3': {'dst': 'FEE_QSF'}},#清算费
                {'farex': {'dst': 'FEE_JYGF'}},#交易规费
                {'exchange_fare0': {'dst': 'FEE_JSF'}},#经手费
                {'exchange_fare3': {'dst': 'FEE_ZGF'}},#证管费
                {'exchange_fare6': {'dst': 'FEE_QTF'}},#其他费
                {'feefront': {'dst': 'FEEFRONT'}},#前台费用
                {'prodta_no': {'dst': 'TA_SN'}},# TA编号
                {'prod_code': {'dst': 'PROD_CODE'}},# TA编号
                # {'settrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}}#
            ]
        },
    '600391': #今日资金流水查询
        {
            'dstfuncid':'332257',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},   #操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}}, #操作方式
                {'password_type': {'dst': 'password_type', 'default': '2'}},
                {'sysnode_id':{'dst':'sysnode_id','default':'2'}},

                {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'MARKET':{'dst':'exchange_type','dict':dict_market_wzq_to_T2sdk}},
                {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                {'user_token':{'dst':'user_token'}},

                {'REC_COUNT':{'dst':'request_num'}},
                {'KEY_STR':{'dst':'position_str'}}
            ],
            'ans':
            [
                {'fund_account': {'dst': 'ACCOUNT'}},  # 资金帐号
                {'position_str':{'dst':'KEY_STR'}},#定位串
                {'init_date':{'dst':'MATCH_DATE'}}, #成交日期
                {'business_time':{'dst':'MATCH_TIME','functionPlugIn':'functionPlugIn(TimeFormat,business_time)'}}, #时间格式化
                {'entrust_bs':{'dst':'TRD_ID','dict':QueryBsflag_outdict}},#交易行为
                {'business_flag':{'dst':'TRD_BIZ_CODE'}},#交易业务代码
                {'business_name':{'dst':'TRD_NAME'}},#交易行为名称
                {'entrust_date': {'dst': 'TRD_DATE'}},  # 委托日期
                {'business_id': {'dst': 'TRD_FUND_ID'}},  # 资金流水
                {'fund_account':{'dst':'ACCOUNT'}},#
                {'money_type':{'dst':'CURRENCY'}},#
                {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},#
                {'stock_account': {'dst': 'SECU_ACC'}},#   股东代码
                {'occur_amount': {'dst': 'FUNDEFFECT'}},#资金发生数
                {'fundbal': {'dst': 'FUNDBAL'}},#资金余额
                {'stkbal': {'dst': 'SECU_BAL'}},#股票金额
                {'entrust_no': {'dst': 'ORDER_ID'}},#合同序号
                {'stock_code': {'dst': 'SECU_CODE'}},#证券代码
                {'stock_name': {'dst': 'SECU_NAME'}},#证券名称
                {'occur_amount': {'dst': 'MATCH_QTY','functionPlugIn':'functionPlugIn(FormatFloat,occur_amount,"0")'}},#成交数量
                {'business_price': {'dst': 'MATCH_PRICE','functionPlugIn':'functionPlugIn(FormatFloat,business_price,"3")'}},#成交价格
				{'business_balance': {'dst': 'MATCH_AMT','functionPlugIn':'functionPlugIn(FormatFloat,business_balance,"2")'}},#成交金额
                {'exchange_fare5': {'dst': 'FEE_JSXF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare5,"2")'}},#净手续费
                {'fare0': {'dst': 'FEE_SXF','functionPlugIn':'functionPlugIn(FormatFloat,fare0,"2")'}},#手续费
                {'fare1': {'dst': 'FEE_YHS','functionPlugIn':'functionPlugIn(FormatFloat,fare1,"2")'}},#印花税
                {'fare2': {'dst': 'FEE_GHF','functionPlugIn':'functionPlugIn(FormatFloat,fare2,"2")'}},#过户费
                {'fare3': {'dst': 'FEE_QSF','functionPlugIn':'functionPlugIn(FormatFloat,fare3,"2")'}},#清算费
                {'farex': {'dst': 'FEE_JYGF','functionPlugIn':'functionPlugIn(FormatFloat,farex,"2")'}},#交易规费
                {'exchange_fare0': {'dst': 'FEE_JSF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare0,"2")'}},#经手费
                {'exchange_fare3': {'dst': 'FEE_ZGF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare3,"2")'}},#证管费
                {'exchange_fare6': {'dst': 'FEE_QTF','functionPlugIn':'functionPlugIn(FormatFloat,exchange_fare6,"2")'}},#其他费
                {'feefront': {'dst': 'FEEFRONT','functionPlugIn':'functionPlugIn(FormatFloat,feefront,"2")'}},#前台费用
                # {'settrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}}#
            ]
        },
    '430016': #港股交割单查询
        {
            'dstfuncid':'430016',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'430016'}},
                {'CUSTID':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'ORGID':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'netaddr':{'dst':'netaddr'}},
                {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                {'ORGID': {'dst': 'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext#':{'dst':'ext','default':'0'}},
                {'custcert':{'dst':'custcert'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'COMPANY_ID':{'dst':'company_id'}},
                {'SECUID': {'dst': 'secuid'}},
                {'SECU_CODE': {'dst': 'stkcode'}},
                {'BEGIN_DATE':{'dst':'strdate'}},
                {'END_DATE':{'dst':'enddate'}},
                {'FUNDID':{'dst':'fundid'}},
                {'printflag#':{'dst':'printflag','default':'0'}},
                {'poststr':{'dst':'poststr'}},
                {'qryflag#':{'dst':'qryflag','default':'1'}},
                {'REC_COUNT':{'dst':'request_num'}},
                {'KEY_STR':{'dst':'poststr'}}
            ],
            'ans':
            [
                {'position_str':{'dst':'KEY_STR'}},
                {'cleardate':{'dst':'cleardate'}},
                {'bizdate':{'dst':'bizdate'}},
                {'bizdate':{'dst':'MATCH_DATE'}},
                {'ordertime':{'dst':'MATCH_TIME','functionPlugIn':'functionPlugIn(TimeFormat8To6,ordertime,"0","6")'}}, #时间格式化
                {'bsflag':{'dst':'TRD_ID','dict':QueryBsflag_outdict}},
                {'digestid':{'dst':'TRD_BIZ_CODE'}},
                {'digestname':{'dst':'TRD_NAME'}},
                {'custid':{'dst':'custid'}},
                {'custname':{'dst':'custname'}},
                {'orgid':{'dst':'orgid'}},
                {'fundid':{'dst':'ACCOUNT'}},
                {'money_type':{'dst':'CURRENCY'}},
                {'market': {'dst': 'MARKET','dict':GGT_Market_outdict}},
                {'stock_account': {'dst': 'SECU_ACC'}},
                {'fundeffect': {'dst': 'FUNDEFFECT'}},
                {'fundbal': {'dst': 'FUNDBAL'}},
                {'stkbal': {'dst': 'SECU_BAL'}},
                {'orderid': {'dst': 'ORDER_ID'}},
                {'stkcode': {'dst': 'SECU_CODE'}},
                {'stkname': {'dst': 'SECU_NAME'}},
                {'bsflag': {'dst': 'TRD_NAME'}},
                {'matchqty': {'dst': 'MATCH_QTY'}},
                {'matchprice': {'dst': 'MATCH_PRICE'}},
                {'matchamt': {'dst': 'MATCH_AMT'}},
                {'fee_jsxf': {'dst': 'FEE_JSXF'}},
                {'fee_sxf': {'dst': 'FEE_SXF'}},
                {'fee_yhs': {'dst': 'FEE_YHS'}},
                {'fee_ghf': {'dst': 'FEE_GHF'}},
                {'fee_qsf': {'dst': 'FEE_QSF'}},
                {'fee_jygf': {'dst': 'FEE_JYGF'}},
                {'fee_jsf': {'dst': 'FEE_JSF'}},
                {'fee_zgf': {'dst': 'FEE_ZGF'}},
                {'fee_qtf': {'dst': 'FEE_QTF'}},
                {'feefront': {'dst': 'FEEFRONT'}},
                {'bankcode': {'dst': 'EXT_INST'}},
                {'stkeffect': {'dst': 'stkeffect'}},
                {'matchcode': {'dst': 'matchcode'}},
                {'settrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}}
            ]
        },
    '600402': #资金可取查询
        {
            'dstfuncid':'420507',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'420507'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'fundid_tmp':{'dst':'fundid_tmp','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(SelectNoNULLPama,ACCOUNT,fundid_tmp)'}}, #选择非空的ACCOUNT
                {'CURRENCY':{'dst':'moneytype','default':'0'}}
            ],
            'ans':
            [
                {'money_type':{'dst':'CURRENCY'}},
                {'maxdraw':{'dst':'DRAW_AMT'}},
                {'fundid':{'dst':'ACCOUNT'}}
            ]
        },
    '600403': #查询多个资产帐号用
        {
            'dstfuncid':'410502',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'410502'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'ACCOUNT':{'dst':'fundid'}},
                {'CURRENCY':{'dst':'moneytype','default':'0'}},
                {'remark':{'dst':'remark'}}
            ],
            'ans':
            [
                {'money_type':{'dst':'CURRENCY'}},
                {'custid':{'dst':'CUST_CODE'}},
                {'fundseq':{'dst':'ISMAIN','dict':MAIN_FUND_FLAG_outdict}},
                {'fundid':{'dst':'ACCOUNT'}}
            ]
        },
    '600405': #多资金帐户间转账
        {
            'dstfuncid':'410620',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'410620'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'OUT_ACCOUNT':{'dst':'outfundid'}},
                {'checkpwdflag':{'dst':'checkpwdflag','default':'1'}},
                {'fundpwd':{'dst':'outpassword','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,ACC_PWD,USER_ID_CLS,"kdencode",OUT_ACCOUNT)'}},
                {'IN_ACCOUNT':{'dst':'infundid'}},
                {'CURRENCY':{'dst':'moneytype','default':'0'}},
                {'CPTL_AMT':{'dst':'fundeffect'}},
                {'cashcheck':{'dst':'cashcheck','default':'0'}}
            ],
            'ans':
            [
                {'outsno':{'dst':'outsno'}},
                {'insno':{'dst':'insno'}}
            ]
        },
      '600042':  # 查询ST高风险权限状态
        {
            'dstfuncid': '331300',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},
                ],
            'ans':
                [
                    {'stock_account': {'dst': 'SECU_ACC'}},  # 股东代码
                    {'open_date': {'dst': 'ACCOUNT_DATE'}},  # 开户时间
                    {'holder_rights': {'dst': 'holder_rights'}},  # ST状态
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
                    {'main_flag': {'dst': 'MAIN_FLAG'}}  # 主股东账号标识
                ]
        },
    '600043':  # 更新风险测评
        {
            'dstfuncid': '331012',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password':{'dst':'password','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                    {'RIST_ASSESSMENT': {'dst': 'paper_answer','functionPlugIn':'functionPlugIn(GetGjzqSurveyInfo,RIST_ASSESSMENT)'}},    #风险测评结果
                    # {'SURVEY_COLS': {'dst': 'SURVEY_COLS','functionPlugIn':'functionPlugIn(GetHlzqSurveyInfo,RIST_ASSESSMENT,"3")'}},    #调查表栏目(复数)

                ],
            'ans':
                [
                    {'SURVEY_CLS': {'dst': 'SURVEY_CLS'}},  #调查表类别
                    {'invest_advice': {'dst': 'invest_advice'}},  #投资建议
                    {'paper_score': {'dst': 'SURVEY_SCORE'}},  #测评分数
                    {'corp_risk_level': {'dst': 'RATING_LVL', 'dict': HLZQ_RISK_TYPE_TO_WECHAT_dict}},  #评级级别
                    {'min_rank_flag': {'dst': 'min_rank_flag'}} #最低风险等级标识 0=非最低；返回1=最低
                ]
        },

      '600042B':  # 查询创业板权限开通状态
        {
            'dstfuncid': '410332',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410332'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'COMPANYID': {'dst': 'company_id'}},
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                    {'custcert': {'dst': 'custcert'}},
                    {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}},  # 资金账号
                    {'secuid': {'dst': 'secuid'}}
                ],
            'ans':
                [
                    {'secuid': {'dst': 'SECUID'}},  # 股东代码
                    {'securight': {'dst': 'STATUS', 'functionPlugIn': 'functionPlugIn(FindTheSubStr,securight,"X")'}},  # ST状态
                    {'market': {'dst': 'MARKET'}}  # 交易市场
                ]
        },

    '160109':  # 客户签署协议查询
        {
            'dstfuncid': 'L1160109',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L1160109'}},
                    {'F_OP_USER#-1': {'dst': 'F_OP_USER','default': '8888'}},
                    {'F_OP_ROLE#-1': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE#-1#': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL#-1': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION#-1': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_RUNTIME#-1': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_SUBSYS#-1': {'dst': 'F_SUBSYS', 'default': '1'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG'}},

                    {'ACCOUNT': {'dst': 'CUST_CODE'}},
                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE', 'default': '0A'}}
                ],
            'ans':
                [
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},  #
                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE'}},  #
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},  #
                    {'EFT_DATE': {'dst': 'EFT_DATE'}},  #
                    {'EXP_DATE': {'dst': 'EXP_DATE'}},  #
                    {'REMOTE_SYS': {'dst': 'REMOTE_SYS'}},
                    {'STKBD': {'dst': 'STKBD'}},
                    {'TRDACCT': {'dst': 'TRDACCT'}},
                    {'UPD_DATE': {'dst': 'UPD_DATE'}}
                ]
        },

    '600406': #外围客户交易帐户资金归集
        {
            'dstfuncid':'410621',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'410621'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'IN_ACCOUNT':{'dst':'fundid'}},
                {'CURRENCY':{'dst':'moneytype','default':'0'}},
                {'cashcheck':{'dst':'cashcheck','default':'0'}}
            ],
            'ans':
            [
                {'fundeffect':{'dst':'fundeffect'}},
                {'renum':{'dst':'renum'}}
            ]
        },
    '600040A':  #根据证件号码查询客户基本信息
        {
            'dstfuncid': '********',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '********'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'orgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'ID_TYPE': {'dst': 'ID_TYPE'}},
                    {'ID_CODE': {'dst': 'ID_CODE'}}
                ],
            'ans':
                [
                    {'STATUS': {'dst': 'STATUS'}},  # 开户状态
                    {'ID_NAME': {'dst': 'ID_NAME'}},  # 姓名
                    {'EDUCATION': {'dst': 'EDUCATION'}},  # 学历
                    {'M_TEL': {'dst': 'M_TEL'}}  # 开户时间
                ]
        },

    '600040':  # 用户信息查询
        {
            'dstfuncid': '331150',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号

                ],
            'ans':
                [
                    {'mobile_tel': {'dst': 'M_TEL'}},  # 手机号码
                    {'id_kind': {'dst': 'ID_TYPE', 'dict': dict_idtype_COUNTER_TO_WZQ}},  # 证件类型
                    {'id_no': {'dst': 'ID_CODE'}},  # 证件号码
                    {'organ_prop': {'dst': 'INT_ORG'}},  # 机构代码
                    {'id_begindate': {'dst': 'ID_BEG_DATE'}},  # 证件开始日期
                    {'id_enddate': {'dst': 'ID_EXP_DATE'}},  # 证件有效日期
                    {'open_date': {'dst': 'ACCOUNT_DATE'}},  # 开户时间
                    {'client_name': {'dst': 'ID_NAME'}},
                    {'fund_account': {'dst': 'ACCOUNT'}},
                    {'degree_code': {'dst': 'EDUCATION', 'dict': dict_HLZQ_EDUCATION_TO_WECHAT}}, #学历
                    {'client_status': {'dst': 'STATUS', 'dict': dict_client_status_TO_WZQ}}, #客户状态
                    {'profession_code': {'dst': 'OCCU_TYPE', 'dict': dict_GJZQ_OCCU_TYPE_TO_WECHAT}}   #职业
                ]
        },

    '600030A':  # 根据证件号码查询客户基本信息
        {
            'dstfuncid': '********',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '********'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'custcert': {'dst': 'custcert'}},
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},  # 证件类型
                    {'ID_CODE': {'dst': 'ID_CODE'}}  # 证件号码
                ],
            'ans':
                [
                    {'COMPANY_ID_tmp': {'dst': 'COMPANY_NAME', 'default': '华林证券'}},  # 公司名称
                    {'ORG_CODE': {'dst': 'BRANCH_CODE'}},  # 营业部代码
                    {'ORG_NAME': {'dst': 'BRANCH_NAME'}},  # 营业部名称
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},  # 资产账户
                    {'CUACCT_STATUS': {'dst': 'CUACCT_STATUS'}}  # 资产账户状态
                ]
        },

    '600030':  # 查询营业部信息
        {
            'dstfuncid': '330004',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},
                    {'input_content': {'dst': 'input_content', 'default': '1'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'account_content'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},# 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                ],
            'ans':
                [
                    {'COMPANY_NAME': {'dst': 'COMPANY_NAME'}},  #券商名称
                    {'branch_no': {'dst': 'BRANCH_CODE'}},    #营业部代码
                    {'STKBD': {'dst': 'BRANCH_NAME'}},    #营业部名称
                ]
        },
    '600060':  # 查询交易账户
        {
            'dstfuncid': '331300',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password', 'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},
                ],
            'ans':
                [
                    {'fund_account': {'dst': 'ACCOUNT'}},  # 资金帐号
                    {'exchange_type': {'dst': 'MARKET',
                                       'functionPlugIn': 'functionPlugIn(JoinString,holder_kind,exchange_type)',
                                       'dict': dict_market_T2sdk_to_wzq}},
                    {'stock_account': {'dst': 'SECU_ACC'}},
                    {'holder_status': {'dst': 'STATUS'}},
                    {'register': {'dst': 'TRD_STATUS'}},
                    {'holder_rights': {'dst': 'holder_rights'}},
                    {'KE_CHUANG_STATUS': {'dst': 'KE_CHUANG_STATUS'}},
                    {'BALANCE_SERVICE_STATUS': {'dst': 'BALANCE_SERVICE_STATUS'}},
                    {'SH_KZZ_STATUS': {'dst': 'SH_KZZ_STATUS'}},
                    {'SZ_KZZ_STATUS': {'dst': 'SZ_KZZ_STATUS'}},
                    {'GEM_STATUS': {'dst': 'GEM_STATUS'}},
                    {'GEM_FIX_STATUS': {'dst': 'GEM_FIX_STATUS'}},
                ]
        },
    '600061':  #股东查询
        {
            'dstfuncid': '410501',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410501'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                    {'fundid_tmp':{'dst':'fundid','functionPlugIn':'functionPlugIn(WSGetFundid,SESSION_JSON)'}}, #从票据取资金帐号
                    {'secuid_tmp':{'dst': 'secuid'}},  # 股东代码
                    {'QRYFLAG_tmp':{'dst':'qryflag','default':'1'}},
                    {'count':{'dst':'count','default':'10'}},
                    {'poststr':{'dst':'poststr'}}
                ],
            'ans':
                [
                    {'market':{'dst':'market'}},
                    {'secuid':{'dst':'secuid'}},
                    {'regflag':{'dst':'regflag'}},
                    {'opendate':{'dst':'opendate'}},
                    {'status':{'dst':'status'}}
                ]
        },
    '600070A':  # 查询客户开户状态
        {
            'dstfuncid': '99000291',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '99000291'}},
                    {'custid': {'dst': 'custid'}},
                    {'custorgid': {'dst': 'custorgid'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'ID_TYPE': {'dst': 'ID_TYPE'}},
                    {'ID': {'dst': 'ID_CODE'}}
                ],
            'ans':
                [
                    {'OPEN_DATE': {'dst': 'OPEN_DATE'}},  # 开户时间
                    {'INT_ORG': {'dst': 'INT_ORG'}},  # 机构代码
                    {'USER_CODE': {'dst': 'USER_CODE'}}  # 客户代码
                ]
        },

    '600070B':  # 股东查询
        {
            'dstfuncid': '99000265',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '99000265'}},
                    {'custid': {'dst': 'custid'}},
                    {'custorgid': {'dst': 'custorgid'}},
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'CUST_CODE': {'dst': 'CUST_CODE'}},
                    {'STKEX': {'dst': 'STKEX'}},
                    {'STKBD': {'dst': 'STKBD'}},
                    {'SECU_ACC': {'dst': 'TRDACCT'}}
                ],
            'ans':
                [
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},
                    {'INT_ORG': {'dst': 'INT_ORG'}},
                    {'STKBD': {'dst': 'STKBD'}},
                    {'TRDACCT': {'dst': 'TRDACCT'}},
                    {'TRDACCT_SN': {'dst': 'TRDACCT_SN'}},
                    {'TRDACCT_EXID': {'dst': 'TRDACCT_EXID'}},
                    {'TRDACCT_TYPE': {'dst': 'TRDACCT_TYPE'}},
                    {'TRDACCT_EXCLS': {'dst': 'TRDACCT_EXCLS'}},
                    {'TRDACCT_NAME': {'dst': 'TRDACCT_NAME'}},
                    {'TRDACCT_STATUS': {'dst': 'TRDACCT_STATUS'}},
                    {'TREG_STATUS': {'dst': 'TREG_STATUS'}},
                    {'BREG_STATUS': {'dst': 'BREG_STATUS'}},
                    {'STKPBU': {'dst': 'STKPBU'}},
                    {'FIRMID': {'dst': 'FIRMID'}},
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},
                    {'ID_CODE': {'dst': 'ID_CODE'}},
                    {'ID_ISS_AGCY': {'dst': 'ID_ISS_AGCY'}},
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},
                    {'OPEN_DATE': {'dst': 'OPEN_DATE'}},
                    {'STKEX': {'dst': 'STKEX'}},
                    {'CUST_CODE': {'dst': 'CUST_CODE'}}
                ]
        },

    '600080':  # 根据客户证件查询客户资金帐号
        {
            'dstfuncid': '********',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '********'}},
                    {'custid': {'dst': 'custid'}},
                    {'custorgid': {'dst': 'custorgid'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'ID_TYPE': {'dst': 'ID_TYPE'}},
                    {'ID': {'dst': 'ID_CODE'}}
                ],
            'ans':
                [
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},  # 客户代码
                    {'CUACCT_CODE': {'dst': 'ACCOUNT'}},  # 资金账号
                    {'CUST_STATUS': {'dst': 'CUST_STATUS'}} #客户状态
                ]
        },

    'KOW003':  # 根据客户证件查询客户资金帐号
        {
            'dstfuncid': 'KOW003',
            'req':
                [
                    {'USER_ID_CLS': {'dst': 'USER_ID_CLS'}},  # 用户标识类型
                    {'USER_ID': {'dst': 'USER_ID'}},  # 用户标识
                    {'COMPANY_ID': {'dst': 'COMPANY_ID'}},  # 券商代码
                    {'MARKETS': {'dst': 'MARKETS'}},  # 交易市场
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},  # 证件类型
                    {'ID_BEG_DATE': {'dst': 'ID_BEG_DATE'}},  # 发证日期
                    {'NATIONALITY': {'dst': 'NATIONALITY'}},  # 民族
                    {'ID_CODE': {'dst': 'ID_CODE'}},  # 证件号码
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},  # 证件有效日期
                    {'ID_ZIP_CODE': {'dst': 'ID_ZIP_CODE'}},  # 证件邮编
                    {'ID_ADDR': {'dst': 'ID_ADDR'}},  # 证件地址
                    {'ID_NAME': {'dst': 'ID_NAME'}},  # 姓名
                    {'CITIZENSHIP': {'dst': 'CITIZENSHIP'}},  # 国籍
                    {'SEX': {'dst': 'SEX'}},  # 性别
                    {'M_TEL': {'dst': 'M_TEL'}},  # 手机号码
                    {'BIRTHDAY': {'dst': 'BIRTHDAY'}},  # 生日
                    {'EDUCATION': {'dst': 'EDUCATION'}},  # 学历
                    {'EXT_INST': {'dst': 'EXT_INST', 'dict': dict_HLBkCode}},  # 银行代码
                    {'EXT_ACC': {'dst': 'EXT_ACC'}},  # 银行账号
                    {'EXT_ACC_PWD': {'dst': 'EXT_ACC_PWD'}},  # 银行密码
                    {'TRD_PWD': {'dst': 'TRD_PWD'}},  # 交易密码
                    {'CAPITAL_PWD': {'dst': 'CAPITAL_PWD'}},  # 资金密码
                    {'ZIP_CODE': {'dst': 'ZIP_CODE'}},  # 邮编
                    {'ADDRESS': {'dst': 'ADDRESS'}},  # 联系地址
                    {'EMAIL': {'dst': 'EMAIL'}},  # 电子邮箱
                    {'ID_IMG_1': {'dst': 'ID_IMG_1'}},  # 证件正面
                    {'ID_IMG_2': {'dst': 'ID_IMG_2'}},  # 证件反面
                    {'ID_IMG_3': {'dst': 'ID_IMG_3'}},  # 大头像
                    {'INT_ORG': {'dst': 'INT_ORG'}},  # 机构代码
                    {'ID_ISS_AGCY': {'dst': 'ID_ISS_AGCY'}},  # 证件发证机关
                    {'RIST_SCORE': {'dst': 'RIST_SCORE'}},  # 评测得分
                    {'RIST_NO': {'dst': 'RIST_NO'}},  # 评测试题编号
                    {'RIST_ASSESSMENT': {'dst': 'RIST_ASSESSMENT'}},  # 风险评测答题
                    {'OCCU_TYPE': {'dst': 'OCCU_TYPE'}},  # 职业类型
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},  # 发证过期日期
                    {'CHECK_FLAG': {'dst': 'CHECK_FLAG'}},  # OCR识别标志
                    {'CHECK_STR': {'dst': 'CHECK_STR'}},  # 人脸识别校验串
                    {'EDUCATION': {'dst': 'EDUCATION'}},  # 学历
                    {'FACE_FLAG': {'dst': 'FACE_FLAG'}},  # 人脸识别标志
                    {'OCCU_TYPE': {'dst': 'OCCU_TYPE'}},  # 职业
                    {'COMPANY': {'dst': 'COMPANY'}},  # 工作单位
                    {'JOB_TITLE': {'dst': 'JOB_TITLE'}},  # 职务
                    {'CREDIT_RECORD': {'dst': 'CREDIT_RECORD'}},  # 诚信记录
                    {'ACTUAL_CONTROLLER': {'dst': 'ACTUAL_CONTROLLER'}},  # 实际控制人
                    {'ACTUAL_RECEIPTOR': {'dst': 'ACTUAL_RECEIPTOR'}},  # 实际收益人
                    {'TAX_ID': {'dst': 'TAX_ID'}},  # 个人税收居民身份
                    {'SIGN_TYPE': {'dst': 'SIGN_TYPE'}},  # 签署适当性协议书
                    {'SIGN_DATE': {'dst': 'SIGN_DATE'}},  # 适当性协议签订日期
                    {'SIGN_TIME': {'dst': 'SIGN_TIME'}},  # 适当性协议签订时间
                    {'CREDIT_RECORD_OPTIONS': {'dst': 'CREDIT_RECORD_OPTIONS'}},  # 诚信记录选项
                    {'INCOME': {'dst': 'INCOME'}},
                    {'CHANNEL': {'dst': 'CHANNEL'}}
                ],
            'ans':
                [
                    {'MSG_CODE': {'dst': 'MSG_CODE'}},
                    {'MSG_TEXT': {'dst': 'MSG_TEXT'}}
                ]
        },

    '600360A':  # 查询指定客户银行账号
        {
            'dstfuncid': '410601',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410601'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'CURRENCY': {'dst': 'moneytype'}},  # 货币代码
                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    # 从票据取资金帐号
                    {'EXT_INST': {'dst': 'bankcode', 'dict': dict_HLBkCode}},  # 银行代码
                ],
            'ans':
                [
                    {'bankid': {'dst': 'EXT_ACC'}},
                    {'moneytype': {'dst': 'CURRENCY', 'default': '0'}},
                    {'bankcode': {'dst': 'EXT_INST', 'default': '0', 'dict': dict_Hualin_Security_To_Tencent}},
                    {'MAIN_FLAG_tmp': {'dst': 'MAIN_FLAG', 'default': '01'}},
                    {'OPEN_FLAG_tmp': {'dst': 'OPEN_FLAG', 'default': '1'}},
                ]
        },

    '600360B':  #查询客户开户日期
        {
            'dstfuncid': '410321',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410321'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},
                ],
            'ans':
                [
                    {'opendate': {'dst': 'opendate'}}  # 开户时间
                ]
        },
    '600360C':  # 历史资金流水查询
        {
            'dstfuncid': '411521',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '411521'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'BEGIN_DATE': {'dst': 'strdate'}},
                    {'END_DATE': {'dst': 'enddate'}},
                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    {'printflag#': {'dst': 'printflag', 'default': '0'}},
                    {'poststr': {'dst': 'poststr'}},
                    {'qryflag#': {'dst': 'qryflag', 'default': '0'}},
                    {'REC_COUNT': {'dst': 'count', 'default': '1'}},
                    {'KEY_STR': {'dst': 'poststr'}}
                ],
            'ans':
                [
                    {'orderid': {'dst': 'orderid'}},
                    {'matchqty': {'dst': 'matchqty'}}
                ]
        },
    '600360D':  # 历史资金流水查询
        {
            'dstfuncid': '410523',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410523'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'qryflag#': {'dst': 'qryflag', 'default': '0'}},
                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    {'REC_COUNT': {'dst': 'count', 'default': '1'}},
                    {'KEY_STR': {'dst': 'poststr'}}
                ],
            'ans':
                [
                    {'orderid': {'dst': 'orderid'}},
                    {'matchqty': {'dst': 'matchqty'}}
                ]
        },
    '700713':  # 锁定用于中签新股的资金
        {
            'dstfuncid': '480011',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '480011'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    {'matchdate': {'dst': 'matchdate'}}
                ],
            'ans':
                [
                    {'opersno': {'dst': 'opersno'}},
                    {'frzamt': {'dst': 'frzamt'}}
                ]
        },

    '700714':	#查询资金冻结状态
        {
            'dstfuncid':'411547',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'411547'}},
                {'LOGIN_CODE':{'dst':'custid'}},
                {'custorgid':{'dst':'custorgid'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'MARKET':{'dst':'market'}},
                {'custcert':{'dst':'custcert'}},
                {'fundid_tmp':{'dst':'fundid'}},
                {'secuid_tmp':{'dst':'secuid'}},
                {'stkcode_tmp':{'dst':'stkcode','default':''}},
                {'issuetype':{'dst':'issuetype'}}
            ],
            'ans':
            [
                {'frzamt':{'dst':'FRZ_AMT'}}			#预冻结金额

            ]
        },

    '601160': #港股查询当日委托
        {
            'dstfuncid':'430001',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'430001'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'MARKET':{'dst':'market','dict':dict_market_wzq_to_T2sdk}},
                {'FUNDID':{'dst':'fundid'}},
                {'SECUID':{'dst':'secuid'}}, #股东代码
                {'SECU_CODE':{'dst':'stkcode'}},
                {'ORDER_ID':{'dst':'ordersno'}},
                {'ordergroup#':{'dst':'ordergroup','default':'0'}},
                {'bankcode':{'dst':'bankcode'}},
                {'qryflag#':{'dst':'qryflag','default':'1'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEY_STR':{'dst':'position_str'}},
                {'EXT_SERIAL_NO':{'dst':'extsno'}}
            ],
            'ans':
            [
                {'opertime':{'dst':'ORDER_TIME'}},
                {'operdate':{'dst':'ORDER_DATE'}},
                {'entrust_date':{'dst':'TRD_DATE'}},
                {'custid':{'dst':'USER_CODE'}},
                {'custname':{'dst':'USER_NAME'}},
                {'fundid':{'dst':'ACCOUNT'}},
                {'money_type':{'dst':'CURRENCY'}},
                {'orgid': {'dst': 'BRANCH'}},
                {'secuid':{'dst':'SECU_ACC'}},
                {'bsflag':{'dst':'TRD_ID','dict':QueryBsflag_outdict}},
                {'ordersno': {'dst': 'ORDER_ID'}},
                {'orderid': {'dst': 'ORDER_TEMPID'}},
                {'market':{'dst':'MARKET','dict':GGT_Market_outdict}},
                {'stock_code':{'dst':'SECU_CODE'}},
                {'stock_name':{'dst':'SECU_NAME'}},
                {'seat':{'dst':'SEAT'}},
                {'orderprice':{'dst':'PRICE'}},
                {'orderqty':{'dst':'QTY'}},
                {'orderfrzamt_rmb':{'dst':'ORDER_AMT'}},
                {'cancelflag':{'dst':'IS_WITHDRAW','dict':CancelFlagTranfer_outdict}},
                {'orderstatus':{'dst':'DCL_FLAG','dict':orderstats_to_DCL_FLAG_dict}},
                {'matchqty':{'dst':'MATCHED_QTY'}},
                {'cancelqty':{'dst':'WITHDRAWN_QTY'}},
                {'matchamt':{'dst':'MATCHED_AMT'}},
                {'ordergroup': {'dst': 'EXT_SERIAL_NO'}},
                {'referrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,referrate)'}},
                {'poststr': {'dst': 'KEY_STR'}}
            ]
        },
	'601190': #港股通历史成交查询
        {
            'dstfuncid':'430013',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'430013'}},
                {'ext#': {'dst': 'ext', 'default': '0'}},
                {'CUSTID':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'ORGID':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'netaddr': {'dst': 'netaddr'}},
                {'ORGID':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},

                {'SECU_CODE':{'dst':'stkcode'}},
                {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                {'CURRENCY': {'dst': 'moneytype'}},
                {'SECUID': {'dst': 'secuid'}},  # 股东代码
                {'BEGIN_DATE':{'dst':'strdate'}},
                {'END_DATE':{'dst':'enddate'}},
                {'FUNDID':{'dst':'fundid'}},
                {'default0':{'dst':'printflag','default':'0'}},
                {'default1':{'dst':'qryflag','default':'1'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEY_STR':{'dst':'poststr'}}
            ],
            'ans':
            [
                {'market':{'dst':'MARKET','dict':GGT_Market_outdict}}, #交易市场
                {'stkcode': {'dst': 'SECU_CODE'}}, #证券代码
                {'stock_name':{'dst':'SECU_NAME'}}, #证券名称
                {'moneytype': {'dst': 'CURRENCY'}}, #货币类型
                {'bsflag':{'dst':'TRD_ID','dict':QueryBsflag_outdict}}, #交易行为
                {'serial_no':{'dst':'ORDER_ID'}}, #合同序号

                {'matchqty': {'dst': 'MATCHED_QTY'}}, #成交数量
                {'matchamt': {'dst': 'MATCHED_AMT'}}, #成交金额
                {'matchprice': {'dst': 'MATCHED_PRICE'}}, #成交价格
                {'cleardate': {'dst': 'MATCHED_DATE'}}, #成交日期
                {'matchtime':{'dst':'MATCHED_TIME','functionPlugIn':'functionPlugIn(TimeFormat8To6,matchtime,"0","6")'}}, #时间格式化
                {'matchcode': {'dst': 'MATCHED_SN'}}, #成交序号

                {'fundeffect': {'dst': 'SETT_AMT'}}, #清算过户金额
                {'fee_sxf': {'dst': 'COMMISSION'}}, #手续费
                {'fee_yhs': {'dst': 'STAMP_DUTY'}}, #印花税
                {'fee_ghf': {'dst': 'TRANS_FEE'}}, #过户费
                {'fee_jygf': {'dst': 'XTRANS_FEE'}}, #交易所过户费
                {'fee_jsf': {'dst': 'HANDLE_FEE'}}, #经手费
                {'fee_zgf': {'dst': 'ADMIN_FEE'}}, #证管费
                {'fee_jygf': {'dst': 'TRADE_FEE'}}, #交易规费
                {'fee_qsf': {'dst': 'CLEAR_FEE'}}, #清算费

                {'secuid':{'dst':'SECU_ACC'}}, #股东代码
                {'poststr': {'dst': 'KEY_STR'}}, #定位串
                {'fundid': {'dst': 'ACCOUNT'}}, #资金账号
                {'settrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}}, #结算汇率
            ]
        },
    '601170':  # 港股通历史委托查询
        {
            'dstfuncid': '430002',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430002'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'BEGIN_DATE': {'dst': 'strdate'}},
                    {'END_DATE': {'dst': 'enddate'}},
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},# 从票据取资金帐号
                    {'secuid_tmp': {'dst': 'secuid', 'functionPlugIn': 'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}},  # 从票据股东代码
                    {'SECU_CODE': {'dst': 'stkcode'}},
                    {'ORDER_ID': {'dst': 'ordersno', 'default': '0'}},
                    {'EXT_SERIAL_NO': {'dst': 'ordergroup', 'default': '0'}},
                    {'QRYFLAG_tmp': {'dst': 'qryflag', 'default': '1'}},  # 查询方向
                    {'REC_COUNT': {'dst': 'count', 'default': '50'}},  # 请求行数
                    {'KEY_STR': {'dst': 'poststr'}}
                ],
            'ans':
                [
                    {'operdate': {'dst': 'ORDER_DATE'}},  # 委托日期
                    {'opertime': {'dst': 'ORDER_TIME','functionPlugIn': 'functionPlugIn(TimeFormat8To6,opertime,"0","6")'}},  # 委托时间
                    {'orderdate': {'dst': 'TRD_DATE'}},

                    {'moneytype': {'dst': 'CURRENCY'}},  # 货币类型
                    {'bsflag': {'dst': 'TRD_ID', 'dict': QueryBsflag_outdict}},
                    {'market': {'dst': 'MARKET', 'dict': GGT_Market_outdict}},  # 交易市场

                    {'stkname': {'dst': 'SECU_NAME'}},  # 证券名称
                    {'stkcode': {'dst': 'SECU_CODE'}},  # 证券代码
                    {'orderprice': {'dst': 'PRICE'}},  # 委托价格
                    {'orderqty': {'dst': 'QTY'}},  # 数量
                    {'orderfrzamt_rmb': {'dst': 'ORDER_FRZ_AMT'}},  # 委托冻结金额
                    {'matchqty': {'dst': 'MATCHED_QTY'}},  # 成交数量
                    {'cancelqty': {'dst': 'WITHDRAWN_QTY'}},  # 已撤单数量
                    {'matchamt': {'dst': 'MATCHED_AMT'}},  # 成交金额
                    {'ordergroup': {'dst': 'EXT_SERIAL_NO'}},  # 外部流水号
                    {'ordersno': {'dst': 'ORDER_ID'}},  # 合同序号
                    {'stock_account': {'dst': 'SECU_ACC'}},  # 资金账号
                    {'poststr': {'dst': 'KEY_STR'}},  # 定位串
                    {'orderstatus': {'dst': 'DCL_FLAG', 'dict': orderstats_to_DCL_FLAG_dict}},  # 合法标志
                    {'fundid': {'dst': 'ACCOUNT'}},  # 资金账号

                    {'referrate': {'dst': 'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,referrate)'}},  # 参考汇率
                    {'afundamt': {'dst': 'afundamt'}},  # A股资金变动金额
                    {'hfundamt': {'dst': 'hfundamt'}},  # 港股资金变动金额
                    {'reporttime': {'dst': 'DCL_TIME'}},  # 报盘时间
                    {'custname': {'dst': 'USER_NAME'}},  # 客户姓名
                    {'custid': {'dst': 'USER_CODE'}},  # 客户代码
                    {'orgid': {'dst': 'BRANCH'}},  # 机构编码
                    {'orderid': {'dst': 'BIZ_NO'}},  # 申报合同序号
                    {'seat': {'dst': 'SEAT'}},  # 交易席位
                    {'cancelflag': {'dst': 'IS_WITHDRAW'}},  # 撤单标志
                    {'reporttime': {'dst': 'DCL_TIME'}}  # 报盘时间

                ]
        },
    '430009':  # 港股通参考汇率查询
        {
            'dstfuncid': '430009',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430009'}},
                    {'custid': {'dst': 'custid'}},  # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid'}},  # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0',}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'MARKET': {'dst': 'market', 'default': ''}},
                    {'moneytype': {'dst': 'moneytype', 'default': ''}}
                ],
            'ans':
                [
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                    {'moneytype': {'dst': 'moneytype'}},
                    {'buyrate': {'dst': 'buyrate', 'functionPlugIn': 'functionPlugIn(FormatRate,buyrate)'}},
                    {'daybuyriserate': {'dst': 'daybuyriserate'}},
                    {'nightbuyriserate': {'dst': 'nightbuyriserate'}},
                    {'salerate': {'dst': 'salerate', 'functionPlugIn': 'functionPlugIn(FormatRate,salerate)'}},
                    {'daysaleriserate': {'dst': 'daysaleriserate'}},
                    {'nightsaleriserate': {'dst': 'nightsaleriserate'}},
                    {'midrate': {'dst': 'midrate'}},
                    {'sysdate': {'dst': 'sysdate'}},
                    {'settrate': {'dst': 'settrate'}},
                    {'remark': {'dst': 'remark'}}
                ]
        },

    '601180': #港股当日成交查询
        {
            'dstfuncid':'430003',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'430003'}},
                {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                #{'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                #{'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'custorgid':{'dst':'orgid'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},
                {'ext':{'dst':'ext','default':'0'}},
                {'netaddr':{'dst':'netaddr'}},
                {'netaddr2':{'dst':'netaddr2'}},

                {'MARKET':{'dst':'market'}},
                {'ACCOUNT':{'dst':'fundid'}},
                {'SECUID':{'dst':'secuid'}},
                {'SECU_CODE':{'dst':'stkcode'}},
                {'ORDER_ID':{'dst':'ordersno'}},
                {'bankcode':{'dst':'bankcode'}},
                {'qryflag_tmp':{'dst':'qryflag','default':'1'}},
                {'REC_COUNT':{'dst':'request_num','default':'50'}},
                {'KEYSTR':{'dst':'poststr'}},
                {'bankcode':{'dst':'bankcode'}}
            ],
            'ans':
            [
                {'trddate':{'dst':'TRD_DATE'}},		    #交易日期
                {'undefined':{'dst':'CURRENCY','default':'0'}},	#货币
                {'secuid':{'dst':'SECU_ACC'}},		    #股东代码
                {'money_type':{'dst':'CURRENCY'}},		    #货币(柜台没这个字段)
                {'bsflag':{'dst':'TRD_ID','dict':QueryBsflag_outdict}}, #交易行为
                {'ordersno':{'dst':'ORDER_ID'}},	    #合同序号
                {'market':{'dst':'MARKET','dict':GGT_Market_outdict}},		    #交易市场
                {'stock_name':{'dst':'SECU_NAME'}},	    #证券名称
                {'stock_code':{'dst':'SECU_CODE'}},            #证券代码
                {'orderprice':{'dst':'PRICE'}},		    #价格
                {'orderqty':{'dst':'QTY'}}, 		    #数量
                {'matchtype':{'dst':'MATCH_TYPE','dict':dict_match_type}}, #成交类型
                {'matchtime':{'dst':'MATCHED_TIME','functionPlugIn':'functionPlugIn(TimeFormat8To6,matchtime,"0","6")'}}, #时间格式化
                {'matchcode':{'dst':'MATCHED_SN'}}, 	    #成交序号
                {'matchprice':{'dst':'MATCHED_PRICE'}},     #成交价格
                {'matchqty':{'dst':'MATCHED_QTY'}}, 	    #成交数量
                {'matchamt':{'dst':'MATCHED_AMT'}}, 	    #成交金额
                {'position_str':{'dst':'KEY_STR'}}, 		    #定位串
                {'settrate':{'dst':'RATE', 'functionPlugIn': 'functionPlugIn(FormatRate,settrate)'}}, 		    #汇率(柜台没这个字段)
                {'fundid':{'dst':'ACCOUNT'}}
            ]
        },

    '430007':  # 港股通最大可交易数量查询
        {
            'dstfuncid': '430007',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430007'}},
                    {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                    {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0',}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'MARKET':{'dst':'market'}},
                    {'ACCOUNT':{'dst':'fundid'}},
                    {'secuid_tmp':{'dst':'secuid','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}}, #从票据股东代码
                    {'SECU_CODE':{'dst':'stkcode'}},
                    {'ORDER_PRICE':{'dst':'price'}},
                    {'TRD_ID':{'dst':'bsflag','dict':TRD_ID_bsflag_600140dict}}
                ],
            'ans':
                [
                    {'maxstkqty': {'dst': 'ORDER_QTY'}}
                ]
        },

    '600121':  # 最大可交易数量查询
        {
            'dstfuncid': '333001',
            'req':
                [
                    {'sys_node': {'dst': 'sys_node', 'default': '2'}},
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}}, #市场
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},# 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'user_token': {'dst': 'user_token'}},

                    # {'secuid_tmp':{'dst':'secuid','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}}, #从票据股东代码
                    {'SECU_CODE':{'dst':'stock_code'}},     #证券代码
                    {'ORDER_PRICE':{'dst':'entrust_price'}},
                    {'entrust_prop':{'dst':'entrust_prop', 'default': '0'}},
                    {'TRD_ID':{'dst':'bsflag','dict':TRD_ID_bsflag_600140dict}}
                ],
            'ans':
                [
                    {'enable_buy_amount': {'dst': 'ORDER_QTY','functionPlugIn':'functionPlugIn(FormatFloat,enable_buy_amount,"0")'}}
                ]
        },

        '500403':  # 港股通标的查询
        {
            'dstfuncid': '430015',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430015'}},
                    {'custid':{'dst':'custid'}}, #客户号
                    {'custorgid':{'dst':'custorgid'}}, #机构号
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0',}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'ACCOUNT':{'dst':'fundid'}},
                    {'secuid_tmp':{'dst':'secuid'}}, #股东代码

                    {'COMPANY_ID':{'dst':'company_id'}},
                    {'MARKET':{'dst':'market','dict':dict_market_wzq_to_T2sdk}},
                    {'SECU_CODE':{'dst':'stkcode'}},
                    {'KEY_STR':{'dst':'KEY_STR'}},
                    {'REC_COUNT':{'dst':'REC_COUNT'}}
                ],
            'ans':
                [
                    {'market': {'dst': 'MARKET','dict':GGT_Market_outdict}},
                    {'SNO': {'dst': 'SNO'}},
                    {'stkname': {'dst': 'SECU_NAME'}},
                    {'stkcode': {'dst': 'SECU_CODE'}},
                    {'DOUBLE_FLAG': {'dst': 'DOUBLE_FLAG'}},
                    {'KEY_STR':{'dst':'KEY_STR'}}
                ]
        },

        '500401':  # 查询港股通市场额度
        {
            'dstfuncid': '430014',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430014'}},
                    {'custid':{'dst':'custid'}}, #客户号
                    {'custorgid':{'dst':'custorgid'}}, #机构号
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0',}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'ACCOUNT':{'dst':'fundid'}},
                    {'secuid_tmp':{'dst':'secuid'}}, #股东代码

                    {'COMPANY_ID':{'dst':'company_id'}},
                    {'MARKET':{'dst':'market'}},

                ],
            'ans':
                [                                       #前端字段在代码转
                    {'market':{'dst':'market'}},        #市场
                    {'quotabal':{'dst':'quotabal', 'functionPlugIn': 'functionPlugIn(Formatlimit,quotabal)'}},    #每日初始额度
                    {'quotaavl':{'dst':'quotaavl', 'functionPlugIn': 'functionPlugIn(Formatlimit,quotaavl)'}},    #日中剩余额度
                    {'quotastatus':{'dst':'quotastatus', 'dict': GGT_QuotaStatus_outdict}},  #额度状态
                    {'upddate':{'dst':'upddate'}}   #更新日期
                ]
        },
        '500060A':  #查询客户是否具有港股通交易资质
        {
            'dstfuncid': '********',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '********'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'orgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'ACCOUNT': {'dst': 'CUST_CODE'}}, #客户代码
                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE', 'default':'0l'}}, #协议类型，默认：港股通协议
					{'REMOTE_SYS': {'dst': 'REMOTE_SYS'}}, #对接远程系统
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}}, #资产账号
					{'STKBD': {'dst': 'STKBD'}}, #交易板块
                    {'TRDACCT': {'dst': 'TRDACCT'}} #交易账户
                ],
            'ans':
                [
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},  # 资产账号
                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE'}},  # 客户协议类型
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},  # 客户代码
                    {'EFT_DATE': {'dst': 'EFT_DATE'}},  # 生效日期
                    {'EXP_DATE': {'dst': 'EXP_DATE'}},  # 有效截止日期
					{'REMOTE_SYS': {'dst': 'REMOTE_SYS'}},  # 对接远程系统
                    {'STKBD': {'dst': 'STKBD'}},  # 交易板块
                    {'TRDACCT': {'dst': 'TRDACCT'}},  # 交易账户
                    {'UPD_DATE': {'dst': 'UPD_DATE'}},  # 更新日期
                ]
        },

    '160001':  # 查询客户信息
        {
            'dstfuncid': 'L1160001',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L1160001'}},
                    {'F_OP_USER#-1': {'dst': 'F_OP_USER','default': '8888'}},
                    {'F_OP_ROLE#-1': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE#-1#': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL#-1': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION#-1': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_RUNTIME#-1': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_SUBSYS#-1': {'dst': 'F_SUBSYS', 'default': '1'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG'}},

                    {'ACCOUNT': {'dst': 'USER_CODE'}}
                ],
            'ans':
                [
                    {'USER_TYPE': {'dst': 'USER_TYPE'}},  #用户类型
                    {'OCCU_TYPE': {'dst': 'OCCU_TYPE', 'dict': dict_GJZQ_OCCU_TYPE_TO_WECHAT}},   #职业类型
                    {'BIRTHDAY': {'dst': 'BIRTHDAY'}},  #出生日期
                ]
        },

        '190086':  # 客户批量作答
        {
            'dstfuncid': 'L1190086',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L1190086'}},
                    {'F_OP_USER#-1': {'dst': 'F_OP_USER','default': '8888'}},
                    {'F_OP_ROLE#-1': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE#-1#': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL#-1': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION#-1': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_RUNTIME#-1': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_SUBSYS#-1': {'dst': 'F_SUBSYS', 'default': '1'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG'}},

                    {'COMPANY_ID': {'dst': 'COMPANY_ID'}},   #券商编码
                    {'RIST_ASSESSMENT': {'dst': 'RIST_ASSESSMENT'}},    #风险测评结果
                    {'CUSTID': {'dst': 'custid'}},       #
                    {'ACCOUNT': {'dst': 'USER_CODE'}},   #用户代码
                    {'SURVEY_SN': {'dst': 'SURVEY_SN', 'default': '25'}},     #调查表编码
                    {'SURVEY_GR#-1': {'dst': 'SURVEY_GR', 'default': '1'}},     #调查表栏组
                    {'SURVEY_COLS': {'dst': 'SURVEY_COLS','functionPlugIn':'functionPlugIn(GetHlzqSurveyInfo,RIST_ASSESSMENT,"3")'}},    #调查表栏目(复数)
                    {'SURVEY_CELLS': {'dst': 'SURVEY_CELLS','functionPlugIn':'functionPlugIn(GetHlzqSurveyInfo,RIST_ASSESSMENT,"4")'}},  #调查表单元(复数)
                    {'SURVEY_ANS_VALS#-1': {'dst': 'SURVEY_ANS_VALS'}}, #调查表作答分值(复数)
                    {'SUBSYS_FLAG': {'dst': 'SUBSYS_FLAG'}},    #同步子系统标志
                    {'ANS_STATUS': {'dst': 'ANS_STATUS'}},       #作答状态
                    {'VERSION': {'dst': 'VERSION', 'default': '1.0'}}       #版本
                ],
            'ans':
                [
                    {'SURVEY_CLS': {'dst': 'SURVEY_CLS'}},  #调查表类别
                    {'SURVEY_SN': {'dst': 'SURVEY_SN'}},  #调查表编码
                    {'SURVEY_SCORE': {'dst': 'SURVEY_SCORE'}},  #测评分数
                    {'RATING_LVL': {'dst': 'RATING_LVL'}},  #评级级别
                    {'RATING_LVL_NAME': {'dst': 'RATING_LVL_NAME'}} #评级级别名称  #
                ]
        },

    '410331':  # 外围设置客户权限(
        {
            'dstfuncid': '410331',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410331'}},
                    {'custid':{'dst':'custid','functionPlugIn':'functionPlugIn(WSGetCustid,SESSION_JSON)'}}, #从票据中取客户号
                    {'custorgid':{'dst':'custorgid','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0',}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'MARKET':{'dst':'market'}},
                    {'ACCOUNT':{'dst':'fundid'}},
                    {'rightid':{'dst':'rightid', 'default': '0s'}},     #开港股权限
                    {'action':{'dst':'action', 'default': 'A'}},
                    {'secuid_tmp':{'dst':'secuid','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}} #从票据股东代码
                ],
            'ans':
                [

                ]
        },
    '410321':  # 查询客户预留的证件类型，证件号码，手机号码
        {
            'dstfuncid': '410321',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410321'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},
                ],
            'ans':
                [
                    {'idno': {'dst': 'idno'}},  # 证件id
                    {'idtype': {'dst': 'idtype', 'dict':dict_idtype_COUNTER_TO_WZQ}},  # 证件类型
                    {'mobileno': {'dst': 'mobileno'}}  # 手机号码
                ]
        },
    '99000270': #用户基本信息查询
        {
            'dstfuncid':'99000270',
            'req':
            [
                {'funcid#':{'dst':'funcid','default':'99000270'}},
                {'ext':{'dst':'ext','default':'0'}},
                {'custcert':{'dst':'custcert'}},
                {'custid':{'dst':'custid'}},
                {'custorgid':{'dst':'custorgid'}},
                {'trdpwd':{'dst':'trdpwd'}},
                {'netaddr':{'dst':'netaddr'}},
                {'operway#':{'dst':'operway','default':'operway','dict':general_dict}},


                {'COMPANY_ID':{'dst':'company_id'}},
                {'USER_CODE':{'dst':'USER_CODE'}},  #用户代码
                {'USER_ROLE#-1':{'dst':'USER_ROLE','default':'0'}}, #用户角色
                {'USER_ROLE':{'dst':'USER_ROLE'}},
                {'OP_TYPE':{'dst':'OP_TYPE'}}   #操作类型
            ],
            'ans':
            [
                {'OPEN_SOURCE':{'dst':'OPEN_SOURCE'}}   #网厅开户标志
            ]
        },
    '600011':   # 资金密码登陆 新 DH edit
        {
            'dstfuncid':'330010',
            'req':
            [
                {'op_branch_no':{'dst':'op_branch_no','default':'5010'}},    # 操作分支机构
                {'op_entrust_way#':{'dst':'op_entrust_way','default':'h'}},     # 操作方式
                {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                {'branch_no':{'dst':'branch_no','default':'5010'}},   # 分支机构
                {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},  # 从票据中取客户号
                {'ACCOUNT': {'dst': 'fund_account', 'default': ''}},  # 资产账户
                {'CURRENCY': {'dst': 'CURRENCY', 'default': '0'}},  # 币种
                {'USER_PWD':{'dst':'password','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,USER_PWD,USER_ID_CLS)'}},    # 密码
                {'password_type': {'dst': 'password_type', 'default': '1'}}    # 密码类别
            ],
            'ans':
            [
                {'REST_NUM':{'dst':'REST_NUM'}}
            ]
        },
    '********':  # 用户密码管理
        {
            'dstfuncid': '********',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '********'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'custcert': {'dst': 'custcert'}},
                    {'USER_CODE': {'dst': 'USER_CODE'}},  # 用户代码
                    {'OLD_AUTH_DATA': {'dst': 'OLD_AUTH_DATA', 'functionPlugIn': 'functionPlugIn(WSMultiEncode,OLD_AUTH_DATA,"rsa_decrypt","","aes_encrypt",USER_CODE)'}}, # 旧认证信息
                    {'OPERATION_TYPE': {'dst': 'OPERATION_TYPE'}},  # 操作类型
                    {'USER_ROLE': {'dst': 'USER_ROLE'}},  # 用户角色
                    {'AUTH_TYPE': {'dst': 'AUTH_TYPE'}},  # 认证类型
                    {'OP_REMARK': {'dst': 'OP_REMARK'}},  # 操作备注
                    {'USE_SCOPE': {'dst': 'USE_SCOPE'}},  # 使用范围
                    {'NEW_AUTH_DATA': {'dst': 'NEW_AUTH_DATA', 'functionPlugIn': 'functionPlugIn(WSMultiEncode,NEW_AUTH_DATA,"rsa_decrypt","","aes_encrypt",USER_CODE)'}} # 新认证信息
                ],
            'ans':
                [
                    {'USER_CODE': {'dst': 'USER_CODE'}},  # 用户代码
                ]
        },
    '600047A':  # 修改客户普通资料
        {
            'dstfuncid': 'L1100004',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION', 'default': 'L1100004'}},
                    {'F_OP_USER#-1': {'dst': 'F_OP_USER', 'default': '10416'}},
                    {'F_OP_ROLE#-1': {'dst': 'F_OP_ROLE', 'default': '2'}},
                    {'F_OP_SITE#-1#': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL#-1': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION#-1': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_RUNTIME#-1': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_REMOTE_OP_ORG#-1': {'dst': 'F_REMOTE_OP_ORG', 'default': ''}},
                    {'F_REMOTE_OP_USER#-1': {'dst': 'F_REMOTE_OP_USER', 'default': ''}},

                    {'custid': {'dst': 'USER_CODE', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 客户代码
                    {'ZIP_CODE': {'dst': 'ZIP_CODE', 'default': ''}},  # 邮政编码
                    {'ADDRESS': {'dst': 'ADDRESS', 'default': ''}},  # 联系地址
                    {'TEL': {'dst': 'TEL'}},  # 联系电话
                    {'FAX': {'dst': 'FAX'}},  # 传真电话
                    {'EMAIL': {'dst': 'EMAIL'}},  # 电子邮箱
                    {'M_TEL': {'dst': 'MOBILE_TEL'}},  # 移动电话
                    {'CITIZENSHIP': {'dst': 'CITIZENSHIP'}},  # 国籍
                    {'NATIONALITY': {'dst': 'NATIONALITY'}},  # 名族
                    {'EDUCATION': {'dst': 'EDUCATION'}},  # 学历
                    {'NATIVE_PLACE': {'dst': 'NATIVE_PLACE'}},  # 籍贯/注册地
                    {'SEX': {'dst': 'SEX'}},  # 性别
                    {'BIRTHDAY': {'dst': 'BIRTHDAY'}},  # 生日
                    {'REMARK': {'dst': 'REMARK'}},  # 备注信息
                    {'ID_ISS_AGCY': {'dst': 'ID_ISS_AGCY'}},  # 发证机关
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},  # 证件有效日期
                    {'ID_BEG_DATE': {'dst': 'ID_BEG_DATE'}},  # 证件开始日期
                    {'ID_ZIP_CODE': {'dst': 'ID_ZIP_CODE'}},  # 证件编码
                    {'ID_ADDR': {'dst': 'ID_ADDR'}},  # 证件地址
                    {'MARRY': {'dst': 'MARRY'}},  # 婚姻状况
                    {'AVOCATION': {'dst': 'AVOCATION'}},  # 兴趣爱好
                    {'VEHICLE': {'dst': 'VEHICLE'}},  # 交通工具
                    {'HOUSE_OWNER': {'dst': 'HOUSE_OWNER'}},  # 住宅所有权
                    {'OFFICE_TEL': {'dst': 'OFFICE_TEL'}},  # 办公电话
                    {'WELL_TEL': {'dst': 'WELL_TEL'}},  # 小灵通电话
                    {'LINKTEL_ORDER': {'dst': 'LINKTEL_ORDER'}},  # 首选联系地址
                    {'OFFICE_ADDR': {'dst': 'OFFICE_ADDR'}},  # 办公地址
                    {'CORP_ADDR': {'dst': 'CORP_ADDR'}},  # 公司地址
                    {'LINKADDR_ORDER': {'dst': 'LINKADDR_ORDER'}},  # 首选联系地址
                    {'CHG_SPEC_REMARK_FLAG': {'dst': 'CHG_SPEC_REMARK_FLAG'}},  # 特殊操作备注修改标志
                    {'SUBSYS': {'dst': 'SUBSYS'}},  # 同步子系统
                    {'OPEN_SOURCE': {'dst': 'OPEN_SOURCE'}}  # 开户来源
                ],
            'ans':
                [
                    {'SERIAL_NO#-1': {'dst': 'SERIAL_NO'}}  # 此业务只返回第一结果集
                ]
        },
    '600047':  # 客户资料修改
        {
            'dstfuncid': '331103',
            'req':
                [
                    {'COMPANY_ID': {'dst': 'COMPANY_ID'}},
                    {'funcid': {'dst': 'funcid', 'default': '333002'}},
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'registe_sure_flag': {'dst': 'registe_sure_flag'}},
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},# 从票据中取机构号
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, # 从票据取密码
                    {'password': {'dst': 'password', 'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}},
                    {'user_token': {'dst': 'user_token'}},

                    {'EDUCATION': {'dst': 'degree_code', 'dict': dict_HLZQ_EDUCATION_TO_T2SDK}},  # 学历
                    {'OCCU_TYPE': {'dst': 'profession_code', 'dict': dict_GJZQ_WECHAT_TO_OCCU_TYPE}}  # 职业信息
                ],
            'ans':
                [
                    {'SERIAL_NO#-1': {'dst': 'SERIAL_NO'}}  # 此业务只返回第一结果集
                ]
        },
    '600044':  # 开通ST权限
        {
            'dstfuncid': '410331',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '410331'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'MARKET': {'dst': 'market', 'default': '1'}},
                    {'ACCOUNT': {'dst': 'fundid'}},
                    {'secuid_tmp': {'dst': 'secuid','functionPlugIn': 'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}},  # 从票据股东代码
                    {'rightid': {'dst': 'rightid', 'default': '04'}},  # 权限
                    {'action': {'dst': 'action', 'default': 'A'}}  # 操作
                ],
            'ans':
                [
                    {'SERIAL_NO#-1': {'dst': 'SERIAL_NO'}}  # 此业务只返回第一结果集
                ]
        },
    '601140':  # 港股通普通委托
        {
            'dstfuncid': '430010',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430010'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    # {'trdpwd': {'dst': 'trdpwd','functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'TCC': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'timeinforce': {'dst': 'timeinforce', 'default': '0'}},

                    {'MARKET': {'dst': 'market', 'dict': GGT_Market_indict}},
                    {'TRD_ID': {'dst': 'bsflag', 'dict': TRD_ID_bsflag_600140dict}},
                    {'CUSTID': {'dst': 'inputid', 'default': ''}},
                    {'CURRENCY': {'dst': 'moneytype'}},
                    {'ORDER_QTY': {'dst': 'qty'}},
                    {'ORDER_PRICE': {'dst': 'price', 'default': ''}},
                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    # 从票据取资金帐号
                    {'SECU_ACC': {'dst': 'secuid'}},
                    {'SECU_CODE': {'dst': 'stkcode'}},
                    {'ordergroup#-1': {'dst': 'ordergroup', 'default': '0'}},
                    {'bankcode#-1': {'dst': 'bankcode', 'default': '0'}},
                    {'remark#-1': {'dst': 'remark', 'default': '0'}},
                    {'EXT_SERIAL_NO': {'dst': 'extsno'}}
                ],
            'ans':
                [
                    {'ordersno': {'dst': 'ORDER_ID'}}
                ]
        },
    '601150A':  # 港股通可撤单委托查询
        {
            'dstfuncid': '430012',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430012'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'TCC': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'ORDER_ID': {'dst': 'ordersno'}},  # 委托序号
                    {'orderdate': {'dst': 'orderdate', 'default': ''}},  # 委托日期
                    {'SECU_ACC': {'dst': 'secuid'}},  # 股东代码
                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    # 从票据取资金帐号
                    {'qryflag#-1': {'dst': 'qryflag', 'default': '1'}},  # 查询方向
                    {'REC_COUNT': {'dst': 'count', 'default': '50'}},  # 请求行数
                    {'poststr': {'dst': 'poststr'}},  # 定位串
                    {'stkcode': {'dst': 'stkcode'}}  # 证券代码
                ],
            'ans':
                [
                    {'bsflag': {'dst': 'bsflag'}},  # 买卖类别
                    {'cancelstatus': {'dst': 'cancelstatus'}},  # 撤单状态
                    {'fundid': {'dst': 'fundid'}},
                    {'ordergroup': {'dst': 'ordergroup'}},
                    {'opertime': {'dst': 'opertime'}},
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},
                    {'matchqty': {'dst': 'matchqty'}},
                    {'orderdate': {'dst': 'orderdate'}},
                    {'orderid': {'dst': 'orderid'}},
                    {'orderprice': {'dst': 'orderprice'}},
                    {'orderqty': {'dst': 'orderqty'}},  # 委托数量
                    {'ordersno': {'dst': 'ordersno'}},  # 委托序号
                    {'orderstatus': {'dst': 'orderstatus'}},
                    {'poststr': {'dst': 'poststr'}},
                    {'secuid': {'dst': 'secuid'}},
                    {'stkcode': {'dst': 'stkcode'}},
                    {'stkname': {'dst': 'stkname'}}
                ]
        },
    '601150B':  # 港股通委托撤单
        {
            'dstfuncid': '430011',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '430011'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'trdpwd': {'dst': 'trdpwd',
                                'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode",custid)'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'TCC': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},

                    {'fundid_tmp': {'dst': 'fundid', 'functionPlugIn': 'functionPlugIn(WSGetFundid,SESSION_JSON)'}},
                    # 从票据取资金帐号
                    {'MARKET': {'dst': 'market', 'dict': GGT_Market_indict}},  # 交易市场
                    {'TRD_ID': {'dst': 'bsflag', 'dict': TRD_ID_bsflag_600140dict}},  # 交易行为
                    {'ORDER_ID': {'dst': 'ordersno'}},  # 合同序号
                    {'orderdate': {'dst': 'orderdate', 'default': ''}},  # 委托日期
                    {'SECU_ACC': {'dst': 'secuid'}}  # 股东代码
                ],
            'ans':
                [
                    {'ordersno': {'dst': 'ORDER_ID'}}
                ]
        },
    '99000265':  # 查询证券账户信息
        {
            'dstfuncid': '99000265',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '99000265'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'custorgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'netaddr2': {'dst': 'netaddr2'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'ACCOUNT': {'dst': 'CUST_CODE'}},
                    {'STKEX': {'dst': 'STKEX'}},
                    {'STKBD': {'dst': 'STKBD'}},
                    {'SECU_ACC': {'dst': 'TRDACCT'}}
                ],
            'ans':
                [
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},
                    {'INT_ORG': {'dst': 'INT_ORG'}},
                    {'STKBD': {'dst': 'STKBD'}},
                    {'TRDACCT': {'dst': 'TRDACCT'}},
                    {'TRDACCT_SN': {'dst': 'TRDACCT_SN'}},
                    {'TRDACCT_EXID': {'dst': 'TRDACCT_EXID'}},
                    {'TRDACCT_TYPE': {'dst': 'TRDACCT_TYPE'}},
                    {'TRDACCT_EXCLS': {'dst': 'TRDACCT_EXCLS'}},
                    {'TRDACCT_NAME': {'dst': 'TRDACCT_NAME'}},
                    {'TRDACCT_STATUS': {'dst': 'TRDACCT_STATUS'}},
                    {'TREG_STATUS': {'dst': 'TREG_STATUS'}},
                    {'BREG_STATUS': {'dst': 'BREG_STATUS'}},
                    {'STKPBU': {'dst': 'STKPBU'}},
                    {'FIRMID': {'dst': 'FIRMID'}},
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},
                    {'ID_CODE': {'dst': 'ID_CODE'}},
                    {'ID_ISS_AGCY': {'dst': 'ID_ISS_AGCY'}},
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},
                    {'OPEN_DATE': {'dst': 'OPEN_DATE'}},
                    {'STKEX': {'dst': 'STKEX'}},
                    {'CUST_CODE': {'dst': 'CUST_CODE'}}
                ]
        },
    '********':  #查询客户的交易资质
        {
            'dstfuncid': '********',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '********'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'orgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'ACCOUNT': {'dst': 'CUST_CODE'}}, #客户代码
                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE'}}, #协议类型
					{'REMOTE_SYS': {'dst': 'REMOTE_SYS'}}, #对接远程系统
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}}, #资产账号
					{'STKBD': {'dst': 'STKBD'}}, #交易板块
                    {'TRDACCT': {'dst': 'TRDACCT'}} #交易账户
                ],
            'ans':
                [
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},  # 资产账号
                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE'}},  # 客户协议类型
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},  # 客户代码
                    {'EFT_DATE': {'dst': 'EFT_DATE'}},  # 生效日期
                    {'EXP_DATE': {'dst': 'EXP_DATE'}},  # 有效截止日期
					{'REMOTE_SYS': {'dst': 'REMOTE_SYS'}},  # 对接远程系统
                    {'STKBD': {'dst': 'STKBD'}},  # 交易板块
                    {'TRDACCT': {'dst': 'TRDACCT'}},  # 交易账户
                    {'UPD_DATE': {'dst': 'UPD_DATE'}},  # 更新日期
                ]
        },
    '99000384':  #代理中登账户业务
        {
            'dstfuncid': '99000384',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '99000384'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'orgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'CUSTID': {'dst': 'CUST_CODE'}},  # 客户代码
                    {'CHK_STATUS': {'dst': 'CHK_STATUS'}},
                    {'OPERATOR_TYPE': {'dst': 'OPERATOR_TYPE'}}, #操作类型
					{'STKBD': {'dst': 'STKBD'}}, #交易板块
                    {'TRDACCT_EXCLS': {'dst': 'TRDACCT_EXCLS'}}, #交易账户类别
					{'SERIAL_NO': {'dst': 'SERIAL_NO'}}, #流水序号
                    {'OCCUR_DATE': {'dst': 'OCCUR_DATE'}}, #发生日期
                    {'ORGID': {'dst': 'INT_ORG'}}, #内部机构代码
                    {'STK_BIZ_CLS': {'dst': 'STK_BIZ_CLS'}}, #业务类别
					{'YMT_CODE': {'dst': 'YMT_CODE'}}, #一码通账号
                    {'USER_TYPE': {'dst': 'USER_TYPE'}}, #客户类型
					{'TRDACCT': {'dst': 'TRDACCT'}}, #证券账户
                    {'TRDACCT_EX': {'dst': 'TRDACCT_EX'}}, #配号证券账户
                    {'CUST_FNAME': {'dst': 'CUST_FNAME'}}, #客户全称
                    {'ID_TYPE': {'dst': 'ID_TYPE'}}, #证件类型
					{'ID_CODE': {'dst': 'ID_CODE'}}, #证件号码
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}}, #证件有效期
					{'ID_ADDR': {'dst': 'ID_ADDR'}}, #证件地址
                    {'ID_TYPE2': {'dst': 'ID_TYPE2'}}, #辅助证件类型
                    {'ID_CODE2': {'dst': 'ID_CODE2'}}, #辅助证件号码
                    {'ID_EXP_DATE2': {'dst': 'ID_EXP_DATE2'}}, #辅助证件有效期
					{'ID_ADDR2': {'dst': 'ID_ADDR2'}}, #辅助证件地址
                    {'NEW_CUST_FNAME': {'dst': 'NEW_CUST_FNAME'}}, #新客户全称
					{'NEW_ID_TYPE': {'dst': 'NEW_ID_TYPE'}}, #新证件类型
                    {'NEW_ID_CODE': {'dst': 'NEW_ID_CODE'}}, #新证件号码
                    {'NEW_ID_TYPE2': {'dst': 'NEW_ID_TYPE2'}}, #新辅助证类型
                    {'NEW_ID_CODE2': {'dst': 'NEW_ID_CODE2'}}, #新辅助证件号码
					{'ACCT_TYPE': {'dst': 'ACCT_TYPE'}}, #证券账户类别
                    {'STKPBU': {'dst': 'STKPBU'}}, #交易单元
					{'ACCTBIZ_CLS': {'dst': 'ACCTBIZ_CLS'}}, #业务类别
                    {'ACCTBIZ_EXCODE': {'dst': 'ACCTBIZ_EXCODE'}}, #证券业务代码（外部）
                    {'BIRTHDAY': {'dst': 'BIRTHDAY'}}, #生日
                    {'SEX': {'dst': 'SEX'}}, #性别
					{'EDUCATION': {'dst': 'EDUCATION'}}, #学历
                    {'OCCU_TYPE': {'dst': 'OCCU_TYPE'}}, #职业
					{'CITIZENSHIP': {'dst': 'CITIZENSHIP'}}, #国籍
                    {'NATIONALITY': {'dst': 'NATIONALITY'}}, #民族
                    {'ORGANIZATION_CLS': {'dst': 'ORGANIZATION_CLS'}}, #机构类别
                    {'CAPITAL_ATTR': {'dst': 'CAPITAL_ATTR'}}, #资本属性
					{'NATIONAL_ATTR': {'dst': 'NATIONAL_ATTR'}}, #国有属性
                    {'ORG_SIMPLE_NAME': {'dst': 'ORG_SIMPLE_NAME'}}, #机构简称
					{'ORG_ENGLISH_NAME': {'dst': 'ORG_ENGLISH_NAME'}}, #机构英文名称
                    {'ORG_SITE': {'dst': 'ORG_SITE'}}, #机构网址
                    {'LEGAL_REP': {'dst': 'LEGAL_REP'}}, #法人代表
                    {'LEGAL_REP_ID_TYPE': {'dst': 'LEGAL_REP_ID_TYPE'}}, #法人代表证件类型
					{'LEGAL_REP_ID_CODE': {'dst': 'LEGAL_REP_ID_CODE'}}, #法人代表证件代码
                    {'LINKMAN': {'dst': 'LINKMAN'}}, #联系人姓名
					{'LINKMAN_ID_TYPE': {'dst': 'LINKMAN_ID_TYPE'}}, #联系人证件类型
                    {'LINKMAN_ID_CODE': {'dst': 'LINKMAN_ID_CODE'}}, #联系人证件号码
                    {'MOBILE_TEL': {'dst': 'MOBILE_TEL'}}, #移动电话
                    {'TEL': {'dst': 'TEL'}}, #联系电话
					{'FAX': {'dst': 'FAX'}}, #传真电话
                    {'ADDRESS': {'dst': 'ADDRESS'}}, #联系地址
                    {'ZIP_CODE': {'dst': 'ZIP_CODE'}}, #邮政编码
                    {'EMAIL': {'dst': 'EMAIL'}}, #电子邮箱
					{'NET_SERVICE': {'dst': 'NET_SERVICE'}}, #网络服务
                    {'NET_SERVICEPASS': {'dst': 'NET_SERVICEPASS'}}, #网络服务密码
					{'DEBT_BEAR_WAY': {'dst': 'DEBT_BEAR_WAY'}}, #合伙人承担责任方式
                    {'YMT_CODE_SHT': {'dst': 'YMT_CODE_SHT'}}, #新一账通账号
                    {'PROPER_CLS': {'dst': 'PROPER_CLS'}}, #适当性类别
                    {'SIGN_CLS': {'dst': 'SIGN_CLS'}}, #签约类别
                    {'SIGN_DATE': {'dst': 'SIGN_DATE'}}, #签约日期
					{'EFT_DATE': {'dst': 'EFT_DATE'}}, #生效日期
                    {'REMOVE_LMT_CLS': {'dst': 'REMOVE_LMT_CLS'}}, #解除限制类别
					{'FIR_ORG_NAME': {'dst': 'FIR_ORG_NAME'}}, #指定交易券商名称
                    {'ACCT_OPENTYPE': {'dst': 'ACCT_OPENTYPE'}}, #开户方式
                    {'USER_ROLE': {'dst': 'USER_ROLE', 'default': '1'}},
					{'LOGIN_CODE': {'dst': 'LOGIN_CODE'}}, #用户代码
                    {'USE_SCOPE': {'dst': 'USE_SCOPE', 'default': '0'}},
					{'OPERATION_TYPE': {'dst': 'OPERATION_TYPE'}}, #操作类型
                    {'AUTH_TYPE': {'dst': 'AUTH_TYPE', 'default': '0'}}
                ],
            'ans':
                [
                    {'SERIAL_NO': {'dst': 'SERIAL_NO'}}  # 流水号
                ]
        },
    '99000385':  #证券账户业务信息查询(新)
        {
            'dstfuncid': '99000385',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '99000385'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'orgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'SERIAL_NO': {'dst': 'SERIAL_NO'}}, #流水序号
                    {'SERIAL_NO_POS': {'dst': 'SERIAL_NO_POS'}}, #流水序号位置
					{'BGN_DATE': {'dst': 'BGN_DATE'}}, #开始日期
                    {'END_DATE': {'dst': 'END_DATE'}}, #结束日期
					{'ACCTBIZ_EXCODE': {'dst': 'ACCTBIZ_EXCODE'}}, #业务类型
                    {'ACCT_TYPE': {'dst': 'ACCT_TYPE'}}, #账户类型
                    {'TRDACCT': {'dst': 'TRDACCT'}}, #交易账户
                    {'ACCTBIZ_STATUS': {'dst': 'ACCTBIZ_STATUS'}}, #协议类型
					{'OPEN_DATE': {'dst': 'OPEN_DATE'}}, #回报日期
                    {'PRINT_FLAG': {'dst': 'PRINT_FLAG'}}, #打印标识
					{'INT_ORG': {'dst': 'INT_ORG'}}, #机构代码
                    {'ID_CODE': {'dst': 'ID_CODE'}}, #证件号码
                    {'CHK_STATUS': {'dst': 'CHK_STATUS'}}, #复核状态
                    {'USER_ROLE': {'dst': 'USER_ROLE', 'default': '1'}},
                    {'USE_SCOPE': {'dst': 'USE_SCOPE', 'default': '0'}},
					{'OPERATION_TYPE': {'dst': 'OPERATION_TYPE'}}, #操作类型
                    {'AUTH_TYPE': {'dst': 'AUTH_TYPE', 'default': '0'}}
                ],
            'ans':
                [
                    {'SERIAL_NO': {'dst': 'SERIAL_NO'}},  #流水序号
                    {'OCCUR_DATE': {'dst': 'OCCUR_DATE'}},  #发生日期
                    {'OCCUR_TIME': {'dst': 'OCCUR_TIME'}},  #发生时间
                    {'INT_ORG': {'dst': 'INT_ORG'}},  #内部机构代码
                    {'OPEN_DATE': {'dst': 'OPEN_DATE'}},  #开户日期
					{'BIZ_DATE': {'dst': 'BIZ_DATE'}},  #业务日期
                    {'STK_BIZ_CLS': {'dst': 'STK_BIZ_CLS'}},  #业务类别
                    {'ACCT_OPENTYPE': {'dst': 'ACCT_OPENTYPE'}},  #开户方式
                    {'YMT_CODE': {'dst': 'YMT_CODE'}},  #一码通账号
                    {'YMT_STATUS': {'dst': 'YMT_STATUS'}},  #一码通账号状态
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},  #客户代码
                    {'USER_TYPE': {'dst': 'USER_TYPE'}},  #客户类别
                    {'TRDACCT': {'dst': 'TRDACCT'}},  #证券账户
                    {'TRDACCT_EX': {'dst': 'TRDACCT_EX'}},  #配号证券账户
					{'CUST_FNAME': {'dst': 'CUST_FNAME'}},  #客户全称
                    {'CITIZENSHIP': {'dst': 'CITIZENSHIP'}},  #国籍
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},  #证件类型
                    {'ID_CODE': {'dst': 'ID_CODE'}},  #证件号码
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},  #证件有效日期
                    {'ID_ADDR': {'dst': 'ID_ADDR'}},  #证件地址
                    {'ID_TYPE2': {'dst': 'ID_TYPE2'}},  #辅助证件类型
                    {'ID_CODE2': {'dst': 'ID_CODE2'}},  #辅助证件号码
                    {'ID_EXP_DATE2': {'dst': 'ID_EXP_DATE2'}},  #辅助证件有效期
					{'ID_ADDR2': {'dst': 'ID_ADDR2'}},  #辅助证件地址
                    {'ACCT_TYPE': {'dst': 'ACCT_TYPE'}},  #证券账户类别
                    {'ACCT_STATUS': {'dst': 'ACCT_STATUS'}},  #证券账户状态
                    {'STKPBU': {'dst': 'STKPBU'}},  #交易单元
                    {'FIRMID': {'dst': 'FIRMID'}},  #清算编号
                    {'ACCTBIZ_AGTCODE': {'dst': 'ACCTBIZ_AGTCODE'}},  #代理网点编号
                    {'ACCTBIZ_AGTNAME': {'dst': 'ACCTBIZ_AGTNAME'}},  #代理网点名称
                    {'ACCTBIZ_ORGCODE': {'dst': 'ACCTBIZ_ORGCODE'}},  #代理结构编号
                    {'ACCTBIZ_CODE': {'dst': 'ACCTBIZ_CODE'}},  #业务代码（内部）
					{'ACCTBIZ_EXCODE': {'dst': 'ACCTBIZ_EXCODE'}},  #业务代码（外部）
                    {'ACCTBIZ_STATUS': {'dst': 'ACCTBIZ_STATUS'}},  #业务状态
                    {'ORIGINAL_SN': {'dst': 'ORIGINAL_SN'}},  #原流水序号
                    {'EXTERNAL_SN': {'dst': 'EXTERNAL_SN'}},  #外部流水序号
                    {'CHK_APP_SN': {'dst': 'CHK_APP_SN'}},  #复核编号
                    {'CHK_STATUS': {'dst': 'CHK_STATUS'}},  #复核状态
                    {'PRINT_FLAG': {'dst': 'PRINT_FLAG'}},  #打印状态
                    {'PRINT_DATE': {'dst': 'PRINT_DATE'}},  #打印时间
                    {'FRT_BIZ_SN': {'dst': 'FRT_BIZ_SN'}},  #任务流水号
					{'OP_USER': {'dst': 'OP_USER'}},  #操作员代码
                    {'OP_NAME': {'dst': 'OP_NAME'}},  #操作员姓名
                    {'OP_ROLE': {'dst': 'OP_ROLE'}},  #操作员角色
                    {'OP_ORG': {'dst': 'OP_ORG'}},  #操作员所属机构
                    {'CREDE_SCAN': {'dst': 'CREDE_SCAN'}},  #扫描标识
                    {'FEE_AMT_EX': {'dst': 'FEE_AMT_EX'}},  #登记公司收费金额
                    {'FEE_AMT': {'dst': 'FEE_AMT'}},  #登记公司收费金额
                    {'CLOSE_DATE': {'dst': 'CLOSE_DATE'}},  #销户日期
                    {'FIRST_TRD_DATE': {'dst': 'FIRST_TRD_DATE'}},  #首次交易日期
					{'YMT_CODE_SHT': {'dst': 'YMT_CODE_SHT'}},  #新一码通号
                    {'RTN_ERR_CODE': {'dst': 'RTN_ERR_CODE'}},  #返回信息代码
                    {'RETURN_MSG': {'dst': 'RETURN_MSG'}},  #返回信息
                    {'ACCTBIZ_CLS': {'dst': 'ACCTBIZ_CLS'}},  #业务类别
                    {'ACCTBIZ_ORGNAME': {'dst': 'ACCTBIZ_ORGNAME'}},  #
                ]
        },
    '99000427':  #证券账户业务信息拓展查询(新)
        {
            'dstfuncid': '99000427',
            'req':
                [
                    {'funcid#': {'dst': 'funcid', 'default': '99000427'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},# 从票据中取客户号
                    {'custorgid': {'dst': 'custorgid', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    # {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    # {'trdpwd':{'dst':'trdpwd','functionPlugIn':'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS,"kdencode","410301")'}},
                    {'trdpwd': {'dst': 'trdpwd'}},
                    {'netaddr': {'dst': 'netaddr'}},
                    {'orgid': {'dst': 'orgid'}},
                    {'operway#': {'dst': 'operway', 'default': 'operway', 'dict': general_dict}},
                    {'ext': {'dst': 'ext', 'default': '0'}},
                    {'custcert': {'dst': 'custcert'}},

                    {'SERIAL_NO': {'dst': 'SERIAL_NO'}}, #流水序号
                    {'LOGIN_CODE': {'dst': 'USER_CODE'}}, #用户代码
                    {'USER_ROLE': {'dst': 'USER_ROLE', 'default': '1'}},
                    {'USE_SCOPE': {'dst': 'USE_SCOPE', 'default': '0'}},
					{'OPERATION_TYPE': {'dst': 'OPERATION_TYPE'}}, #操作类型
                    {'AUTH_TYPE': {'dst': 'AUTH_TYPE', 'default': '0'}}
                ],
            'ans':
                [
                    {'SERIAL_NO': {'dst': 'SERIAL_NO'}},  #流水序号
                    {'OCCUR_DATE': {'dst': 'OCCUR_DATE'}},  #发生日期
                    {'OCCUR_TIME': {'dst': 'OCCUR_TIME'}},  #发生时间
                    {'INT_ORG': {'dst': 'INT_ORG'}},  #内部机构代码
                    {'OPEN_DATE': {'dst': 'OPEN_DATE'}},  #开户日期
					{'BIZ_DATE': {'dst': 'BIZ_DATE'}},  #业务日期
                    {'STK_BIZ_CLS': {'dst': 'STK_BIZ_CLS'}},  #业务类别
                    {'ACCT_OPENTYPE': {'dst': 'ACCT_OPENTYPE'}},  #开户方式
                    {'YMT_CODE': {'dst': 'YMT_CODE'}},  #一码通账号
                    {'YMT_STATUS': {'dst': 'YMT_STATUS'}},  #一码通账号状态
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},  #客户代码
                    {'USER_TYPE': {'dst': 'USER_TYPE'}},  #客户类别
                    {'TRDACCT': {'dst': 'TRDACCT'}},  #证券账户
                    {'TRDACCT_EX': {'dst': 'TRDACCT_EX'}},  #配号证券账户
					{'CUST_FNAME': {'dst': 'CUST_FNAME'}},  #客户全称
                    {'CITIZENSHIP': {'dst': 'CITIZENSHIP'}},  #国籍
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},  #证件类型
                    {'ID_CODE': {'dst': 'ID_CODE'}},  #证件号码
                    {'ID_EXP_DATE': {'dst': 'ID_EXP_DATE'}},  #证件有效日期
                    {'ID_ADDR': {'dst': 'ID_ADDR'}},  #证件地址
                    {'ID_TYPE2': {'dst': 'ID_TYPE2'}},  #辅助证件类型
                    {'ID_CODE2': {'dst': 'ID_CODE2'}},  #辅助证件号码
                    {'ID_EXP_DATE2': {'dst': 'ID_EXP_DATE2'}},  #辅助证件有效期
					{'ID_ADDR2': {'dst': 'ID_ADDR2'}},  #辅助证件地址
                    {'ACCT_TYPE': {'dst': 'ACCT_TYPE'}},  #证券账户类别
                    {'ACCT_STATUS': {'dst': 'ACCT_STATUS'}},  #证券账户状态
                    {'STKPBU': {'dst': 'STKPBU'}},  #交易单元
                    {'FIRMID': {'dst': 'FIRMID'}},  #清算编号
                    {'ACCTBIZ_AGTCODE': {'dst': 'ACCTBIZ_AGTCODE'}},  #代理网点编号
                    {'ACCTBIZ_AGTNAME': {'dst': 'ACCTBIZ_AGTNAME'}},  #代理网点名称
                    {'ACCTBIZ_ORGCODE': {'dst': 'ACCTBIZ_ORGCODE'}},  #代理结构编号
                    {'ACCTBIZ_CODE': {'dst': 'ACCTBIZ_CODE'}},  #业务代码（内部）
					{'ACCTBIZ_EXCODE': {'dst': 'ACCTBIZ_EXCODE'}},  #业务代码（外部）
                    {'ACCTBIZ_STATUS': {'dst': 'ACCTBIZ_STATUS'}},  #业务状态
                    {'ORIGINAL_SN': {'dst': 'ORIGINAL_SN'}},  #原流水序号
                    {'EXTERNAL_SN': {'dst': 'EXTERNAL_SN'}},  #外部流水序号
                    {'CHK_APP_SN': {'dst': 'CHK_APP_SN'}},  #复核编号
                    {'CHK_STATUS': {'dst': 'CHK_STATUS'}},  #复核状态
                    {'PRINT_FLAG': {'dst': 'PRINT_FLAG'}},  #打印状态
                    {'PRINT_DATE': {'dst': 'PRINT_DATE'}},  #打印时间
                    {'FRT_BIZ_SN': {'dst': 'FRT_BIZ_SN'}},  #任务流水号
					{'OP_USER': {'dst': 'OP_USER'}},  #操作员代码
                    {'OP_NAME': {'dst': 'OP_NAME'}},  #操作员姓名
                    {'OP_ROLE': {'dst': 'OP_ROLE'}},  #操作员角色
                    {'OP_ORG': {'dst': 'OP_ORG'}},  #操作员所属机构
                    {'CREDE_SCAN': {'dst': 'CREDE_SCAN'}},  #扫描标识
                    {'FEE_AMT_EX': {'dst': 'FEE_AMT_EX'}},  #登记公司收费金额
                    {'FEE_AMT': {'dst': 'FEE_AMT'}},  #登记公司收费金额
                    {'CLOSE_DATE': {'dst': 'CLOSE_DATE'}},  #销户日期
                    {'FIRST_TRD_DATE': {'dst': 'FIRST_TRD_DATE'}},  #首次交易日期
					{'YMT_CODE_SHT': {'dst': 'YMT_CODE_SHT'}},  #新一码通号
                    {'RTN_ERR_CODE': {'dst': 'RTN_ERR_CODE'}},  #返回信息代码
                    {'RETURN_MSG': {'dst': 'RETURN_MSG'}},  #返回信息
                    {'SIGN_DATE': {'dst': 'SIGN_DATE'}},  #签约日期
                    {'SIGN_CLS': {'dst': 'SIGN_CLS'}},  #签约类别
                ]
        },
    '260601':  # 日历信息获取(600046)
        {
            'dstfuncid': 'L1260601',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L1260601'}},
                    {'F_OP_USER#-1': {'dst': 'F_OP_USER','default': '8888'}},
                    {'F_OP_ROLE#-1': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE#-1#': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL#-1': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION#-1': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_RUNTIME#-1': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG'}},

                    {'BOOKSET': {'dst': 'BOOKSET'}},   #帐套编号
                    {'PHYSICAL_DATE': {'dst': 'PHYSICAL_DATE'}},    #日期
                    {'QUERY_FLAG#': {'dst': 'QUERY_FLAG', 'default': '1'}}, #查询标志    #调查表编码
                    {'DIFF_DAYS': {'dst': 'DIFF_DAYS'}}       #T+n日
                ],
            'ans':
                [
                    {'BOOKSET': {'dst': 'BOOKSET'}},  #帐套编号
                    {'PHYSICAL_DATE': {'dst': 'PHYSICAL_DATE'}},  #日期
                    {'DATE_FLAG': {'dst': 'DATE_FLAG'}},  #日期标志
                    {'DATE_STATUS': {'dst': 'DATE_STATUS'}} #日期状态
                ]
        },
        '600384':  #向中登发起创业板转签委托(600046)
        {
            'dstfuncid': '99000384',
            'req':
                [
                    {'F_FUNCTION#': {'dst': 'F_FUNCTION', 'default': '99000384'}},
                    {'F_OP_USER': {'dst': 'F_OP_USER'}},
                    {'F_OP_ROLE#': {'dst': 'F_OP_ROLE', 'default': '1'}},
                    {'F_OP_SITE': {'dst': 'F_OP_SITE'}},
                    {'F_CHANNEL#': {'dst': 'F_CHANNEL', 'default': 'e'}},

                    {'F_SESSION': {'dst': 'F_SESSION'}},  #
                    {'F_FUNCTION': {'dst': 'F_FUNCTION'}},
                    {'ACCT_TYPE#': {'dst': 'ACCT_TYPE', 'default': '21'}},
					{'F_REMOTE_OP_USER': {'dst': 'F_REMOTE_OP_USER'}},
                    {'F_REMOTE_OP_ORG': {'dst': 'F_REMOTE_OP_ORG'}},
					{'OPERATOR_TYPE#': {'dst': 'OPERATOR_TYPE', 'default': '0'}},

                    {'SESSION': {'dst': 'SESSION'}},

                    {'COMPANY_ID': {'dst': 'COMPANY_ID'}},
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},
					{'EFT_DATE': {'dst': 'EFT_DATE'}},
                    {'STKBD': {'dst': 'STKBD'}},
					{'TRDACCT': {'dst': 'TRDACCT'}},
                    {'SIGN_CLS': {'dst': 'SIGN_CLS'}},
                    {'ACCTBIZ_EXCODE#': {'dst': 'ACCTBIZ_EXCODE', 'default': '13'}},
                    {'CHK_STATUS': {'dst': 'CHK_STATUS', 'default': '2'}},
                    {'ID_TYPE': {'dst': 'ID_TYPE'}},
					{'ID_CODE': {'dst': 'ID_CODE'}},
                    {'SIGN_DATE': {'dst': 'SIGN_DATE'}},
                    {'FIRST_TRD_DATE': {'dst': 'FIRST_TRD_DATE'}},
					{'SIGN_PLACE': {'dst': 'SIGN_PLACE'}},
                    {'OPEN_TYPE': {'dst': 'OPEN_TYPE'}},

					{'OPEN_TYPE_WIN': {'dst': 'OPEN_TYPE_WIN'}},
                    {'OPERATION_TYPE#': {'dst': 'OPERATION_TYPE'}},
                    {'ORGID': {'dst': 'INT_ORG'}},
                    {'ACCT_OPENTYPE#': {'dst': 'ACCT_OPENTYPE', 'default': '0'}},
                    {'PROPER_CLS#': {'dst': 'PROPER_CLS', 'default': '1'}},
                    {'TRDACCT_EXCLS#': {'dst': 'TRDACCT_EXCLS', 'default': '0'}},
                    {'ACCTBIZ_CLS#': {'dst': 'ACCTBIZ_CLS', 'default': '01'}}
                ],
            'ans':
                [
                    {'SERIAL_NO': {'dst': 'SERIAL_NO'}}  # 流水号
                ]
        },
    '600261A':  # 设置协议报送状态(600046)
        {
            'dstfuncid': 'L1100123',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L1100123'}},
                    {'F_OP_USER': {'dst': 'F_OP_USER','default': '10416'}},
                    {'F_OP_ROLE': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_FUNCTION': {'dst': 'F_FUNCTION', 'default': '0'}},
                    {'F_RUNTIME': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_REMOTE_OP_ORG': {'dst': 'F_REMOTE_OP_ORG'}},
                    {'F_REMOTE_OP_USER': {'dst': 'F_REMOTE_OP_USER'}},

                    {'OPERATION_TYPE#': {'dst': 'OPERATION_TYPE', 'default': '3'}}, #操作类型(必传)
                    {'CUST_CODE': {'dst': 'CUST_CODE'}},   #客户代码(必传)"
                    {'CUST_AGMT_TYPE#': {'dst': 'CUST_AGMT_TYPE', 'default': '0A'}}, #协议类型(必传)   #日期
                    {'EFT_DATE': {'dst': 'EFT_DATE'}},
                    {'EXP_DATE': {'dst': 'EXP_DATE'}},
                    {'STKBD': {'dst': 'STKBD'}},
                    {'TRDACCT': {'dst': 'TRDACCT'}},
                    {'OPEN_TYPE': {'dst': 'OPEN_TYPE'}},
                    {'SIGN_PLACE': {'dst': 'SIGN_PLACE'}},
                    {'DEAL_STATUS#': {'dst': 'DEAL_STATUS', 'default': '1'}},
                    {'SIGN_DATE': {'dst': 'SIGN_DATE'}},
                    {'CUST_NAME': {'dst': 'CUST_NAME'}},
                    {'OP_REMARK': {'dst': 'OP_REMARK'}},
                    {'REDO_FLAG': {'dst': 'REDO_FLAG'}},
                    {'BATCHNO': {'dst': 'BATCHNO'}},
                    {'APPLY_DATE': {'dst': 'APPLY_DATE'}},
                    {'RETURN_MSG': {'dst': 'RETURN_MSG'}},
                    {'REMOTE_SYS': {'dst': 'REMOTE_SYS'}},
                    {'ACCT_TYPE': {'dst': 'ACCT_TYPE'}},
                    {'CUACCT_CODE': {'dst': 'CUACCT_CODE'}},
                    {'AGMT_OPER_FLAG': {'dst': 'AGMT_OPER_FLAG'}},
                    {'OPEN_FLAG': {'dst': 'OPEN_FLAG'}},
                    {'SETT_DEALLOG': {'dst': 'SETT_DEALLOG'}}
                ],
            'ans':
                [
                    {'SERIAL_NO#': {'dst': 'SERIAL_NO'}} #此业务只返回第一结果集
                ]
        },
    '600046A':  # 修改联系人信息(600046)
        {
            'dstfuncid': 'L1202018',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L1202018'}},
                    {'F_OP_USER': {'dst': 'F_OP_USER','default': '10416'}},
                    {'F_OP_ROLE': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_FUNCTION': {'dst': 'F_FUNCTION', 'default': '0'}},
                    {'F_RUNTIME': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_REMOTE_OP_ORG': {'dst': 'F_REMOTE_OP_ORG'}},
                    {'F_REMOTE_OP_USER': {'dst': 'F_REMOTE_OP_USER'}},

                    {'CUST_CODE': {'dst': 'CUST_CODE'}},   #客户代码(必传)"
                    {'TRD_PWD': {'dst': 'TRD_PWD'}}, #资金密码(必传)
                    {'BACKUP_NAME': {'dst': 'LINKMAN'}},    #备份联系人姓名
                    {'OCC_TYPE': {'dst': 'OCC_TYPE'}},
                    {'LINKMAN_ID_TYPE': {'dst': 'LINKMAN_ID_TYPE', 'default': '00'}},
                    {'LINKMAN_ID': {'dst': 'LINKMAN_ID'}},
                    {'OFFICE_TEL': {'dst': 'OFFICE_TEL'}},
                    {'BACKUP_M_TEL': {'dst': 'MOBILE_TEL'}}, #备份联系人电话
                    {'FAX': {'dst': 'FAX'}},
                    {'LINKMAN_EMAIL': {'dst': 'LINKMAN_EMAIL'}},
                    {'LINKMAN_ADDR': {'dst': 'LINKMAN_ADDR'}},
                    {'LINKMAN_ZIP': {'dst': 'LINKMAN_ZIP'}},
                    {'REMARK': {'dst': 'REMARK'}}
                ],
            'ans':
                [
                    {'SERIAL_NO#': {'dst': 'SERIAL_NO'}} #此业务只返回第一结果集
                ]
        },
    '99000261':  # 签署客户协议(99000261)
        {
            'dstfuncid': '99000261',
            'req':
                [
                    {'F_EXT_AUTHORIZEKEY': {'dst': 'F_EXT_AUTHORIZEKEY', }},
                    {'F_EXT_INST': {'dst': 'F_EXT_INST', }},
                    {'F_EXT_SESSIONIN': {'dst': 'F_EXT_SESSIONIN', }},
                    {'F_FUNCTION': {'dst': 'F_FUNCTION', 'default': '99000261'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG', }},
                    {'F_OP_PASSWORD': {'dst': 'F_OP_PASSWORD', }},
                    {'F_OP_ROLE': {'dst': 'F_OP_ROLE', }},
                    {'F_OP_TOKEN': {'dst': 'F_OP_TOKEN', }},
                    {'F_OP_USER': {'dst': 'F_OP_USER', }},
                    {'F_RUNTIME': {'dst': 'F_RUNTIME', }},
                    {'netaddr': {'dst': 'netaddr', }},
                    {'netaddr2': {'dst': 'netaddr2', }},
                    {'operway': {'dst': 'operway', 'default': 'e'}},

                    {'CUST_AGMT_TYPE': {'dst': 'CUST_AGMT_TYPE', 'default': '2C'}},
                    {'ACCOUNT': {'dst': 'CUST_CODE', }},
                    {'OPERATION_TYPE': {'dst': 'OPERATION_TYPE', 'default': '0'}},
                    {'STKBD': {'dst': 'STKBD', 'default': '10'}},
                    {'MARKET':{'dst':'market'}},
                    {'secuid_tmp':{'dst': 'TRDACCT','functionPlugIn':'functionPlugIn(WSGetSecuid,SESSION_JSON,MARKET)'}},  # 从票据股东代码
                    # {'TRDACCT': {'dst': 'TRDACCT', }},
                ],
            'ans':
                [
                    {'SERIAL_NO#': {'dst': 'SERIAL_NO'}} #此业务只返回第一结果集
                ]
        },
    '600063':  # 客户适当性信息查询(********)
        {
            'dstfuncid': '********',
            'req':
                [
                    {'F_EXT_AUTHORIZEKEY': {'dst': 'F_EXT_AUTHORIZEKEY', }},
                    {'F_EXT_INST': {'dst': 'F_EXT_INST', }},
                    {'F_EXT_SESSIONIN': {'dst': 'F_EXT_SESSIONIN', }},
                    {'F_FUNCTION': {'dst': 'F_FUNCTION', 'default': '********'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG', }},
                    {'F_OP_PASSWORD': {'dst': 'F_OP_PASSWORD', }},
                    {'F_OP_ROLE': {'dst': 'F_OP_ROLE', }},
                    {'F_OP_TOKEN': {'dst': 'F_OP_TOKEN', }},
                    {'F_OP_USER': {'dst': 'F_OP_USER', }},
                    {'F_RUNTIME': {'dst': 'F_RUNTIME', }},
                    {'netaddr': {'dst': 'netaddr', }},
                    {'netaddr2': {'dst': 'netaddr2', }},
                    {'operway': {'dst': 'operway', 'default': 'e'}},

                    {'ACCOUNT': {'dst': 'CUST_CODE', }},
                ],
            'ans':
                [
                    {'RATING_LVL': {'dst': 'RISK_TYPE', 'dict': HLZQ_RATING_LVL_TO_WECHAT_dict}}, #客户风险等级
                    {'INVEST_PRO': {'dst': 'CUSTOMER_RANGE', 'dict': HLZQ_INVEST_PRO_TO_WECHAT_dict}}, #客户投资偏好范围
                    {'INVEST_LMT': {'dst': 'CUSTOMER_TERM', 'dict': HLZQ_INVEST_LMT_TO_WECHAT_dict}}, #客户投资偏好期限
                    {'CUST_LASTRISK_FLAG': {'dst': 'CUST_LASTRISK_FLAG'}} #最低客户风险类别标示
                ]
        },
    '********':  # 客户电子协议签署(********)
        {
            'dstfuncid': '********',
            'req':
                [
                    {'F_EXT_AUTHORIZEKEY': {'dst': 'F_EXT_AUTHORIZEKEY', }},
                    {'F_EXT_INST': {'dst': 'F_EXT_INST', }},
                    {'F_EXT_SESSIONIN': {'dst': 'F_EXT_SESSIONIN', }},
                    {'F_FUNCTION': {'dst': 'F_FUNCTION', 'default': '********'}},
                    {'F_OP_ORG': {'dst': 'F_OP_ORG', }},
                    {'F_OP_PASSWORD': {'dst': 'F_OP_PASSWORD', }},
                    {'F_OP_ROLE': {'dst': 'F_OP_ROLE', }},
                    {'F_OP_TOKEN': {'dst': 'F_OP_TOKEN', }},
                    {'F_OP_USER': {'dst': 'F_OP_USER', }},
                    {'F_RUNTIME': {'dst': 'F_RUNTIME', }},
                    {'netaddr': {'dst': 'netaddr', }},
                    {'netaddr2': {'dst': 'netaddr2', }},
                    {'operway': {'dst': 'operway', 'default': 'e'}},

                    {'CONTRACT_CLS': {'dst': 'CONTRACT_CLS'}},
                    {'ACCOUNT': {'dst': 'CUST_CODE', }},
                    {'SIGN_ORG': {'dst': 'SIGN_ORG', 'default': '0'}},
                    {'CONTRACT_CLS_NAME': {'dst': 'CONTRACT_CLS_NAME'}},
                    {'CONTRACT_MD5':{'dst':'CONTRACT_MD5'}},
                    {'CONTRACT_TYPE':{'dst': 'CONTRACT_TYPE'}},
                    {'CONTRACT_TYPE_NAME': {'dst': 'CONTRACT_TYPE_NAME', }},
                    {'SERIAL_NO': {'dst': 'SERIAL_NO', }},
                    {'SIGN_TEXT': {'dst': 'SIGN_TEXT', }},
                    {'SIGN_VER': {'dst': 'VERSION', }}
                ],
            'ans':
                [
                    {'SERIAL_NO': {'dst': 'SIGN_CONTRACT'}}
                ]
        },
    'L2100217':  #首次交易日期查询(查询中登)
        {
            'dstfuncid': 'L2100217',
            'req':
                [
                    {'F_FUNCTION#-1': {'dst': 'F_FUNCTION','default': 'L2100217'}},
                    {'F_OP_USER': {'dst': 'F_OP_USER','default': '10416'}},
                    {'F_OP_ROLE': {'dst': 'F_OP_ROLE','default': '2'}},
                    {'F_OP_SITE': {'dst': 'F_OP_SITE', 'default': '0'}},
                    {'F_CHANNEL': {'dst': 'F_CHANNEL', 'default': 'e'}},
                    {'F_SESSION': {'dst': 'F_SESSION', 'default': '0'}},
                    {'F_FUNCTION': {'dst': 'F_FUNCTION', 'default': '0'}},
                    {'F_RUNTIME': {'dst': 'F_RUNTIME', 'default': '0'}},
                    {'F_REMOTE_OP_ORG': {'dst': 'F_REMOTE_OP_ORG'}},
                    {'F_REMOTE_OP_USER': {'dst': 'F_REMOTE_OP_USER'}},

                    {'ACCOUNT': {'dst': 'CUST_CODE', }},
                ],
            'ans':
                [
                    {'EARLY_TRD_DATE': {'dst': 'EARLY_TRD_DATE'}}  # 首次交易日
                ]
        },
    '800060':  # 查询TA账户信息
        {
            'dstfuncid': '337450',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no':{'dst':'branch_no','functionPlugIn':'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, #从票据中取机构号
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'TA_SN': {'dst': 'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                ],
            'ans':
                [
                    {'prodholder_status':{'dst':'STATUS'}}, # 产品账户状态
                    {'prodta_no':{'dst':'TA_SN'}}, # TA编号
                    {'secum_account':{'dst':'BALANCE_ACCOUNT'}}, # 证券理财账户

                ]
        },
    '800049':  # 开通TA账户
        {
            'dstfuncid': '337420',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 从票据中取机构号
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'TA_SN':{'dst':'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                ],
            'ans':
                [
                    {'secum_account':{'dst':'BALANCE_ACCOUNT'}},  # 相应TA编号对应的证券理财账户

                ]
        },
    '800063':  # 理财产品适当性匹配查询
        {
            'dstfuncid': '331261',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'BALANCE_CODE': {'dst': 'prod_code'}},  # 产品代码。
                    {'TA_SN': {'dst': 'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                ],
            'ans':
                [
                    {'instr_batch_no':{'dst':'INSTR_BATCH_NO'}},  # 指令批号
                    {'elig_deficitrate_flag':{'dst':'ELIG_DEFICITRATE_FLAG'}},  # 亏损率匹配标志 0-不匹配 1-匹配
                    {'elig_risk_flag':{'dst':'ELIG_RISK_FLAG'}},   # 风险匹配标志 0-不匹配 1-匹配
                    {'elig_investkind_flag':{'dst':'ELIG_INVESTKIND_FLAG'}},  # 投资品种匹配标志 0-不匹配 1-匹配
                    {'elig_term_flag':{'dst':'ELIG_TERM_FLAG'}},  # 投资周期匹配标志 0-不匹配 1-匹配
                    {'corp_risk_level':{'dst':'RISK_TYPE','functionPlugIn':'functionPlugIn(GjzqCustRiskLev,min_rank_flag,corp_risk_level)'}},# 客户风险等级
                    {'prodrisk_level':{'dst':'RISK_PRODUCT', 'dict': GJZQ_PROD_RISK_LEV_dict}},  # 产品风险等级

                ]
        },
    '800064':  # 签订适当性确认书留痕
        {
            'dstfuncid': '331279',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'INSTR_BATCH_NO': {'dst': 'instr_batch_no'}},  # 800063返回的指令批号。
                    {'OPER_INFO': {'dst': 'oper_info'}},  # 匹配文案信息
                ],
            'ans':
                [
                    {'serial_no':{'dst':'SERIAL_NO'}},  # 流水序号
                ]
        },
    '337400':  # 证券理财代码交易查询
        {
            'dstfuncid': '337400',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'BALANCE_CODE': {'dst': 'prod_code'}},  # 产品代码。
                    {'TA_SN': {'dst': 'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                ],
            'ans':
                [
                    {'ipo_begin_date':{'dst':'OCCUR_DATE'}},  # 购买日期&发生日期
                    {'prod_code':{'dst':'BALANCE_CODE'}},  # 产品代码
                    {'prod_name':{'dst':'BALANCE_NAME'}},  # 产品名称
                    {'prodcode_type':{'dst':'BALANCE_TYPE', 'dict': GJZQ_PROD_TYPE_dict}},  # 产品类型
                    {'prod_term':{'dst':'TRADE_DAYS'}},  # 产品期限
                    {'AUTO_RENEWAL':{'dst':'AUTO_RENEWAL', 'default': '0'}},  # 自动续做 0：不允许；1:允许；不存在续做的业务，返回0
                    {'prodpre_ratio':{'dst':'PURCHASE_RATE','functionPlugIn':'functionPlugIn(FormatPerson, prodpre_ratio)'}},
                    {'REPURCHASE_RATE':{'dst':'REPURCHASE_RATE', 'default': '0'}},  # 提前回购利率
                    {'min_perfapp_balance':{'dst':'PURCHASE_START_AMT', 'default': '0'}},  # 申购起点金额
                    {'REPURCHASE_START_AMT':{'dst':'REPURCHASE_START_AMT', 'default': '0'}},  # 提前回购利率
                    {'interest_begin_date':{'dst':'START_DATE', 'default': '0'}},  # 开始计息日期
                    {'END_DATE':{'dst':'END_DATE', 'default': '********'}},  # 提前回购利率
                    {'FINISH_DATE':{'dst':'FINISH_DATE', 'default': '********'}},  # 资金可取日
                ]
        },
    '337455':  # 证券理财代码库存查询
        {
            'dstfuncid': '337455',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'BALANCE_CODE': {'dst': 'prod_code'}},  # 产品代码。
                ],
            'ans':
                [
                    {'max_holder_num':{'dst':'QTY'}},  # 所剩产品的库存数
                    {'prod_code':{'dst':'BALANCE_CODE'}},  # 产品代码
                ]
        },
    '600171_332300':  # 持仓记录查询,合约查询接口
        {
            'dstfuncid': '332300',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    # 从票据取密码
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},

                    {'stock_code': {'dst': 'stock_code', 'default': STOCK_CODE}},
                    {'unfinished_type': {'dst': 'unfinished_type', 'default': 'E'}},
                    {'stock_type': {'dst': 'stock_type', 'default': 'Z'}},

                ],
            'ans':
                [
                    {'date_back': {'dst': 'date_back'}},  # 转入日期
                    {'entrust_no': {'dst': 'entrust_no'}},  # 委托编号

                    {'entrust_date': {'dst': 'entrust_date'}},  # 委托日期
                ]
        },
    '600171_332620':  # 持仓记录查询,合约查询接口
        {
            'dstfuncid': '332620',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'client_id': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},

                    # 从票据取密码
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},

                    {'stock_code': {'dst': 'stock_code', 'default': ''}},
                    {'query_type': {'dst': 'query_type', 'default': '0'}},
                    {'entrust_no': {'dst': 'entrust_no', }},
                    {'start_date': {'dst': 'start_date', }},
                    {'end_date': {'dst': 'end_date', }},
                    {'en_entrust_status': {'dst': 'en_entrust_status'}},
                    {'user_token': {'dst': 'user_token'}},
                ],
            'ans':
                [
                    {'init_date': {'dst': 'ORDER_DATE'}},  # 委托日期
                    {'entrust_balance': {'dst': 'entrust_balance'}},  # 委托金额
                    {'curr_time': {'dst': 'ORDER_TIME', 'functionPlugIn':'functionPlugIn(TimeFormat,curr_time)'}},  # 委托时间
                    {'branch_no': {'dst': 'BRANCH_CODE'}},  # 营业部代码
                    {'stock_code': {'dst': 'SECU_ACC'}},  # 股东代码
                    {'TRD_ID': {'dst': 'TRD_ID', 'default': ''}},  # 交易行为
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
                    {'stock_code': {'dst': 'BALANCE_CODE'}},  # 交易代码
                    {'stock_name': {'dst': 'BALANCE_NAME'}},  # 产品名称
                    {'sub_stock_type': {'dst': 'BALANCE_TYPE', 'default': '1'}},  # 理财产品类型
                    {'bond_term': {'dst': 'BALANCE_TIME_LIMIT', 'dict': dict_tx_balance_time_limit}},  # 理财产品期限
                    {'expire_year_rate': {'dst': 'PURCHASE_RATE',
                                          'functionPlugIn': 'functionPlugIn(WSRateTrans,expire_year_rate)'}},  # 购买年化利率
                    {'preend_year_rate': {'dst': 'REPURCHASE_RATE',
                                          'functionPlugIn': 'functionPlugIn(WSRateTrans,preend_year_rate)'}},  # 提前回购利率
                    {'bond_term': {'dst': 'TRADE_DAYS'}},  # 交易天数
                    {'real_end_date': {'dst': 'EXPIRE_DATE'}},  # 到期日期
                    {'PURCHASE_QTY': {'dst': 'PURCHASE_QTY',
                                      'functionPlugIn': 'functionPlugIn(getPurchaseNum332621,entrust_balance,back_balance)'}},
                    # 购买数量
                    {'PURCHASE_AMT': {'dst': 'PURCHASE_AMT',
                                      'functionPlugIn': 'functionPlugIn(getPurchaseAMT332621,entrust_balance,back_balance)'}},
                    # 购买金额
                    {'REPURCHSE_QTY': {'dst': 'REPURCHSE_QTY',
                                       'functionPlugIn': 'functionPlugIn(getRepurchaseNum332621,back_balance)'}},
                    # 购买数量
                    {'REPURCHASE_AMT': {'dst': 'REPURCHASE_AMT',
                                        'functionPlugIn': 'functionPlugIn(getRepurchaseAMT332621,back_balance)'}},
                    # 购买数量
                    {'FINISH_INCOME': {'dst': 'FINISH_INCOME',
                                       'functionPlugIn': 'functionPlugIn(getFinishIncome332621,entrust_balance,back_balance,profit)'}},
                    # 已到账收益
                    {'ESTIMATE_INCOME_DOWN': {'dst': 'ESTIMATE_INCOME_DOWN', 'default': ''}},  # 预估收益下限, 暂无用
                    {'ESTIMATE_INCOME_UP': {'dst': 'ESTIMATE_INCOME_UP', 'default': ''}},  # 预估收益上线, 暂无用
                    # 合同序列号
                    {'ORDER_ID': {'dst': 'ORDER_ID',
                                  'functionPlugIn': 'functionPlugIn(WSGetRedeemID332621,init_date,entrust_no)'}},

                    # 合同序列号
                    {'ORDER_FLAG': {'dst': 'ORDER_FLAG',
                                    'functionPlugIn': 'functionPlugIn(getOrderFlag332621,entrust_status)'}},  # 委托状态
                    {'fund_account': {'dst': 'ACCOUNT'}},  # 证券账户
                    {'entrust_date+serial_no': {'dst': 'REDEEM_ID',
                                                'functionPlugIn': 'functionPlugIn(WSGetRedeemID332621,init_date,entrust_no)'}},
                    # 券商审批单号
                    {'EXTENSION_FLAG': {'dst': 'EXTENSION_FLAG', 'default': '0'}},  #
                    {'HAS_EXTEND': {'dst': 'HAS_EXTEND', 'default': '0'}},  # 自动展期状态
                    {'position_str': {'dst': 'KEY_STR'}},
                    {'entrust_status': {'dst': 'entrust_status'}},
                    {'entrust_prop': {'dst': 'entrust_prop'}},
                    {'bankmsgid': {'dst': 'EXT_RET_CODE'}},
                    {'entrust_type': {'dst': 'entrust_type'}},
                    {'date_back': {'dst': 'date_back'}},
                    {'bankmsg': {'dst': 'EXT_RET_MSG'}},
                    {'errormsg': {'dst': 'EXT_RET_MSG'}},
                    {'ORIGIN_ID': {'dst': 'ORIGIN_ID', 'default': ''}},
                    {'EXT_SERIAL_NO': {'dst': 'EXT_SERIAL_NO', 'default': ''}},
                ]
        },
    '600171':  # 持仓记录查询,合约查询接口
        {
            'dstfuncid': '332621',
            'req':
                [
                    {'stock_code': {'dst': 'stock_code', 'default': STOCK_CODE}},
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    {'user_token': {'dst': 'user_token'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'COMPANY_ID':{'dst':'COMPANY_ID'}},
                    {'ORDER_DATE': {'dst': 'entrust_date'}},  # 资金帐号
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'KEY_STR': {'dst': 'position_str'}},
                    {'REC_COUNT': {'dst': 'request_num', 'default': '100'}},
                ],
            'ans':
                [
                    {'entrust_date':{'dst':'ORDER_DATE'}},  # 委托日期
                    {'ORDER_DATE':{'dst':'ORDER_TIME', 'default': '00:00:00'}},  # 委托时间
                    {'branch_no':{'dst':'BRANCH_CODE'}},  # 营业部代码
                    {'stock_code': {'dst': 'SECU_ACC'}},  # 股东代码
                    {'TRD_ID':{'dst':'TRD_ID', 'default': ''}},  # 交易行为
                    {'exchange_type': {'dst': 'MARKET', 'dict': dict_market_T2sdk_to_wzq}},  # 交易市场
                    {'stock_code':{'dst':'BALANCE_CODE'}},  # 交易代码
                    {'stock_name':{'dst':'BALANCE_NAME'}},  # 产品名称
                    {'sub_stock_type': {'dst': 'BALANCE_TYPE', 'default': '1' }},  # 理财产品类型
                    {'bond_term': {'dst': 'BALANCE_TIME_LIMIT', 'dict': dict_tx_balance_time_limit}},  # 理财产品期限
                    {'expire_year_rate': {'dst': 'PURCHASE_RATE',
                                          'functionPlugIn': 'functionPlugIn(WSRateTrans,expire_year_rate)'}},  # 购买年化利率
                    {'preend_year_rate': {'dst': 'REPURCHASE_RATE',
                                          'functionPlugIn': 'functionPlugIn(WSRateTrans,preend_year_rate)'}},  # 提前回购利率
                    {'compact_term': {'dst': 'TRADE_DAYS'}},  # 交易天数
                    {'date_back': {'dst': 'date_back'}},  # 转入日期
                    {'real_end_date': {'dst': 'EXPIRE_DATE'}},  # 到期日期
                    {'PURCHASE_QTY':{'dst':'PURCHASE_QTY', 'functionPlugIn': 'functionPlugIn(getPurchaseNum332621,entrust_balance,back_balance)'}},  # 购买数量
                    {'PURCHASE_AMT':{'dst':'PURCHASE_AMT', 'functionPlugIn': 'functionPlugIn(getPurchaseAMT332621,entrust_balance,back_balance)'}},  # 购买金额
                    {'REPURCHSE_QTY':{'dst':'REPURCHSE_QTY', 'functionPlugIn': 'functionPlugIn(getRepurchaseNum332621,back_balance)'}},  # 购买数量
                    {'REPURCHASE_AMT':{'dst':'REPURCHASE_AMT', 'functionPlugIn': 'functionPlugIn(getRepurchaseAMT332621,back_balance)'}},  # 购买数量
                    {'FINISH_INCOME':{'dst':'FINISH_INCOME', 'functionPlugIn': 'functionPlugIn(getFinishIncome332621,entrust_balance,back_balance,profit)'}},  # 已到账收益
                    {'ESTIMATE_INCOME_DOWN': {'dst': 'ESTIMATE_INCOME_DOWN', 'default': ''}},  # 预估收益下限, 暂无用
                    {'ESTIMATE_INCOME_UP': {'dst': 'ESTIMATE_INCOME_UP', 'default': ''}},  # 预估收益上线, 暂无用
                    {'ORDER_ID': {'dst': 'ORDER_ID', 'functionPlugIn': 'functionPlugIn(WSGetRedeemID332621,entrust_date,serial_no)'}},  # 合同序列号
                    {'ORDER_FLAG': {'dst': 'ORDER_FLAG', 'functionPlugIn': 'functionPlugIn(getOrderFlag332621,entrust_status)'}},  # 委托状态
                    {'fund_account':{'dst':'ACCOUNT'}},  # 证券账户
                    {'entrust_date+serial_no': {'dst': 'REDEEM_ID', 'functionPlugIn': 'functionPlugIn(WSGetRedeemID332621,entrust_date,serial_no)'}},  # 券商审批单号
                    {'EXTENSION_FLAG': {'dst': 'EXTENSION_FLAG', 'default': '0'}},  #
                    {'HAS_EXTEND': {'dst': 'HAS_EXTEND', 'default': '0'}},  #  自动展期状态
                    {'position_str':{'dst':'KEY_STR'}},
                    {'serial_no':{'dst':'serial_no'}},
                    {'bankmsgid':{'dst':'EXT_RET_CODE'}},
                    {'bankmsg':{'dst':'EXT_RET_MSG'}},
                    {'errormsg':{'dst':'EXT_RET_MSG'}},
                    {'ORIGIN_ID':{'dst':'ORIGIN_ID', 'default': ''}},
                    {'EXT_SERIAL_NO':{'dst':'EXT_SERIAL_NO', 'default': ''}},
                ]
        },
    '600173':  # 查询提前购回订单
        {
            'dstfuncid': '332620',
            'req':
                [
                    {'stock_code': {'dst': 'stock_code', 'default': ''}},
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    # 从票据中取机构号
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址

                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},


                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'COMPANY_ID':{'dst':'COMPANY_ID'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    # {'ORDER_DATE': {'dst': 'start_date', 'functionPlugIn': 'functionPlugIn(WSGetStartOrderDate,ORDER_DATE)'}},  # 委托日期。
                    # {'ORDER_DATE': {'dst': 'end_date', 'functionPlugIn': 'functionPlugIn(WSGetEndOrderDate,ORDER_DATE)'}},  # 委托日期。
                    {'ORDER_TYPE': {'dst': 'en_entrust_prop', 'dict': dict_order_type_to_hs_type}},  # 委托日期。
                    {'query_type': {'dst': 'query_type', 'functionPlugIn': 'functionPlugIn(WSGetQueryType,ORDER_DATE)'}},  # 委托日期。
                    {'KEY_STR': {'dst': 'position_str'}},
                    {'REC_COUNT': {'dst': 'request_num', 'default': '100'}}
                ],
            'ans':
                [
                    {'stock_code':{'dst':'BALANCE_CODE'}},  # 产品代码
                    {'stock_name':{'dst':'BALANCE_NAME'}},  # 产品名称
                    {'BALANCE_TYPE': {'dst': 'BALANCE_TYPE', 'default': '1'}},  # 理财产品类型
                    {'bond_term': {'dst': 'BALANCE_TIME_LIMIT', 'dict': dict_tx_balance_time_limit}},  # 理财产品期限
                    {'expire_year_rate': {'dst': 'PURCHASE_RATE', 'functionPlugIn': 'functionPlugIn(WSRateTrans,expire_year_rate)'}},  # 购买年化利率
                    {'preend_year_rate': {'dst': 'REPURCHASE_RATE', 'functionPlugIn': 'functionPlugIn(WSRateTrans,preend_year_rate)'}},  # 提前回购利率
                    {'init_date':{'dst':'ORDER_DATE'}},  # 委托日期
                    {'curr_time':{'dst':'ORDER_TIME','functionPlugIn':'functionPlugIn(TimeFormat,curr_time)'}},# 委托时间
                    {'entrust_balance':{'dst':'REPURCHASE_AMT'}},  # 回购金额
                    {'orig_entrust_date+join_report_no': {'dst': 'ORIGIN_ID', 'functionPlugIn': 'functionPlugIn(WSGetOriginSerialID,orig_entrust_date,join_report_no)'}},  # 主合约单号
                    {'OFFER_CONTRACT_NO': {'dst': 'OFFER_CONTRACT_NO', 'functionPlugIn': 'functionPlugIn(WSGetRedeemID,init_date,entrust_no)'}},  # 报盘合同号
                    {'init_date+entrust_no': {'dst': 'REDEEM_ID', 'functionPlugIn': 'functionPlugIn(WSGetRedeemID,init_date,entrust_no)'}},  # 券商审批单号
                    {'ORDER_TYPE': {'dst': 'ORDER_TYPE', 'functionPlugIn': 'functionPlugIn(WSGetOrderType,entrust_prop)'}},  # 委托类型
                    {'HAS_EXTEND': {'dst': 'HAS_EXTEND', 'functionPlugIn': 'functionPlugIn(WSHasExtend,entrust_prop)'}},  # 是否续期单
                    {'ORDER_FLAG': {'dst': 'ORDER_FLAG', 'functionPlugIn': 'functionPlugIn(getOrderFlag600173,entrust_status)'}},  # 委托状态
                    {'position_str':{'dst':'KEY_STR'}},  # 定位串
                    {'entrust_type':{'dst':'entrust_type'}},  # 定位串

                ]
        },
    '331407': #　基金电子合同签署
        {
            'dstfuncid': '331407',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    #{'custorgid': {'dst': 'fund_company', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}}, # 基金公司
                    {'BALANCE_CODE': {'dst': 'fund_code'}}, # 基金代码
                    {'TA_SN': {'dst': 'fund_company', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                    {'BALANCE_CODE': {'dst': 'prod_code'}}, # 产品代码
                    {'sub_risk_flag': {'dst': 'sub_risk_flag', 'default':'a'}}, # 委托金额
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                ],
            'ans':
                [
                    {'allot_no':{'dst':'ORDER_ID'}},  # 委托ID
                ]
        },
    '337411': #　新增理财委托接口
        {
            'dstfuncid': '337411',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'BALANCE_CODE': {'dst': 'prod_code'}}, # 产品代码
                    {'ORDER_AMT': {'dst': 'entrust_balance'}}, # 委托金额
                    #{'EXT_SERIAL_NO': {'dst': 'EXT_SERIAL_NO'}}, # 流水号
                    {'BALANCE_ACCOUNT': {'dst': 'secum_account'}}, # 证券理财账户
                    {'TA_SN': {'dst': 'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                ],
            'ans':
                [
                    {'allot_no':{'dst':'ORDER_ID'}},  # 委托ID
                ]
        },
    '331201': #　电子协议模板查询
        {
            'dstfuncid': '331201',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'BALANCE_CODE': {'dst': 'prod_code'}},  # 产品代码
                ],
            'ans':
                [
                    {'acct_exch_type':{'dst':'acct_exch_type'}},  # 账户交易类别
                    {'agree_model_no':{'dst':'agree_model_no'}},  # 模版编号
                    {'prodta_no':{'dst':'prodta_no'}},  # 产品TA编号
                    {'prod_code':{'dst':'prod_code'}},  # 产品代码
                ]
        },
    '330016':  # 查询下一个交易日
        {
            'dstfuncid': '330016',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构 Y
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式 Y
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址 Y

                    {'finance_type': {'dst': 'finance_type', 'default': ''}},  # 金融品种 N
                    {'exchange_type': {'dst': 'exchange_type', 'default': ''}},  # 交易类别 N
                    {'stage_num': {'dst': 'stage_num', 'default': ''}},  # 间隔天数 N
                    {'init_date': {'dst': 'init_date', 'default': ''}},  # 交易日期 N
                ],
            'ans':
                [
                    {'next_trade_date': {'dst': 'next_trade_date'}},  # 下一交易日
                ]
        },
    '339601':  # 历史报价回购查询
        {
            'dstfuncid': '339601',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址
                    # 从票据中取机构号
                    {'branch_no': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},

                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,passwd_tmp,USER_ID_CLS)'}},

                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'custid': {'dst': 'custid', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号

                    {'begin_date': {'dst': 'begin_date'}},
                    {'end_date': {'dst': 'end_date'}},
                    {'stock_code': {'dst': 'stock_code', 'default': STOCK_CODE}},
                ],
            'ans':
                [
                    {'init_date': {'dst': 'init_date'}},
                    {'entrust_no': {'dst': 'entrust_no'}},
                    {'entrust_way': {'dst': 'entrust_way'}},
                    {'curr_date': {'dst': 'curr_date'}},
                    {'curr_time': {'dst': 'curr_time'}},
                    {'branch_no': {'dst': 'branch_no'}},
                    {'client_id': {'dst': 'client_id'}},
                    {'fund_account': {'dst': 'fund_account'}},
                    {'exchange_type': {'dst': 'exchange_type'}},
                    {'stock_account': {'dst': 'stock_account'}},
                    {'stock_code': {'dst': 'stock_code'}},
                    {'entrust_type': {'dst': 'entrust_type'}},
                    {'entrust_prop': {'dst': 'entrust_prop'}},
                    {'entrust_bs': {'dst': 'entrust_bs'}},
                    {'expire_year_rate': {'dst': 'expire_year_rate'}},
                    {'preend_year_rate': {'dst': 'preend_year_rate'}},
                    {'entrust_status': {'dst': 'entrust_status'}},
                    {'report_time': {'dst': 'report_time'}},
                    {'position_str': {'dst': 'position_str'}},
                    {'stock_name': {'dst': 'stock_name'}},
                    {'postpone_flag': {'dst': 'postpone_flag'}},
                    {'date_back': {'dst': 'date_back'}},

                ]
        },
    '331203':  # 电子协议签署
        {
            'dstfuncid': '331203',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'passwd_tmp': {'dst': 'passwd_tmp', 'functionPlugIn': 'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}},
                    # 从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'TCC': {'dst': 'op_station', 'functionPlugIn': 'functionPlugIn(tranOpStation,TCC)'}},  # 站点地址

                    {'ACCOUNT': {'dst': 'sdc_account'}}, # 权益账号
                    {'acct_exch_type': {'dst': 'acct_exch_type', 'default':'Z01'}}, # 账户交易类别
                    {'prod_code': {'dst': 'prod_code', 'default': ''}}, # 产品代码
                    {'agree_type': {'dst': 'agree_type', 'default': '!'}},  # 协议类型
                    {'ACCOUNT': {'dst': 'trans_account'}}, # 交易账号
                    {'agency_no': {'dst': 'agency_no', 'default': ''}}, # 销售商代码
                    {'prodta_no': {'dst': 'prodta_no', 'default': ''}}, # 销售商代码
                    {'sub_risk_flag': {'dst': 'sub_risk_flag', 'default':'a'}}, # 是否签署风险揭示书
                    {'risk_level': {'dst': 'risk_level', 'default':''}}, # 风险级别
                    {'csfc_begin_date': {'dst': 'csfc_begin_date'}}, # 合同起始日期
                    {'csfc_end_date': {'dst': 'csfc_end_date'}}, # 合同终止日期
                    {'agree_model_no': {'dst': 'agree_model_no', 'default':'****************'}}, # 模版编号

                    {'remark': {'dst': 'remark'}}, # 备注
                ],
            'ans':
                [
                    {'serial_no':{'dst':'SIGN_CONTRACT'}},  # 签订合同号
                ]
        },
    '800160':  # 委托记录查询
        {
            'dstfuncid': '337452',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'BALANCE_CODE': {'dst': 'prod_code'}},  # 产品代码。
                    {'TA_SN': {'dst': 'prodta_no', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                    {'ORDER_ID': {'dst': 'allot_no'}},  # 委托id
                    {'ORDER_DATE': {'dst': 'entrust_date'}},  # 产品日期。
                    {'KEY_STR': {'dst': 'position_str'}},
                    {'REC_COUNT': {'dst': 'request_num', 'default': '100'}},
                ],
            'ans':
                [
                    {'entrust_date':{'dst':'ORDER_DATE'}},  # 委托日期
                    {'entrust_time':{'dst':'ORDER_TIME', 'default': '00:00:00'}},  # 委托时间
                    {'secum_account':{'dst':'BALANCE_ACCOUNT'}},  # 理财证券账号
                    {'prodta_no':{'dst':'TA_SN', 'dict': TC2GJZQ_xinkelicai_TA_dict}},  # TA编号
                    {'prod_code':{'dst':'BALANCE_CODE'}},  # 产品代码
                    {'prod_name':{'dst':'BALANCE_NAME'}},  # 产品编号
                    {'prodcode_type': {'dst': 'BALANCE_TYPE', 'dict': GJZQ_PROD_TYPE_dict}},  # 产品类型
                    {'PURCHASE_RATE':{'dst':'PURCHASE_RATE'}},  # 购买年化利率
                    {'REPURCHASE_RATE':{'dst':'REPURCHASE_RATE', 'default': '0.00'}},  # 提前回购利率
                    {'EXPIRE_DATE':{'dst':'EXPIRE_DATE'}},  # 到期日期
                    {'prod_term': {'dst': 'TRADE_DAYS'}},  # 产品期限
                    {'prod_end_date':{'dst':'EXPIRE_DATE'}},  # 到期日期
                    {'entrust_amount':{'dst':'PURCHASE_QTY'}},  # 购买数量
                    {'entrust_balance':{'dst':'PURCHASE_AMT'}},  # 购买金额
                    {'REPURCHSE_QTY':{'dst':'REPURCHSE_QTY', 'default': '0'}},  # 回购数量
                    {'REPURCHASE_AMT':{'dst':'REPURCHASE_AMT', 'default': '0'}},  # 回购金额
                    {'REPURCHASE_INCOME':{'dst':'REPURCHASE_INCOME', 'default': '0.00'}},  # 提前购回收益
                    {'FINISH_INCOME':{'dst':'FINISH_INCOME'}},  # 已到账收益
                    {'allot_no':{'dst':'ORDER_ID'}},  # 合同序号
                    {'entrust_status':{'dst':'ORDER_FLAG', 'dict': GJZQ2TC_xinkelicai_order_dict}},  # 委托状态
                    {'FINISH_INCOME':{'dst':'FINISH_INCOME'}},  # 已到账收益
                    {'fund_account':{'dst':'ACCOUNT'}},  # 证券账户
                    {'position_str':{'dst':'KEY_STR'}},  # 定位串

                ]
        },
    '600034':  # 查询风险股票提示信息
        {
            'dstfuncid': '333000',
            'req':
                [
                    {'op_branch_no': {'dst': 'op_branch_no', 'default': '5010'}},  # 操作分支机构
                    {'op_entrust_way#': {'dst': 'op_entrust_way', 'default': 'h'}},  # 操作方式
                    {'custid': {'dst': 'client_id', 'functionPlugIn': 'functionPlugIn(WSGetCustid,SESSION_JSON)'}},
                    {'custorgid': {'dst': 'branch_no', 'functionPlugIn': 'functionPlugIn(WSGetOrgid,SESSION_JSON)'}},
                    {'password_type': {'dst': 'password_type', 'default': '2'}},
                    {'passwd_tmp':{'dst':'passwd_tmp','functionPlugIn':'functionPlugIn(WSGetTrdpwd,SESSION_JSON)'}}, #从票据取密码
                    {'password': {'dst': 'password',
                                  'functionPlugIn': 'functionPlugIn(WSMultiEncodeByChannel,trdpwd,USER_ID_CLS)'}},
                    {'sysnode_id': {'dst': 'sysnode_id', 'default': '2'}},

                    {'ACCOUNT': {'dst': 'fund_account'}},  # 资金帐号
                    {'MARKET': {'dst': 'exchange_type', 'dict': dict_market_wzq_to_T2sdk}}, #市场
                    {'SECU_CODE': {'dst': 'stock_code'}},  # 证券代码
                ],
            'ans':
                [
                    {'notice_no':{'dst':'NOTICE_NO'}},  # 提示编号
                    {'notice_info':{'dst':'NOTICE_INFO', }},  # 提示信息
                ]
        },
}



# 空包返回字段的判断,如果以下有一个字段满足条件，则判断是空包
g_null_param_dict = {
    '600047': {'SERIAL_NO': ''},
    '600160': {'SECU_CODE': '', 'MARKET': '', 'SECU_NAME': '',"ACCOUNT": "", "ORDER_ID": "0"},
    '600161': {'SECU_CODE': '', 'MARKET': '', 'SECU_NAME': '',"ACCOUNT": "", "ORDER_ID": "0"},
    '600170': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': ''},
    '600172': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': ''},
    '600180': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': ''},
    '600190': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': ''},
    '600270': {"KEY_STR": "", "TRAN_DATE": "0", "EXT_INST": "", "TRD_DATE": "0"},
    '600320': {'SG_SECU_CODE':'', 'SECU_CODE':'', 'MARKET':''},
    '600330': {'ASSIGN_SN':'', 'ASSIGN_COUNT':'0', 'SECU_ACC':'', 'SECU_CODE':''},
    '600340': {'SECU_CODE':'','MARKET':'', 'SECU_NAME':''},
    '600380': {'MARKET':'', 'QYSL':'0', 'QYSL_KCB':'0'},
    '600390': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': '', 'TRD_BIZ_CODE': '0'},
    '339305': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': '', 'TRD_BIZ_CODE': ''},
    '600391': {'SECU_ACC': '', 'MARKET': '', 'SECU_NAME': '','TRD_BIZ_CODE':'0'},
    '337497': {'TA_SN': ''},
}
