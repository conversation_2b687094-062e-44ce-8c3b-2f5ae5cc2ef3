# -*- coding: utf-8 -*-
import datetime
import platform
import copy
import traceback
import pymysql
import re
import json
import time
import aiohttp
from decimal import Decimal, ROUND_HALF_UP

from tyrzconfig import g_TyrzDbDict
from adapttransdict import *
from kwl_py_log import *

import KwlEncrypt
import ctypes


DESCli = KwlEncrypt.CDes()
RSACli = KwlEncrypt.COpensslOperator()
AESCli = KwlEncrypt.CAes()

#DES 加密  入参原文 密钥
def des_encrypt(plain_text,des_key):
    return DESCli.DESEnCrypt(plain_text,des_key)

#DES 解密  入参原文 密钥
def des_decrypt(cipher_text,des_key):
    return DESCli.DESDeCrypt(cipher_text, des_key)

#SESSION 加密  入参原文 密钥
def session_encrypt(plain_text,des_key):
    return DESCli.SessionEnCrypt(plain_text,des_key)

#SESSION 解密  入参原文 密钥
def session_decrypt(cipher_text,des_key):
    return DESCli.SessionDeCrypt(cipher_text, des_key)

#md5摘要算法
def md5_encrypt(plain_text):
    return RSACli.MD5Encrypt(plain_text)

#md5验签 入参原文、签名
def md5_verify(plain_text,cipher_text):
    return md5_encrypt(plain_text) == cipher_text

# AES加密
def aes_Encrypt(plain_text, aes_key):
    return AESCli.aes_encrypt(plain_text, aes_key)

# AES解密
def aes_Decrypt(cipher_text, aes_key):
    return AESCli.aes_decrypt(cipher_text, aes_key)

cl_openssl = KwlEncrypt.COpensslOperator()
pattern = re.compile(r'functionPlugIn\((.+)\)') #从转换字典里面取funtion

def isWindowsSystem():
    return 'Windows' in platform.system()

def isLinuxSystem():
    return 'Linux' in platform.system()

if isLinuxSystem():
    from KWL_Nginx import *

#金证kdencode加密方式
if isLinuxSystem():
    g_KDEncodeDll = ctypes.cdll.LoadLibrary('libKDEncodeCliJNI.so')
else:
    g_KDEncodeDll = ctypes.cdll.LoadLibrary('KDEncodeCli.dll')

#get redispool by key,len need at least 3
def getRedisByKey(strkey,nums):
    return (ord(strkey[0]) + ord(strkey[-2]) + ord(strkey[-1]))%nums

def KDEncode(text,key):
    szDest = ctypes.create_string_buffer(1024)
    nret = g_KDEncodeDll.KDEncode(6,text.encode(),6,szDest,300,key.encode(),6);
    return str(szDest.value,'utf-8')


def functionPlugIn(mod,paraList):
    function = functionModDict.get(mod,None)
    strResult = ''
    if function is not None:
        strResult=function(paraList)
    return strResult

#适配器使用的函数---------------------------
#从票据中取指定key的值,票据传入的是字典
def WSGetValueFromSession(pamalist):
    jsonsession = pamalist[0]
    key = pamalist[1]
    if key in jsonsession['1'][0]:
        return jsonsession['1'][0][key]
    return ''


def GetToday332600(pamalist):
    return datetime.datetime.now().strftime('%Y%m%d')


def WSGetTrdpwd(pamalist):
    jsonsession = pamalist[0]
    if '6' in jsonsession['1'][0]:
        return jsonsession['1'][0]['6']
    return ''
def WSGetCustid(pamalist):
    jsonsession = pamalist[0]
    if '3' in jsonsession['1'][0]:
        return jsonsession['1'][0]['3']
    return ''

def WSGetFundid(pamalist):
    jsonsession = pamalist[0]
    if '4' in jsonsession['1'][0]:
        return jsonsession['1'][0]['4']
    return ''

def WSGetSecuid(pamalist):
    jsonsession = pamalist[0]
    market = pamalist[1]
    if '8' in jsonsession['1'][0]:
        for eachsecuid in jsonsession['1'][0]['8']:
            if market == eachsecuid['9']:
                return eachsecuid['a']
    return ''

def WSGetOrgid(pamalist):
    jsonsession = pamalist[0]
    if '5' in jsonsession['1'][0]:
        return jsonsession['1'][0]['5']
    return ''


def WSGetOriginSerialID(pamalist):
    orig_entrust_date = pamalist[0]
    join_report_no = pamalist[1]
    return orig_entrust_date + join_report_no


def WSGetRedeemID332621(pamalist):
    # entrust_date+serial_no
    entrust_date = pamalist[0]
    serial_no = pamalist[1]
    return entrust_date + serial_no


def WSGetRedeemID(pamalist):
    # init_date+entrust_no
    init_date = pamalist[0]
    entrust_no = pamalist[1]
    return init_date + entrust_no


def WSGetOrderType(pamalist):
    # init_date+entrust_no
    entrust_prop = pamalist[0]
    if entrust_prop == "QCA":
        return '0'
    elif entrust_prop == "QNE":
        return '1'
    return ''


def WSHasExtend(pamalist):
    # init_date+entrust_no
    entrust_prop = pamalist[0]
    if entrust_prop == "QNP":
        return '1'
    elif entrust_prop == "QNE":
        return '0'
    return ''

def WSGetOrderFlag(pamalist):
    # init_date+entrust_no
    entrust_status = pamalist[0]
    if entrust_status in ['0', '1']:
        return '0'
    elif entrust_status in ['2', '8', 'V']:
        return '1'
    return '2'


def WSGetStartOrderDate(pamalist):
    order_date = pamalist[0]
    # order_date = '20000223'
    if order_date == "":
        return datetime.datetime.now().strftime("%Y%m%d")
    return order_date

def WSGetEndOrderDate(pamalist):
    order_date = pamalist[0]
    #order_date = datetime.datetime.now().strftime("%Y-%m-%d")
    # order_date = '20230223'
    if order_date == "":
        return datetime.datetime.now().strftime("%Y%m%d")
    return order_date

def WSGetQueryType(pamalist):
    order_date = pamalist[0]
    if order_date != "":
        return "3"
    return "0"

def WSRateTrans(pamalist):
    rate_str = pamalist[0]
    try:
        rate = float(rate_str)
        rate = rate * 100
    except Exception as e:
        return ''
    return str((round(rate, 2)))


def ToFloat332600(pamalist):
    float_str = pamalist[0]
    float_val = float(float_str)
    return str(round(float_val, 2))


def TansEnableQuota332600(pamalist):
    enable_quota = pamalist[0]
    try:
        float_val = float(enable_quota)
    except Exception as e:
        return '1'
    if float_val == 0.00:
        return '1'
    return '0'


#二者非空参数
def SelectNoNULLPama(pamalist):
    value1 = pamalist[0]
    value2 = pamalist[1]
    if value1:
        return value1
    else:
        return value2

#插入字符串 InsertString(opertime,':','2#4')
def InsertString(pamalist):
    src = pamalist[0]
    insertstr = pamalist[1]
    postion = pamalist[2].split('#')
    listpositon = []
    for eachpostion in postion:
        if eachpostion.isdigit():
            listpositon.append(int(eachpostion))
    dst = ''
    npose = 0
    for eachpostion in listpositon:
        dst = dst + src[npose:eachpostion] + insertstr
        npose = eachpostion
    dst = dst + src[npose:]
    return dst

#插入字符并进行截取
def InsertAndDeleString(pamalist):
    #如果插入字符串后，不满足截取的最大长度，在前面补0
    #预防传入的时间为 95300 是9点53分00秒 转换为 95：30:0
    des_len = int(pamalist[4])
    src_len = len(pamalist[0])
    insert_len = len(pamalist[2].split('#'))
    if des_len > insert_len + src_len:
        pamalist[0] = pamalist[0].rjust(des_len - insert_len, '0')
    return InsertString(pamalist)[int(pamalist[3]):int(pamalist[4])]

#浮点精度控制
def FormatFloat(paraList):
    if paraList[0] == '':
        paraList[0] = 0
    strSrc = float(paraList[0])
    len = int(paraList[1])
    strRet = '%.*f' % (len,strSrc)
    return strRet

#除法
def DivFloat(paraList):
    if paraList[0] == '':
        paraList[0] = 0
    if paraList[1] == '':
        paraList[1] = 0
    dSrc = float(paraList[0])
    dDiv = float(paraList[1])
    if dSrc == 0 or dDiv < 0.001:
        return '0'
    dRes = dSrc/dDiv
    len = int(paraList[2])
    strRet = '%.*f' % (len,dRes)
    return strRet

#乘法
def MultiFloat(paraList):
    if paraList[0] == '':
        paraList[0] = 0
    if paraList[1] == '':
        paraList[1] = 0
    dSrc = float(paraList[0])
    dMult = float(paraList[1])

    dRes = dSrc * dMult
    len = int(paraList[2])
    strRet = '%.*f' % (len,dRes)
    return strRet

#加法
def AddFloat(paraList):
    if paraList[0] == '':
        paraList[0] = 0
    if paraList[1] == '':
        paraList[1] = 0
    dSrc = float(paraList[0])
    dMult = float(paraList[1])

    dRes = dSrc + dMult
    len = int(paraList[2])
    strRet = '%.*f' % (len,dRes)
    return strRet

#减法
def DecreaseFloat(paraList):
    if paraList[0] == '':
        paraList[0] = 0
    if paraList[1] == '':
        paraList[1] = 0
    dSrc = float(paraList[0])
    dMult = float(paraList[1])

    dRes = dSrc - dMult
    len = int(paraList[2])
    strRet = '%.*f' % (len,dRes)
    return strRet


#  从恒生332621接口获取购买数量
def getPurchaseNum332621(paraList):
    if not paraList[0]:
        return ''
    entrust_balance = float(paraList[0])
    back_balance = float(paraList[1])
    return str(int(round((entrust_balance - back_balance) / 100, 2)))

def getOrderId332602(paraList):
    date = datetime.datetime.now().strftime('%Y%m%d')
    return date + paraList[0]


#  从恒生332621接口获取购买金额
def getPurchaseAMT332621(paraList):
    if not paraList[0]:
        return ''
    entrust_balance = float(paraList[0])
    back_balance = float(paraList[1])
    return str((round(entrust_balance - back_balance, 2)))


#  从恒生332621接口获取预期到账收益
def getFinishIncome332621(paraList):
    if not paraList[0]:
        return ''
    entrust_balance = float(paraList[0])
    back_balance = float(paraList[1])
    profit = float(paraList[2])
    return str((round(entrust_balance - back_balance + profit, 2)))


# 从恒生332621接口获取合同序号 entrust_date+serial_no
def getOrderId332621(paraList):
    serial_no = paraList[0]
    return serial_no



def getOrderFlag332621(paraList):
    entrust_status = paraList[0]
    mapping = {
        '8': '1',
        'V': '1',
        'Z': '1',
        '0': '0',
        '1': '0',
        '2': '0',
        'C': '0'}
    return mapping.get(entrust_status, '3')

def getOrderFlag600173(paraList):
    """
    600173 ORDER_FLAG转换
    :param paraList:
    :return: str
    """
    entrust_status = paraList[0]
    mapping = {
        '8': '1',
        'V': '1',
        'Z': '1',
        '0': '0',
        '1': '0',
        '2': '0',
        'C': '0'}
    return mapping.get(entrust_status, '2')


#  从恒生332621接口获取购买数量
def getRepurchaseNum332621(paraList):
    if not paraList[0]:
        return ''
    back_balance = float(paraList[0])
    return str(int(back_balance / 100))


#  从恒生332621接口获取购买金额
def getRepurchaseAMT332621(paraList):
    if not paraList[0]:
        return ''
    back_balance = float(paraList[0])
    return str(round(back_balance, 2))



# 小数转百分比
def FormatPerson(paraList):
    if paraList[0]:
        dSrc = float(paraList[0])
        dSrc *= 100
        strRet = '%.*f' % (2, dSrc)
        return strRet

# 两数相乘
def Multiplication(paraList):
    if (len(paraList) < 2):
        return
    dAns = float(paraList[0]) * float(paraList[1])
    return '%.*f' % (2, dAns)

# 返回国金新客理财的风险等级
def GjzqCustRiskLev(paraList):
    if (len(paraList) < 2):
        return ''
    if paraList[0] == '1':
        return '5'
    else:
        return GJZQ_CUST_RISK_LEV_dict.get(paraList[1], '')


#根据渠道进行解密、然后安装柜台进行加密
#入参:密文,渠道,柜台加密方式,柜台加密key
def WSMultiEncodeByChannel(pamalist):
    InputText = pamalist[0]
    if not InputText:
        return ""

    try:
        FirstEncodeMethod = g_dict_channel_decode_way[pamalist[1]]
        pszFirstEncodeKey = g_dict_channel_decode_key[pamalist[1]]
    except Exception:
        raise Exception('没有配置该渠道解密方式:' + str(pamalist[1]))

    if FirstEncodeMethod == 'rsa_decrypt':
        #使用g_masterprivatekey私钥解
        try:
            InputText = cl_openssl.RSADecryptBCD(g_masterprivatekey, InputText)
        except:
            log_error("cl_openssl.RSADecryptBCD 解密失败 pamalist:{} error_msg:{}".format(pamalist, traceback.format_exc()))
        if InputText == '': #rsa解不出来的是新session里的密码，用des解密
            InputText = des_decrypt(pamalist[0], '410301_DesPwd')
        else:
            InputText = InputText[-6:]
    return InputText[0:6]

#RSA解密再二次加密,JD之类的加解密
#给trans调用的函数入参必须是一个list
def WSMultiEncode(pamalist):
    InputText = pamalist[0]
    FirstEncodeMethod = pamalist[1]
    pszFirstEncodeKey = pamalist[2]
    SecondEncodeMethod = pamalist[3]
    SecondEncodeKey = pamalist[4]
    if InputText == '':
        return ""
    if FirstEncodeMethod:
        if FirstEncodeMethod == 'rsa_decrypt':
            # bNeedKillearse = False
            # if len(InputText) == 256 + 60:
            #     InputText = InputText[60:]
            #     bNeedKillearse = True
            #使用g_masterprivatekey私钥解
            InputText = cl_openssl.RSADecryptBCD(g_masterprivatekey,InputText)
            # if bNeedKillearse and len(InputText) > 24:
            #     InputText = InputText[24:]
        elif FirstEncodeMethod == 'jd_des_decrypt':
            #使用京东方式解密
            InputText = KwlEncrypt.JD_DesDecrypt(InputText,pszFirstEncodeKey)
    if InputText == '':
        return ""
    if SecondEncodeMethod:
        if  SecondEncodeMethod == 'kdencode':
            return KDEncode(InputText,SecondEncodeKey)
        if SecondEncodeMethod == 'aes_encrypt':
            return KwlEncrypt.AESEncrypt(InputText, SecondEncodeKey)
    return InputText

#拼接任意个字符串
def JoinString(pamalist):
    tmp = ''
    for each in pamalist:
        tmp = tmp + each
    return tmp

def exchange_type_to_market(pamalist):
    """
    将柜台exchange_type字段转换成market
    :param pamalist: [holder_kind,exchange_type]
    :return:
    """
    # "2": "0",# 或4（当exchange为2时，同步判断holder_kind，holder_kind不为1，则对应0 - 深市，holder_kind为1，则对应4 - 深基金）
    # "1": "1",# 或5（当exchange为1时，同步判断holder_kind，holder_kind不为1，则对应1 - 沪市，holder_kind为1，则对应5 - 沪基金）
    holder_kind = pamalist[0]
    exchange_type = pamalist[1]
    result = market_dict[exchange_type]
    if exchange_type == "2" and holder_kind == "1":
        result = "4"
    elif exchange_type == "1" and holder_kind == "1":
        result = "5"
    return result

#截取字符串
def SubString(pamalist):
    return pamalist[0][int(pamalist[1]):int(pamalist[2])]

#并在0到9前面加0，由9XXX变成09XXXX 0->000000
def TimeFormat8To6(pamalist):
    if len(pamalist[0]) == 7:
        pamalist[0] = '0' + pamalist[0]
    if len(pamalist[0]) == 6:
        pamalist[0] = '00' + pamalist[0]
    if pamalist[0] == '0':
        pamalist[0] = '000000'
    return pamalist[0][int(pamalist[1]):int(pamalist[2])]

#获取日期
def GetDate(pamalist):
    return time.strftime(pamalist[0])

#是否负数
def IsNotPositiveNum(pamalist):
    if pamalist[0] and pamalist[0][0] == '-':
        return '1'
    else:
        return '0'

#去掉所有的该字符串
def KillString(pamalist):
    return pamalist[0].replace(pamalist[1],'')

#平安盈亏计算 盈亏=市值-持仓成本,当市值为0或者没有值时，盈亏返回0
#入参四个 市值,成本,市价,精度
def PingAnIncome(pamalist):
    if (not pamalist[0]) or (not  pamalist[1]) or (not pamalist[2]):
        return '0.00'
    if float(pamalist[0]) < 0.01 and float(pamalist[2]) < 0.001:
        return '0.00'
    return DecreaseFloat([pamalist[0],pamalist[1],pamalist[3]])

#时间格式化 时间加":"号191615 转 19:16:55
def TimeFormat(pamalist):
    if not pamalist[0]:
        return '00:00:00'
    strret = pamalist[0]
    if pamalist[0].find(':') != -1:
        return strret[0:8]
    else:
        while(len(strret) < 6):
            strret = '0' + strret
        return strret[0:2] + ':' + strret[2:4] + ':' + strret[4:6]

#时间格式化
# 时间格式化，参数要提供原始的格式和目标的格式Y年M月D日H时m分S秒
# 适用范围:源时间串的年月日等时间之间要是被隔开的,有时间连在一起的如(20150911和2015-0911)请用InsertString方法
# 示例DD-MM月-YYYY(考虑31-1月 -2015此类有空格的情况) 转 YYYYMMDD
#入参原串,原始格式,目标格式
def TimeFormatNew(pamalist):
    strTimeOrder = '' #时间顺序
    strSrcFormat = ''
    for eachchar in pamalist[1]:
        if eachchar not in ['D','M','m','H','S','Y']:
            strSrcFormat = strSrcFormat + eachchar
            continue
        else:
            if strTimeOrder.find(eachchar) == -1:
                strTimeOrder = strTimeOrder + eachchar
            strSrcFormat = strSrcFormat + '#'
    #原串
    strSrcTime = pamalist[0]
    vecspliteform = strSrcFormat.split('#')
    vecTimeValue = []  #时间的值
    nloop = 0
    while nloop < len(vecspliteform):
        if not vecspliteform[nloop]:
            nloop = nloop + 1
            continue
        pose = strSrcTime.find(vecspliteform[nloop])
        if  pose == -1:
            #找不到区分符号的时候，才考虑不合规的存在空格的状况
            strSrcTimetmp = ''
            for eachstr in strSrcTime:
                if eachstr != ' ':
                    strSrcTimetmp = strSrcTimetmp + eachstr
            strSrcTime = strSrcTimetmp
            pose = strSrcTime.find(vecspliteform[nloop])
            if pose == -1:
                #function:TimeFormatNew源时间串中未找到要解析的字符
                return pamalist[0]
            else:
                #去空格后能找到区分符号，回退一次,从新的原串中再取,已经找到过的就不作回退了
                pass
        else:
            #时间串中找到分隔符
            #print(vecspliteform)
            #print(pose)
            #print('原始时间1:%s'%(strSrcTime))
            vecTimeValue.append(strSrcTime[0:pose])
            strSrcTime = strSrcTime[pose+len(vecspliteform[nloop]):]
            #print('原始时间2:%s'%(strSrcTime))
            strSrcTime.replace(vecspliteform[nloop],'')
            nloop = nloop + 1
    if len(strSrcTime) != 0:
        vecTimeValue.append(strSrcTime)
    if len(strTimeOrder) != len(vecTimeValue):
        return pamalist[0]

    # DD-MM月-YY(考虑空格状况31-1月 -2015)
	# 取年月日时分秒
    strYear=strMonth=strDay=strHour=strMin=strSecond = ''
    nloop = 0
    for each in strTimeOrder:
        if each == 'Y':
            strYear = vecTimeValue[nloop]
            if len(strYear) == 2:
                strYear = '20' + strYear
        elif  each == 'M':
            strMonth = vecTimeValue[nloop]
            if len(strMonth) == 1:
                strMonth = '0' + strMonth
        elif  each == 'D':
            strDay = vecTimeValue[nloop]
            if len(strDay) == 1:
                strDay = '0' + strDay
        elif  each == 'H':
            strHour = vecTimeValue[nloop]
            if len(strHour) == 1:
                strHour = '0' + strHour
        elif  each == 'm':
            strMin = vecTimeValue[nloop]
            if len(strMin) == 1:
                strMin = '0' + strMin
        elif  each == 'S':
            strSecond = vecTimeValue[nloop]
            if len(strSecond) == 1:
                strSecond = '0' + strSecond
        nloop = nloop + 1
    #组成目标格式 比如YYYYMMDD,YYYY年MM月DD日HH:mm:SS
    strDestFormat =  pamalist[2]
    strDestFormat = strDestFormat.replace('YYYY',strYear)
    strDestFormat = strDestFormat.replace('MM',strMonth)
    strDestFormat = strDestFormat.replace('DD',strDay)
    strDestFormat = strDestFormat.replace('HH',strHour)
    strDestFormat = strDestFormat.replace('mm',strMin)
    strDestFormat = strDestFormat.replace('SS',strSecond)
    return strDestFormat











#平安配号个数计算 根据成交数量计算号码个数  上海1000股一个号，深圳500股一个号
#入参 市场,成交数量
def PingAssignNum(pamalist):
    if (not pamalist[0]) or (not pamalist[1]):
        return '0'
    if pamalist[0] == '00' or pamalist[0] == '01':
        return str(int(pamalist[1])/500)
    elif pamalist[0] == '10' or pamalist[0] == '11':
        return str(int(pamalist[1])/1000)
    else:
        return pamalist[1]

#平安委托的状态处理MidToUkd
def MidToUkd(pamalist):
    DCL_FLAG = pamalist[0]
    CAN_WITHDRAW = pamalist[1]
    IS_WITHDRAW = pamalist[2]
    IS_WITHDRAWN = pamalist[3]
    if not pamalist[4]:
        return '6'
    if not pamalist[5]:
        return '6'
    if not pamalist[6]:
        return '6'
    if not pamalist[7]:
        return '6'
    ret = '6'
    MATCHED_QTY = int(pamalist[4])
    ORD_QTY = int(pamalist[5])
    WITHDRAWN_QTY = int(pamalist[6])
    VALID_FLAG = int(pamalist[7])
    if DCL_FLAG == '0':
        ret = '0'   #未发送
    if DCL_FLAG == '1':
        if CAN_WITHDRAW == '0' and IS_WITHDRAW == '0' and IS_WITHDRAWN == '0' and MATCHED_QTY == ORD_QTY:
            ret = '3'  #全部成交
        elif CAN_WITHDRAW == '0' and IS_WITHDRAW == '0' and  IS_WITHDRAWN == '1':
            if WITHDRAWN_QTY == ORD_QTY:
                ret = '5' #全部撤单
            elif MATCHED_QTY>0 and ORD_QTY> 0:
                ret = '4' #部成部撤
            elif WITHDRAWN_QTY == 0 and MATCHED_QTY > 0:
                ret = '8' #部成待撤
            elif WITHDRAWN_QTY == 0 and MATCHED_QTY == 0:
                ret = '7' #已报待撤
        elif CAN_WITHDRAW == '1' and IS_WITHDRAW == '0' and IS_WITHDRAWN == '1' and ORD_QTY > MATCHED_QTY and MATCHED_QTY > 0:
            ret = '8' #部成待撤
        elif CAN_WITHDRAW == '1' and IS_WITHDRAW == '0' and IS_WITHDRAWN == '0' and ORD_QTY > MATCHED_QTY and MATCHED_QTY > 0:
            ret = '2' #部分成交
        elif CAN_WITHDRAW == '0' and IS_WITHDRAW == '1' and IS_WITHDRAWN == '1':
            ret = '6' #废单
        elif CAN_WITHDRAW == '1' and IS_WITHDRAW == '1' :
            ret = '6' #废单
        elif CAN_WITHDRAW == '0' and IS_WITHDRAW == '1' and IS_WITHDRAWN == '0':
            if MATCHED_QTY == 0:
                ret = '7' #已报待撤
            elif abs(MATCHED_QTY) == abs(ORD_QTY):
                ret = 3 #全部成交
            elif ORD_QTY > abs(MATCHED_QTY):
                ret = 2 #部分成交
        elif IS_WITHDRAWN == '1' and MATCHED_QTY == 0 and CAN_WITHDRAW == '0':
            #已撤单、成交数量为0,不允许撤单(允许撤单就是已发送了,已撤单，允许撤单表示撤单失败,如集合竞价的时候撤单)，标记为全部撤单
			#/*平安生产环境出现如下情况,金证人员判定为自动撤单(实际查日志发现成交查询里该合同号有-400的成交量,代表400全撤了，只是已撤单数量为0比较奇葩)
			#DCL_FLAG为1,已发送
			#CAN_WITHDRAW为1,允许撤单
			#IS_WITHDRAW为0,正常委托
			#IS_WITHDRAWN为1,已撤单
			#MATCHED_QTY成交数量为0
			#QTY数量为400
			#WITHDRAWN_QTY已撤单数量为0
            ret = '5' #全部撤单
        else:
            ret = '1'

    if VALID_FLAG == 0:
        ret = '6' #废单
    return ret

#从字符串中找子串
def FindTheSubStr(pamalist):
    text = pamalist[0]
    key = pamalist[1]
    if key in text:
        return "1"
    else:
        return "0"

# 华林证券 配号代码 转 申购代码
def ReplacePartStr(pamalist):
    print(pamalist)
    HLZQ_PHDM_TO_SGDM_dict = {
        "741": "730",  # 摇号中签
        "791": "780",  # 摇号中签
        "736": "732",  # 摇号中签
        "756": "754",  # 可转债资金申购
        "744": "733",  # 可转债资金申购
        "794": "783",  # 可转债资金申购
        #"741": "731",  # 老股东询价增发（比例中签）
        #"791": "781",  # 老股东询价增发（比例中签）
        "747": "737",  # 股票市值配售首发
        "797": "787"  # 股票市值配售首发
    }
    head = pamalist[0][:3]
    if head in HLZQ_PHDM_TO_SGDM_dict:
        newstr = HLZQ_PHDM_TO_SGDM_dict[head] + pamalist[0][3:]
        return newstr
    return pamalist[0]

#风险测评答案转换
def GetHlzqSurveyInfo(pamalist):
    #pamalist[0] = '1-55-1:B:2,2:D:5,3:B:2,4:A:0,5:D:0,6:B:2,7:C:3,8:A:1,9:A:0,10:A:1,11:C:5,12:B:4,13:D:5,14:A:0,15:B:2,16:A:7,17:B:7,18:A:5,19:A:1,20:B:3,21:C:0'
    #COLS = '1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|'
    #CELLS = '2|4|2|1|4|2|3|1|1|1|3|2|4|1|2|1|2|1|1|2|3|'
    COLS = ''
    CELLS = ''
    ans_dict = {'A':'1','B':'2','C':'3','D':'4','E':'5'}
    pamalist[0] =pamalist[0][pamalist[0].rfind('-')+1:]  #1:B:2,2:D:5,3:B:2,4:A:0,5:D:0,6:B:2,7:C:3,8:A:1,9:A:0,10:A:1,11:C:5,12:B:4,13:D:5,14:A:0,15:B:2,16:A:7,17:B:7,18:A:5,19:A:1,20:B:3,21:C:0

    for ans in pamalist[0].split(','):  #['1:B:2','2:D:5']
        list_ans = ans.split(':')    #['1','B','2']
        COLS = COLS + list_ans[0] + '|'
        if list_ans != [''] :
            if list_ans[1] in ans_dict:
                CELLS = CELLS + ans_dict[list_ans[1]] + '|'
    if pamalist[1] == '3':
        return COLS
    if pamalist[1] == '4':
        return CELLS
    return pamalist[0]

#风险测评答案转换(恒生柜台)
def GetGjzqSurveyInfo(pamalist):
    #pamalist[0] = '1-55-1:B:2,2:D:5,3:B:2,4:A:0,5:D:0,6:B:2,7:C:3,8:A:1,9:A:0,10:A:1,11:C:5,12:B:4,13:D:5,14:A:0,15:B:2,16:A:7,17:B:7,18:A:5,19:A:1,20:B:3,21:C:0'
    #ans = '886&4|887&2|888&1|889&4|890&4|891&3|892&1|893&|894&4|895&2|8020&3|896&1|897&4|898&1|899&1|900&1|901&1|'

    ans_dict = {'A':'1','B':'2','C':'3','D':'4','E':'5'}
    pamalist[0] =pamalist[0][pamalist[0].rfind('-')+1:]  #1:B:2,2:D:5,3:B:2,4:A:0,5:D:0,6:B:2,7:C:3,8:A:1,9:A:0,10:A:1,11:C:5,12:B:4,13:D:5,14:A:0,15:B:2,16:A:7,17:B:7,18:A:5,19:A:1,20:B:3,21:C:0

    ans = ''
    dict_tmp = {'1':'886','2':'887','3':"888",'4':'889','5':'890','6':'891','7':'892','8':'893','9':'894','10':'895','11':'8020','12':'896','13':'897',
                '14':'898','15':'899','16':'900','17':'901'}
    for row in pamalist[0].split(','):  #['1:AB:2','2:D:5']
        list_AB = row.split(':')  #['1','AB',2]
        ans_gj = ''
        if len(list_AB[1]) > 1:    #多选 AB --> 1^2
            for A in list_AB[1]:
                ans_gj = ans_gj + '^' + ans_dict[A]
            ans_gj = ans_gj[1:]
            ans = ans + dict_tmp[list_AB[0]] + '&' + ans_gj + '|'
        else:
            #单选  A --> 1  B --> 2
            ans = ans + dict_tmp[list_AB[0]] + '&' + ans_dict[list_AB[1]] + '|'
    return ans

#港股通汇率数值格式化，返回前面带0，保留4位小数的汇率
def FormatRate(pamalist):
    rate = pamalist[0]
    formatrate = '0'+rate[:5]
    return formatrate

#港股通额度金额格式化，'10500090000' 变成 138.50亿 保留2位小数
def Formatlimit(pamalist):
    rate = pamalist[0]
    temp = float(rate)/100000000.0
    formatrate = '%.2f' % (0.00 + temp)
    formatrate = str(formatrate)
    return formatrate

#港币转人民币
def HKDtoRMB(pamalist):
    matchamt = float(pamalist[0])
    rate = float(pamalist[1][:5])    #截取4位小数并转float
    matchamt = matchamt * rate
    matchamt = '%.2f' % matchamt
    return str(matchamt)

#人民币转港币
def RMBtoHKD(pamalist):
    price = float(pamalist[0])
    rate = float(pamalist[1][:5])   #截取4位小数并转float
    if rate != 0:
        price = price / rate
    price = str('%.3f' % price)
    return price

#去掉key
def filter_key(paraList):
    strSrc = paraList[0]
    index = strSrc.find(':')
    if index != -1:
        strSrc = strSrc[index+1:]
    return strSrc


#委托属性转换 TRD_ID -> entrust_prop
def tran_entrust_prop(paraList):
    if paraList[0] in ['0B', '0S']:
        return '0'

def format_sell_unit(paraList):
    sell_unit = paraList[0]
    if not sell_unit:
        return "0.00"
    return str(Decimal(sell_unit).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))

#从数据库查询branch_no
def query_branch_no(paraList):
    try:
        g_ConnTyrzDB = pymysql.connect(host=g_TyrzDbDict['host'],
                                       user=g_TyrzDbDict['user'],
                                       db=g_TyrzDbDict['db'],
                                       passwd=g_TyrzDbDict['passwd'],
                                       port=g_TyrzDbDict['port'],
                                       charset=g_TyrzDbDict['charset'],
                                       cursorclass=pymysql.cursors.DictCursor)
        with g_ConnTyrzDB.cursor(cursor=pymysql.cursors.DictCursor) as cur:
            strsql = """SELECT ORG_CODE FROM USER_IDENTITY WHERE USER_ID_INFO = '%s' AND STATUS = 1 LIMIT 1""" % paraList[0]
            cur.execute(strsql)
            data = cur.fetchone()
        return data['ORG_CODE']
    except Exception as e:
        strerr = traceback.format_exc()
        kwl_py_write_log(strerr, 'query_branch_no', ZTLOG_INFO, '')
        return ''


# op_station的拼接
# 留痕新增逻辑：tcc内容中开头存在MI或者MA则判断为新格式，其他情况下按照老的格式逻辑处理
def tranOpStation(paraList):
    tcc = paraList[0]
    if not tcc:
        return ''
    if tcc[0:2] == 'MA' or tcc[0:2] == 'MI':
        op_station = tcc
    else:
        op = tcc.split('|')
        if len(op) == 3:
            op_station = 'MPN:%s;MIP:%s;WXID:%s;CI:TX'%(op[1], op[2], op[0])
        elif len(op) == 2:
            op_station = 'MPN:%s;MIP:%s;WXID:%s;CI:TX' % (op[1], 'NA', op[0])
        else:
            op_station = tcc

    return op_station

#转换规则函数列表声明字典,放到适配器函数定义的最后
functionModDict ={
    'WSGetValueFromSession' : WSGetValueFromSession,
    'WSMultiEncode':WSMultiEncode,
    'WSMultiEncodeByChannel':WSMultiEncodeByChannel,
    'WSGetTrdpwd':WSGetTrdpwd,
    'WSGetCustid':WSGetCustid,
    'GetToday332600': GetToday332600,
    'WSGetOrderFlag': WSGetOrderFlag,
    'WSHasExtend': WSHasExtend,
    'WSGetOriginSerialID': WSGetOriginSerialID,
    'WSGetRedeemID332621': WSGetRedeemID332621,
    'WSGetRedeemID': WSGetRedeemID,
    'WSGetOrderType': WSGetOrderType,
    'WSGetStartOrderDate':WSGetStartOrderDate,
    'WSGetEndOrderDate':WSGetEndOrderDate,
    'WSGetQueryType':WSGetQueryType,
    'WSRateTrans': WSRateTrans,
    'TansEnableQuota332600': TansEnableQuota332600,
    'getOrderFlag332621': getOrderFlag332621,
    'getOrderFlag600173': getOrderFlag600173,
    'getOrderId332621': getOrderId332621,
    'getFinishIncome332621': getFinishIncome332621,
    'getPurchaseNum332621': getPurchaseNum332621,
    'getPurchaseAMT332621': getPurchaseAMT332621,
    'getRepurchaseNum332621': getRepurchaseNum332621,
    'getRepurchaseAMT332621': getRepurchaseAMT332621,
    'getOrderId332602': getOrderId332602,
    'ToFloat332600': ToFloat332600,
    'WSGetFundid':WSGetFundid,
    'WSGetSecuid':WSGetSecuid,
    'WSGetOrgid':WSGetOrgid,
    'SelectNoNULLPama':SelectNoNULLPama,
    'InsertString':InsertString,
    'FormatFloat':FormatFloat,
    'DivFloat':DivFloat,
    'MultiFloat':MultiFloat,
    'AddFloat':AddFloat,
    'DecreaseFloat':DecreaseFloat,
    'InsertAndDeleString':InsertAndDeleString,
    'JoinString':JoinString,
    'SubString':SubString,
    'TimeFormat8To6':TimeFormat8To6,
    'GetDate':GetDate,
    'IsNotPositiveNum':IsNotPositiveNum,
    'KillString':KillString,
    'PingAnIncome':PingAnIncome,
    'TimeFormat':TimeFormat,
    'PingAssignNum':PingAssignNum,
    'TimeFormatNew':TimeFormatNew,
    'MidToUkd':MidToUkd,
    'FindTheSubStr':FindTheSubStr,
    'ReplacePartStr':ReplacePartStr,
    'GetHlzqSurveyInfo':GetHlzqSurveyInfo,
    'GetGjzqSurveyInfo':GetGjzqSurveyInfo,
    'FormatRate':FormatRate,
    'Formatlimit':Formatlimit,
    'HKDtoRMB':HKDtoRMB,
    'RMBtoHKD':RMBtoHKD,
    'filter_key':filter_key,
    'tranOpStation':tranOpStation,
    'query_branch_no':query_branch_no,
    'tran_entrust_prop':tran_entrust_prop,
    'format_sell_unit': format_sell_unit,
    'FormatPerson':FormatPerson,
    'Multiplication':Multiplication,    # 两数相乘
    'GjzqCustRiskLev':GjzqCustRiskLev,    # 两数相乘
    'exchange_type_to_market': exchange_type_to_market,
}

#适配器使用的函数结束---------------------------
#生成XIDSESSION
def MakeXIDSessionString(strUserid):
    # 生成xid_session的过期时间，有效时间为当前时间开始的1个小时内为有效时间
    currenttime = time.time()
    expiretime = currenttime + 60*60 #过期时间为1个小时
    szTime = time.strftime('%Y%m%d%H%M%S', time.localtime(expiretime))

    # XIDSESSION的明文格式：USER_ID|TIME_
    strRetSession = strUserid + '|' + szTime

    # 加密XIDSESSION
    strKey = "88888888"  # XID_SESSION加密所使用的key
    szbuffer = KwlEncrypt.AESEncrypt(strRetSession, strKey)
    return szbuffer

#校验XIDSESSION
def CheckXIDSessionString(strUserid, strXidSession):
    # XID_SESSION加密所使用的key
    currenttime = time.time()
    szCurTime = time.strftime('%Y%m%d%H%M%S', time.localtime(currenttime))

    # 解密XIDSESSION，获取明文的XIDSESSION
    strKey = "88888888"  # XID_SESSION加密所使用的key
    szbuffer = KwlEncrypt.AESDecrypt(strXidSession, strKey)
    # print('aes_decrypt解密结果为')
    strRetSession = szbuffer

    strinfo = strRetSession.split('|')
    if len(strinfo) < 2:
        return '-11', 'XID_SESSION格式不合法'

    if szCurTime > strinfo[1]:
        return '-12', 'XID_SESSION已过期'

    if strUserid != strinfo[0]:
        return '-13', 'XID_SESSION不正确'

    return '0', 'XID_SESSION校验成功'


#测试解密
def testWSMultiEncode():
    print(WSMultiEncode('0000000000000000000000000000000000000000000000000000000000008209bcbff97c2d05de7d106ff9ce10e3cc90541c224d458d311a93894c389f84c5858601ef96dd56a50807980f15b254580ea2aa990ee960423ed35981f13d0453ede849e575ea25a9f50b0766224c435c70c00dd9f5051423240cc1073ee7821f211407af6a3c355c63585491616fc3978411500aa5a798f3f680d03db18868','rsa_decrypt','','',''))
#testWSMultiEncode()

#查找字符串第多少个出现的位置
def FindStrByShowNums(strSrc,strSub,Index):
    if Index == 0:
        return -1
    dstpose = 0
    while Index > 0:
        pose = strSrc.find(strSub)
        if pose == -1:
            return -1
        strSrc = strSrc[pose+1:]
        dstpose = dstpose + pose + 1
        Index = Index - 1
    return  dstpose


#适配器json入参转换,遇到函数,默认值,会修改原本的请求字典
# 入参:原功能号,请求字典
#出参 code 0 succ
#出参 msg  转换请求的错误消息
#出参dst_serviceid目标功能号
#列表出参dst_reqdict  转换后的请求字典
def translateReq(src_serviceid,reqDict,dst_reqdict):
    #the src_serviceid  neednt trans req
    #浅拷贝 不拷贝子对象 如果包含子对象则还是会互相改变 能用更好的办法?请求字典应该是普通一层json
    reqdict_tmp = reqDict.copy()
    if (src_serviceid not in functiondict) or ('req' not in functiondict[src_serviceid]):
        dst_reqdict.update(reqdict_tmp)
        return 0,'无需参数转换',src_serviceid
    #trans req by dict
    dst_serviceid = None
    #取功能号ID放入请求字典
    if 'dstfuncid' not in functiondict[src_serviceid]:
        return -3001,'入参转换未找到目标功能号，请检查' + src_serviceid  + '的转换字典',None
    else:
        dst_serviceid = functiondict[src_serviceid]['dstfuncid']
    #入参转换
    for eachreqkey in functiondict[src_serviceid]['req']:
        for key,value in eachreqkey.items():
            if 'functionPlugIn' in value:
                #函数插件处理---
                paraDict = {}
                paraStr = pattern.findall(value['functionPlugIn'])[0]
                paraList = []
                paraListTemp = paraStr.split(',')
                mod = paraListTemp[0]
                del paraListTemp[0]
                for paraKey in paraListTemp:
                    if paraKey[0] == '"' and paraKey[-1] == '"':
                        paraList.append(paraKey[1:-1])
                    elif paraKey in reqdict_tmp:
                        paraList.append(reqdict_tmp[paraKey])
                    else:
                        paraList.append("")
                functionResultValue= functionPlugIn(mod,paraList)
                if functionResultValue is not None:
                    if value['dst'] != '':
                        dst_reqdict[value['dst']] = functionResultValue
                    reqdict_tmp[key] = functionResultValue #change reqdict_tmp for dict trans
            else:
                if key in reqdict_tmp:
                    if reqdict_tmp[key] != '':
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = reqdict_tmp[key]
                    elif 'default'  in value:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = value['default']
                        reqdict_tmp[key] = value['default']   #change reqdict_tmp for dict trans
                    else:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = ''
                else:
                    if  'default'  in value:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = value['default']
                        reqdict_tmp[key] = value['default']   #change reqdict_tmp for dict trans
                    else:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = ''
                if 'dict' in value:
                    if key in reqdict_tmp and reqdict_tmp[key] in value['dict']:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = value['dict'][reqdict_tmp[key]]
    return 0,'入参转换成功' ,dst_serviceid

#适配器json数组应答参数转换 遇到函数,默认值,字典会修改原本的请求字典
#入参  原lbm,原请求字典,原应答字典数组,目标应答字典数组
#出参code,msg
#列表出参目标应答字典数组
def translateAns(src_serviceid,reqDict,src_ansDictList,dst_ansDictList):
    if not src_ansDictList:
        return 0, '无应答'
    if (src_serviceid not in functiondict) or ('ans' not in functiondict[src_serviceid]):
        dst_ansDictList.append(src_ansDictList)
        return 0,'无需应答参数转换'

    #出参转换，改为以配置文件为基准，配置文件未配置的参数不返回给前端
    for each_row in src_ansDictList:   #每条应答的处理
        dict_oneans = {}
        for eachansvalue in functiondict[src_serviceid]['ans']:
            for key,value in eachansvalue.items():
                if 'functionPlugIn' in value:
                    #函数插件处理---
                    paraDict = {}
                    paraStr = pattern.findall(value['functionPlugIn'])[0]
                    paraList = []
                    paraListTemp = paraStr.split(',')
                    mod = paraListTemp[0]
                    del paraListTemp[0]
                    for paraKey in paraListTemp:
                        if paraKey[0] == '"' and paraKey[-1] == '"':
                            paraList.append(paraKey[1:-1])
                        elif paraKey in each_row:
                            paraList.append(each_row[paraKey])
                        else:
                            paraList.append("")
                    functionResultValue= functionPlugIn(mod,paraList)
                    if functionResultValue is not None:
                        dict_oneans[value['dst']] = functionResultValue
                        each_row[key] = functionResultValue #change src_ansDictList for dict trans
                # else:
                if key in each_row:
                    if each_row[key] != '':
                        dict_oneans[value['dst']] = each_row[key]
                    elif  'default'  in value:
                        dict_oneans[value['dst']] = value['default']
                        each_row[key] = value['default']   #change src_ansDictList for dict trans
                    else:
                        dict_oneans[value['dst']] = ''
                else:
                    if  'default'  in value:
                        dict_oneans[value['dst']] = value['default']
                        each_row[key] = value['default']   #change src_ansDictList for dict trans
                    else:
                        dict_oneans[value['dst']] = ''
                if 'dict' in value:
                    if key in each_row and each_row[key] in value['dict']:
                        dict_oneans[value['dst']] = value['dict'][each_row[key]]
        if dict_oneans:
            dst_ansDictList.append(copy.deepcopy(dict_oneans))
        dict_oneans.clear()
    return 0,'应答参数转换成功'


#适配器json入参转换成U版mid需要格式,遇到函数,默认值,字典会修改原本的请求字典
# 入参:原功能号,请求字典
#出参 code 0 succ
#出参 msg  转换请求的错误消息
#出参dst_serviceid目标功能号
#出参dst_reqdict 转换后的请求字典(主要是用于取包头)
#出参dst_midreqbodylist  转换后的请求mid包体list
def translateReq_U(src_serviceid,reqDict,dst_reqdict):
    #the src_serviceid  neednt trans req
    reqdict_tmp = reqDict.copy()
    if (src_serviceid not in functiondict) or ('req' not in functiondict[src_serviceid]):
        dst_reqdict.update(reqdict_tmp)
        return -3001,'无需参数转换(U版不允许该行为)',src_serviceid,''
    #trans req by dict
    dst_serviceid = None
    dst_midreqbodylist = []
    #取功能号ID放入请求字典
    if 'dstfuncid' not in functiondict[src_serviceid]:
        return -3001,'入参转换未找到目标功能号，请检查' + src_serviceid  + '的转换字典',None,''
    else:
        dst_serviceid = functiondict[src_serviceid]['dstfuncid']
    #入参转换
    for eachreqkey in functiondict[src_serviceid]['req']:
        for key,value in eachreqkey.items():
            if 'functionPlugIn' in value:
                #函数插件处理---
                paraDict = {}
                paraStr = pattern.findall(value['functionPlugIn'])[0]
                paraList = []
                paraListTemp = paraStr.split(',')
                mod = paraListTemp[0]
                del paraListTemp[0]
                for paraKey in paraListTemp:
                    if paraKey[0] == '"' and paraKey[-1] == '"':
                        paraList.append(paraKey[1:-1])
                    elif paraKey in reqdict_tmp:
                        paraList.append(reqdict_tmp[paraKey])
                    else:
                        paraList.append("")
                functionResultValue= functionPlugIn(mod,paraList)
                if functionResultValue is not None:
                    if value['dst'] != '':
                        dst_reqdict[value['dst']] = functionResultValue
                    reqdict_tmp[key] = functionResultValue #change reqdict_tmp for dict trans
            else:
                if key in reqdict_tmp:
                    if reqdict_tmp[key] != '':
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = reqdict_tmp[key]
                    elif 'default'  in value:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = value['default']
                        reqdict_tmp[key] = value['default']   #change reqdict for dict trans
                    else:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = ''
                else:
                    if  'default'  in value:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = value['default']
                        reqdict_tmp[key] = value['default']   #change reqdict_tmp for dict trans
                    else:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = ''
                if 'dict' in value:
                    if key in reqdict_tmp and reqdict_tmp[key] in value['dict']:
                        if value['dst'] != '':
                            dst_reqdict[value['dst']] = value['dict'][reqdict_tmp[key]]
            if value['dst'] != '' and value['dst'].find('@HEAD') == -1:
                bodypama = {}
                bodypama['value'] = dst_reqdict[value['dst']]
                if 'remark' in value and value['remark'] == 'ENCRYPT_BLOWFISH':
                     bodypama['remark'] = 'ENCRYPT_BLOWFISH'
                dst_midreqbodylist.append(bodypama)
    return 0,'入参转换成功' ,dst_serviceid,dst_midreqbodylist





#自己组模拟定位串,把要组的记录全部丢srcanslist中即可
#入参数组,定位串，查询条数(不送或0则查全部)
#出参:带定位串的结果list
def makeKEY_STR(srcanslist,KEY_STR,REC_COUNT):
    if KEY_STR == '':
        KEY_STR = 0
    if REC_COUNT == '':
        REC_COUNT = 0
    KEY_STR = int(KEY_STR)
    REC_COUNT = int(REC_COUNT)
    ncurrentrows = 0 #当前遍历到的地方
    nrealrows = 0 #当前实际的列
    filterlist = []
    for eachrow in srcanslist:
        if ncurrentrows < KEY_STR:
            ncurrentrows = ncurrentrows + 1
            continue
        eachrow['KEY_STR'] = str(ncurrentrows + 1) #设置定位串,定位串从1开始,定位串用所有的记录位置
        filterlist.append(eachrow)
        nrealrows = nrealrows + 1
        ncurrentrows = ncurrentrows + 1
        if nrealrows >= REC_COUNT and REC_COUNT != 0:
            break
    return filterlist


#排序直接用 data_list.sort(key=lambda r: (r['softname'],r['version']), reverse=False)  reverse代表升序
#过滤和包含结果记录,冲突时优先包含
## listdict_filter条件是只要符合其中一个就过滤 or
# listdict_include条件是只要符合一个就包含  or
def Include_FilterRwos(srcanslist,listdict_include,listdict_filter):
    anslist = []
    for eachrow in srcanslist:
        #先除去,再包含
        bneed = True
        for eachdict in listdict_filter:
            for key,value in eachdict.items():
                if eachrow[key] == value:
                    bneed = False
                    break

        bneed1= False
        for eachdict in listdict_include:
            for key,value in eachdict.items():
                if eachrow[key] == value:
                    bneed1 = True
                    break
            if bneed1:
                break
        if len(listdict_include) == 0:
            bneed1 = True #没有写包含条件则认为都需要

        if bneed and bneed1:
            anslist.append(eachrow)
    return  anslist

#写本地日志用 todo中文问题
def WriteLocalLog(filename,msg):
    kwl_py_write_log(msg, 'WriteLocalLog', ZTLOG_INFO, msgid='')
    '''todaydate = time.strftime( '%Y-%m-%d',  time.localtime() )
     exceptlogfile=open("./logs/"  + filename + todaydate + ".txt",'a+')
     nowtime = time.strftime( '%Y-%m-%d %X', time.localtime() )
     exceptlogfile.write(nowtime + ':' + msg + '\n')
     exceptlogfile.flush()
     exceptlogfile.close()'''


# 过滤空数据
def judge_return_null(adaptserviceid, anscontol):
    if adaptserviceid not in g_null_param_dict.keys():
        return anscontol
    else:
        ans = []
        param_dict = g_null_param_dict[adaptserviceid]
        for row in anscontol:
            for key in param_dict.keys():
                if param_dict[key] != row[key]:
                    ans.append(row)
                    break

        return ans



async def http_post(request, msgid, json_type=1):
    """
    http post请求
    :param request:  必填 url payload
    :param msgid:
    :param json_type: 默认传json
    :return:
    """
    method_name = 'http_post'
    try:
        payload = request.get("payload", {})
        headers = request.get("headers", {})
        async with aiohttp.ClientSession(
            conn_timeout=request.get("conn_timeout", 6),
            read_timeout=request.get("read_timeout", 30)
        ) as session:
            payload_copy = copy.copy(payload)
            if isinstance(payload_copy, dict) and 'password' in payload_copy:
                payload_copy['password'] = '******'
            log_info('%s:发起请求:%s' % (request["url"], payload_copy), method_name, msgid)
            if json_type == 1:
                headers["Content-Type"] = "application/json"
                payload = json.dumps(payload)
            async with session.post(
                url=request["url"],
                headers=headers,
                data=payload
            ) as response:
                content = await response.text()
                log_info('%s:收到应答:%s' % (request["url"], content), method_name, msgid)
                return "0", "succ", content
    except aiohttp.ClientConnectionError as e:
        msg = "连接服务器{}异常: {}".format(request["url"], traceback.format_exc())
        log_error(msg, method_name, msgid)
        return "-1111", "连接服务器异常", ''
    except TimeoutError as e:
        msg = "调用服务器{}超时".format(request["url"])
        log_error(msg, method_name, msgid)
        return "-1111", "调用服务器超时", ''
    except Exception as e:
        msg = "调用服务器{}异常".format(request["url"], traceback.format_exc())
        log_error(msg, method_name, msgid)
        return "-1112", "调用服务器异常", ''


#入参 url、请求字符串
#出参三元组
async def httprequest_str( url,reqstr,is_post=True):
    try:
        async with aiohttp.ClientSession(conn_timeout=6,read_timeout=30) as session:
                try:
                    log_req = reqstr.copy()
                    if type(reqstr) is dict and 'password' in reqstr:
                        log_req['password'] = '******'
                    kwl_py_write_log('%s:发起请求:%s'%(url,log_req), 'httprequest_str', ZTLOG_INFO, msgid='')

                    if is_post:
                        if 'https' in url:
                            response =  await session.post(url=url,data=reqstr,verify_ssl=False)
                        else:
                            response =  await session.post(url=url,data=reqstr)
                    else:
                        if 'https' in url:
                            response = await session.get(url=url,verify_ssl=False)
                        else:
                            response = await session.get(url=url)
                    content = await response.text()
                    kwl_py_write_log('%s:收到应答:%s'%(url,content), 'httprequest_str', ZTLOG_INFO, msgid='')
                    # kwl_py_write_log('%s:收到应答:%s'%(url,content),'httprequest_str',1,msgid = '')
                    await response.release()
                    return '0','succ',content
                finally:
                    session.close()
                #return int(ansjson["ANSWERS"][0]['ANS_MSG_HDR']['MSG_CODE']),ansjson["ANSWERS"][0]['ANS_MSG_HDR']['MSG_TEXT'],ansjson["ANSWERS"][0]['ANS_COMM_DATA']
    except TimeoutError as e:
        kwl_py_write_log("调用服务器%s超时"%(url), 'httprequest_str', ZTLOG_ERROR, msgid='')
        return  '-1111',"调用服务器%s超时"%(url),''
    except Exception as e:
        kwl_py_write_log("调用服务器%s异常:%s"%(url,repr(e)), 'httprequest_str', ZTLOG_ERROR, msgid='')
        return '-1112',"调用服务器%s异常:%s"%(url,repr(e)),''



def sort_json(json_dict):
    if json_dict == {} or  json_dict == []:
        return ''
    strtmp = ''
    if isinstance(json_dict,dict):
        #字典则按键排序,键值遇到数组、字典、集合、数字、字符串均递归
        for each in sorted(json_dict.keys()):
            strtmp = strtmp + each + '=' + sort_json(json_dict[each]) + '&'
    elif isinstance(json_dict,list) or isinstance(json_dict,set):
        #数组、集合则直接遍历元素,遇数组、字典、集合、数字、字符串均递归
        for each in json_dict:
            strtmp = strtmp  + sort_json(each) + '&'
    else:
        #数字、字符串等非字典/数组/集合的终结点
        return str(json_dict)
    return strtmp[0:-1]


#请求签名  入参请求字典 签名KEY
def sign_json(req_dict, sign_key):
    req_dict['REQUESTS'][0]['REQ_MSG_HDR'].pop('SIGN','')
    str_sort = sort_json(req_dict['REQUESTS'][0]['REQ_MSG_HDR']) + '&' + sort_json(req_dict['REQUESTS'][0]['REQ_COMM_DATA'])  + '&key=' + sign_key
    a = md5_encrypt(str_sort)
    return md5_encrypt(str_sort)


#请求验签  入参请求字典 签名KEY
def check_sign(req_dict, sign_key):
    sign = req_dict['REQUESTS'][0]['REQ_MSG_HDR']['SIGN']
    return sign == sign_json(req_dict,sign_key)


#测试translate
if __name__ == "__main__":
    pwd = '848132b06b1d1900000000000000000000000000000000000000000000003D3EAF5E445B7591634B8EC249F1D894CAFBDAF605618A0B5C10CEB0E12F19A6E93FC7FB84759C05B4884B7A727B8167370E90D431978DC15130C35BE2C198031AB00A530AD268F0B68B6C6FCC50D6688E4E805A8240F3E6A87DED4596695EB4A531711AE2BEFAF074C9ECB2E111B0CCB00200A66E58D14F79A89615311B45EA'
    # 入参:密文,渠道,柜台加密方式,柜台加密key
    trd_pwd = WSMultiEncodeByChannel([pwd, '1', "kdencode", "410301"])
    print('微证券密码转柜台密码：', trd_pwd)

    pwd = '60ff742ae2ed1400000000000000000000000000000000000000000000001fe2762be065a0ae08b007f62fcabc388ee8d3cf93dcb8a0c00277b54991c9172524278bf0afa66e1b27bcbf326afff869aa77681fe5f14f6bf315b96abd64e9a93904118506f3acbb79c125ddb6b2de6a3de05a307a5c267fe03fff2c84cbe3111c49ce9ad6cb2d6ced5d8e2d4ef8ca9b50bb69113779cf4458742f131a8ebb'
    InputText = cl_openssl.RSADecryptBCD(g_masterprivatekey, pwd)
    print('微证券密码解密: ', InputText)

    des_pwd = des_encrypt('111111', '410301_DesPwd')
    print("des加密密码：", des_pwd)

    des_pwd = des_decrypt('356b6b76bea5154f', '410301_DesPwd')
    print("des解密密码1：", des_pwd)

    SecondEncodeMethod = 'kdencode'
    ss = KDEncode('680315', '89100028803')
    print('KDEncode_by_CUST_CODE：', ss)

    ss = KDEncode('680315', '410301')
    print('KDEncode_410301: ', ss)

    # print(WSGetTrdpwd('Qv4iosEOCUyG2LYOyRalykw3vk4j/xRQxUVMRrFIMh+tYxhndmxJn87Am5aMFETiLsnPlTQx4rVpuIiKCFC/wOY3lYlzqULz3PjElYyX8zgzkaNelp3a8yB5bSnySPGAH/wrNmlb10ZRK46J9r5Vcb7Y8CJxSVZp1CCfUA2boVvA0ZZtNHjoDsddDfiaJcQPJ6TLAbzBGIRUzw8C8Lc1JOR0SaIfMHwEeIuzs1sTN+DeF4+PC5UxxklzcsEuBLAU7duiCt9ZSugXYp5L759rqvPb9U81iGrEjmoUpo21+ML1hT/6VCd6zapfEtoC7/K5Y4H95yLxbPSmZlQZxWY7u+miDimmhS287FtdjKiDekl8hYv0KRSHu2RtYFoYEykEKisvVWvlSlHXILe3wQMjWFq3k9ldqQ8fLuTMHtwI6EhM98ypxgZ0ZnNvYD4eYfVzcmdCkQpp'))
    new_session = 'b4c052738a18b2eff6965373d078a8ed4e3aa95e5a4df4b6c904a45fe3080c4482c5380ce027057f49f136f3e41d1a758907bfbb1287c18d69cee9bf0a95199b839dbf1c00819dceb873892f0f37ff57843f572789b8535a8fa6c086067eaf5ec0c2ebb2306652a80b9057673b1310a67c40b9dbf8d1b4f6'
    ddd = session_decrypt(new_session, 'se410301')
    print('new_session解析', ddd)

    sess = '''{"0": "719039", "user_id": "os-33002400", "1": [{"5": "33", "4": "33002400", "3": "33002400", "2": "12900", "7": "2019-11-26 20:00:30", "8": [], "6": "356b6b76bea5154f"}]}'''
    session = session_encrypt(sess, '410301')
    print('生成session', session)

    print("session中取股东号", WSGetSecuid([json.loads(sess), '1']))
    print(json.loads(sess))

    pwd = '6ae730d02cf88e904a34bb70722eb7432d57253f39a5ee69b01f3cddd0ccfbde8adedbf185022d3efd9f5a92ccd9f5f8477d5d5348271227b91860f95939048244d6d6f3d2c9e42bb5d9eddd60fcd72df8a50a54cafd41503d897ae3669d3a610e3978f6c18dfb9652c3bf82210ae90b2eea137c236a0d38da4ac5329e077836'
    pwd = WSMultiEncode([pwd,"rsa_decrypt","","aes_encrypt",'10000000012'])
    print('rsa解密后aes加密 ',pwd)

    InputText = cl_openssl.RSADecryptBCD(g_masterprivatekey, 'a58118b221eff8000000000000000000000000000000000000000000000089B9FE3FFD87E6768A216B002ECF2C9B420ADC88E76D038DC8DDB486F0231C7B479CFB18A4930CA48055D28D5854F0DA2D6B76D92CA729F3F3A4924934CD1F4CB5DEB791A4CAB1F62C921ADCDBC7B859F892D3AA5E186075AE831B03216D04233F45F2DAB4452E561C2CD03C4479EFFFBC86415E28E4C4677E429B001F912A06')
    print("InputText",InputText)





