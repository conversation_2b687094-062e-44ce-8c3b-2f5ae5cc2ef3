
#这里配置功能号对应方法
g_kow_urlconfig = {
    'KOW009':{
        'url':'http://192.168.103.21:9090/kow/services/personCenter?wsdl',
        'method':'queryUserBaseInfo',
        'mod':'cxf'
    },
    '800014':{
        'url':'http://192.168.103.21:9090/kow/services/businessHandle?wsdl',
        'method':'tx800014',
        'mod':'cxf'
    },
    '800015':{
        'url':'http://192.168.103.21:9090/kow/services/businessHandle?wsdl',
        'method':'tx800015',
        'mod':'cxf'
    },
    'KOW003': {
        'url': 'http://192.168.103.21:9090/kow/services/routeService?wsdl',
        'method': 'openAcctApply',
        'mod': 'cxf'
    },
    '660730': {
        'url': 'http://180.169.107.62:9599/msg/send?sourceId=10&receiverId=8999&msgBody=',
        'method': 'sendSms',
        'mod': 'cxf'
    },
    'KOW001': {
        'url': 'http://192.168.103.21:9090/kow/services/routeService?wsdl',
        'method': 'accountBinding',
        'mod': 'cxf'
    },
    'KOW002': {
        'url': 'http://192.168.103.21:9090/kow/services/routeService?wsdl',
        'method': 'accountUnbinding',
        'mod': 'cxf'
    },
    '600052': {
        'url': 'http://192.168.103.21:9090/kow/services/businessHandle?wsdl',
        'method': 'tx600052',
        'mod': 'cxf'
    },
    '600026': {
        'url': 'http://192.168.103.21:9090/kow/services/businessHandle?wsdl',
        'method': 'tx600026',
        'mod': 'cxf'
    }
}